import { useLocales } from "@/locales";
import React, { useState, useCallback } from "react";

import { Box, Stack, Select, MenuItem, Typography, SelectChangeEvent } from "@mui/material";

import { MultipleLanguageElement } from "./types";
import CustomLocalizedTextField from "../custom-text-field/localized-text-field";

interface LocalizableInformationZoneProps {
    defaultTranslationLanguage: string;
    translationCategoryIds: MultipleLanguageElement[];
    eventId: string;
    setLocalizableInformationZoneLocale?: React.Dispatch<React.SetStateAction<string>>;
}

const LocalizableInformationZone = (props: LocalizableInformationZoneProps) => {
    const { 
        defaultTranslationLanguage, 
        translationCategoryIds, 
        eventId,
        setLocalizableInformationZoneLocale
    } = props;
    const { allLangs } = useLocales();
    const [ selectedTranslationLanguage, setSelectedTranslationLanguage ] = useState<string>(defaultTranslationLanguage);

    const onChange = useCallback((event: SelectChangeEvent) => {
        setSelectedTranslationLanguage(event.target.value);
        if (setLocalizableInformationZoneLocale) {
            setLocalizableInformationZoneLocale(event.target.value);
        }
    }, [setLocalizableInformationZoneLocale]);


    return (
        <Box>
            <Stack direction="row" spacing={1} justifyContent="space-between">
                <Typography variant="h4">Localizable Information</Typography>
                <Stack direction="row" justifyContent="flex-end" spacing={1}>
                    <Select 
                        variant="standard"
                        value={selectedTranslationLanguage}
                        disableUnderline 
                        sx={{
                            minWidth: '80px'
                        }}
                        onChange={onChange}
                    >
                        {allLangs.map(lang => (
                            <MenuItem 
                                key={`lang-${lang.value}`} 
                                value={lang.value}
                                sx={{
                                    paddingRight: '30px'
                                }}
                            >
                                {lang.label}
                            </MenuItem>
                        ))}
                    </Select>
                </Stack>
            </Stack>
            {translationCategoryIds.map(targetColum => 
                <CustomLocalizedTextField 
                    key={`textfield-${targetColum.translationCategoryId}`}
                    language={selectedTranslationLanguage} 
                    content={targetColum}
                    eventId={eventId}
                />)
            }
        </Box>
    )
};

export default React.memo(LocalizableInformationZone);