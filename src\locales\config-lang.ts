'use client';

import merge from 'lodash/merge';
import {
  enUS as enUSAdapter,
  zhH<PERSON> as zhHKAdapter
} from 'date-fns/locale';

// data-grid
import {
  enUS as enUSDataGrid
} from '@mui/x-data-grid';
// core
import {
  enUS as enUSCore,
  zhHK as zhHKCore
} from '@mui/material/locale';
// date-pickers
import {
  enUS as enUSDate,
  zhHK as zhHKDate
} from '@mui/x-date-pickers/locales';

// PLEASE REMOVE `LOCAL STORAGE` WHEN YOU CHANGE SETTINGS.
// ----------------------------------------------------------------------

export const allLangs = [
  {
    label: 'English',
    value: 'en-US',
    systemValue: merge(enUSDate, enUSDataGrid, enUSCore),
    adapterLocale: enUSAdapter,
    icon: 'flagpack:gb-nir',
  },
  {
    label: 'Chinese (HK)',
    value: 'zh-HK',
    systemValue: merge(zhHKDate, zhHKCore),
    adapterLocale: zhHKAdapter,
    icon: 'flagpack:hk',
  },
  {
    label: 'Indonesia',
    value: 'id-ID',
    systemValue: {},
    adapterLocale: { code: 'id-ID' },
    icon: 'flagpack:id',
  },
];

export const defaultLang = allLangs[0]; // English

// GET MORE COUNTRY FLAGS
// https://icon-sets.iconify.design/flagpack/
// https://www.dropbox.com/sh/nec1vwswr9lqbh9/AAB9ufC8iccxvtWi3rzZvndLa?dl=0
