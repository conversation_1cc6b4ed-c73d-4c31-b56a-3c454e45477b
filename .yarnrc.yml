compressionLevel: mixed

enableGlobalCache: false

httpTimeout: 600000

npmScopes:
  stoneleigh:
    npmAlwaysAuth: true
    npmPublishRegistry: "https://stoneleighsolutions-com-860716011834.d.codeartifact.ap-southeast-1.amazonaws.com/npm/stoneleigh-private-npm/"
    npmRegistryServer: "https://stoneleighsolutions-com-860716011834.d.codeartifact.ap-southeast-1.amazonaws.com/npm/stoneleigh-private-npm/"

plugins:
  - path: .yarn/plugins/plugin-envs.cjs
    spec: "https://raw.githubusercontent.com/Ayc0/yarn-plugin-envs/v0.0.3/src/index.js"
  - path: .yarn/plugins/@yarnpkg/plugin-aws-codeartifact.cjs
    spec: "https://raw.githubusercontent.com/mhassan1/yarn-plugin-aws-codeartifact/v0.12.0/bundles/@yarnpkg/plugin-aws-codeartifact.js"

yarnPath: .yarn/releases/yarn-4.0.2.cjs
