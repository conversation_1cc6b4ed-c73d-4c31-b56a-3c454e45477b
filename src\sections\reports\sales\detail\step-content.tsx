import { Dayjs } from 'dayjs';
import Stepper from '@/components/stepper';
import TStep from '@/components/stepper/type';
import { useSnackbar } from '@/components/snackbar';
import useGetEvents from '@/hooks/api/use-get-events';
import { useMemo, useState, useCallback } from 'react';

import Select, { SelectChangeEvent } from '@mui/material/Select';
import {
    Paper,
    Button,
    MenuItem
} from '@mui/material';

import DateRangePicker from 'src/components/date-range-picker';

import ExportButton from './export-button';

const StepContentSalesReport = () => {
    const { enqueueSnackbar } = useSnackbar();
    const events = useGetEvents(1, 9999);
    const [eventId, setEventId] = useState('');
    const [pickedDate, setPickedDate] = useState<{ firstDay?: Dayjs | null, lastDay?: Dayjs | null }>();
    const [activeStep, setActiveStep] = useState<number>(0);

    const onEventInputChange = useCallback((e: SelectChangeEvent) => {
        setEventId(e.target.value as string);
    }, []);

    const onChangeDateRangePicker = useCallback(({ firstDay, lastDay }: { firstDay: Dayjs, lastDay: Dayjs }) => {
        setPickedDate({ firstDay, lastDay });
    }, []);

    const IsValidStep = useCallback((step: number) => {
        switch (step) {
            case 0:
                if (eventId.trim().length === 0) {
                    enqueueSnackbar("Missing event id", { variant: "error" });
                    return false;
                }
                break;
            case 1:
                if (!pickedDate?.firstDay && !pickedDate?.lastDay) {
                    return false;
                }
                break;
            default:
                break;
        }
        return true;
    }, [eventId, enqueueSnackbar, pickedDate?.firstDay, pickedDate?.lastDay]);
    const CancelStep = useCallback((step: number) => {
        switch (step) {
            case 1:
                setPickedDate({ firstDay: null, lastDay: null });
                break;
            default:
                break;
        }
    }, []);
    const handleStepperNext = useCallback(() => {
        if (!IsValidStep(activeStep)) {
            return;
        }
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }, [IsValidStep, activeStep]);

    const handleStepperBack = useCallback(() => {
        CancelStep(activeStep - 1);
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    }, [CancelStep, activeStep]);

    const handleStepperReset = useCallback(() => {
        setEventId('');
        setPickedDate({ firstDay: null, lastDay: null });
        setActiveStep(0);
    }, []);

    const steps: TStep[] = useMemo(() => events ? [
        {
            label: 'Select event',
            description: `Select an event that you would like to have a report`,
            component:
                <Select
                    labelId="event-selector"
                    id="report-event-selector"
                    onChange={onEventInputChange}
                    value={eventId}
                    displayEmpty
                    MenuProps={{
                        anchorOrigin: {
                            vertical: "bottom",
                            horizontal: "right"
                        },
                    }}
                >
                    <MenuItem disabled value="">
                        <em>Please Select Event</em>
                    </MenuItem>

                    <MenuItem value="all">All events</MenuItem>
                    {events?.list?.map((event: any) => (
                        <MenuItem key={event.eventId} value={event.eventId}>{event.eventName}</MenuItem>
                    ))}
                </Select>,
        },
        {
            label: 'Pick Date',
            description: `Select a date or range of date`,
            component: <DateRangePicker onChange={onChangeDateRangePicker} hiddenTextField alwaysDisplay />
        },
    ] : [], [eventId, events, onChangeDateRangePicker, onEventInputChange]);

    return (
        <>
            <Stepper activeStep={activeStep} steps={steps} onNext={handleStepperNext} onBack={handleStepperBack} />
            {activeStep === steps.length && (
                <Paper square elevation={0} sx={{ p: 3 }}>
                    <Button
                        variant='outlined'
                        color='warning'
                        onClick={handleStepperBack}
                        sx={{ mr: '2rem' }}
                    >
                        Back
                    </Button>
                    <ExportButton event={eventId} firstDay={pickedDate?.firstDay} lastDay={pickedDate?.lastDay} onFinished={handleStepperReset} />
                </Paper>
            )}
        </>
    )
}

export default StepContentSalesReport;
