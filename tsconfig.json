{"compilerOptions": {"baseUrl": ".", "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "strict": true, "noEmit": true, "allowJs": true, "jsx": "preserve", "module": "esnext", "incremental": true, "skipLibCheck": true, "noImplicitAny": true, "noImplicitThis": true, "esModuleInterop": true, "isolatedModules": true, "strictNullChecks": true, "resolveJsonModule": true, "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "useUnknownInCatchVariables": false, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "plugins": [{"name": "next"}], "typeRoots": ["./src/types"], "paths": {"@/*": ["src/*"], "@/assets/*": ["src/assets/*"], "@/components/*": ["src/components/*"], "@/constants/*": ["src/constants/*"], "@/enum/*": ["src/enum/*"], "@/hooks/*": ["src/hooks/*"], "@/layouts/*": ["src/layouts/*"], "@/models/*": ["src/models/*"], "@/routes/*": ["src/routes/*"], "@/utils/*": ["src/utils/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/components/datatable/index.tsxx"], "exclude": ["node_modules"]}