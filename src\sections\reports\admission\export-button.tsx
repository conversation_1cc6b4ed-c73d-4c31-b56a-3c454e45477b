import dayjs from 'dayjs';
import API from '@/utils/api';
import { useCallback } from 'react';
import { useSnackbar } from '@/components/snackbar';
import EnumRequestHeader from '@/enum/EnumRequestHeader';
import ADMIN_ENDPOINTS from '@/models/api/admin-endpoints';

import Button from '@mui/material/Button';
import UpgradeIcon from '@mui/icons-material/Upgrade';

interface AdmissionReportQuery {
    event: string,
}

interface ExportButtonProps {
    event: string,
    onFinished: Function,
}

const reportDownload = async (query: AdmissionReportQuery) => {
    const jwt = API.GetUserJWT();
    if (!jwt) {
        throw new Error("EMPTY_JWT");
    }
    const qs = new URLSearchParams({
        ...query
    });
    const request = new Request(`${ADMIN_ENDPOINTS.GetAdmissionReport()}?${qs.toString()}`, {
        method: 'GET',
        headers: new Headers({
            [EnumRequestHeader.AUTHORIZATION]: jwt
        }),
    });
    try {
        return await (await fetch(request)).blob();
    } catch {
        throw new Error('Network error');
    }
};

const ExportButton = (props: ExportButtonProps) => {
    const { event, onFinished } = props;

    const { enqueueSnackbar } = useSnackbar();
    const onExport = useCallback(() => {
        (async () => {
            try {
                const data = await reportDownload({
                    event
                });
                const url = window.URL.createObjectURL(new Blob([data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', `Incutix_${event}_admission_Report_${dayjs().format("YYYYMMDDHHmmss")}.xlsx`);
                document.body.appendChild(link);
                link.click();
                enqueueSnackbar("Start downloading", { variant: "success" });
                onFinished();
            }
            catch (error: unknown) {
                console.warn(error);
                enqueueSnackbar("Download Failed, please ensure your credential is valid.", { variant: "error" });
            }
        })();
    }, [event, enqueueSnackbar, onFinished]);

    return (
        <Button variant="contained" endIcon={<UpgradeIcon />} onClick={onExport}>Export</Button>
    )
};

export default ExportButton;