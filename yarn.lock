# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10

"@aashutoshrathi/word-wrap@npm:^1.2.3":
  version: 1.2.6
  resolution: "@aashutoshrathi/word-wrap@npm:1.2.6"
  checksum: 6eebd12a5cd03cee38fcb915ef9f4ea557df6a06f642dfc7fe8eb4839eb5c9ca55a382f3604d52c14200b0c214c12af5e1f23d2a6d8e23ef2d016b105a9d6c0a
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.2.1
  resolution: "@ampproject/remapping@npm:2.2.1"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.0"
    "@jridgewell/trace-mapping": "npm:^0.3.9"
  checksum: e15fecbf3b54c988c8b4fdea8ef514ab482537e8a080b2978cc4b47ccca7140577ca7b65ad3322dcce65bc73ee6e5b90cbfe0bbd8c766dad04d5c62ec9634c42
  languageName: node
  linkType: hard

"@auth0/auth0-react@npm:^2.2.1":
  version: 2.2.3
  resolution: "@auth0/auth0-react@npm:2.2.3"
  dependencies:
    "@auth0/auth0-spa-js": "npm:^2.1.2"
  peerDependencies:
    react: ^16.11.0 || ^17 || ^18
    react-dom: ^16.11.0 || ^17 || ^18
  checksum: 24f304c333d270344a96b71f8f88faf60858280fc94d2b61ff9fa765408e8671663b902938055a872e06a80a7b711f5952f03492af88c137c9b4bb961fbf5ed5
  languageName: node
  linkType: hard

"@auth0/auth0-spa-js@npm:^2.1.2":
  version: 2.1.2
  resolution: "@auth0/auth0-spa-js@npm:2.1.2"
  checksum: f89b76551e071d51a44b7ce79061c4021c3e57d9e3ea7c5a3610ea6096ea4a8741772aa9a5f142021d51ec61a65f52f4bad53f8589f98b450c0a8ddd9f685ed2
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.22.13, @babel/code-frame@npm:^7.23.5":
  version: 7.23.5
  resolution: "@babel/code-frame@npm:7.23.5"
  dependencies:
    "@babel/highlight": "npm:^7.23.4"
    chalk: "npm:^2.4.2"
  checksum: 44e58529c9d93083288dc9e649c553c5ba997475a7b0758cc3ddc4d77b8a7d985dbe78cc39c9bbc61f26d50af6da1ddf0a3427eae8cc222a9370619b671ed8f5
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.22.9, @babel/compat-data@npm:^7.23.3, @babel/compat-data@npm:^7.23.5":
  version: 7.23.5
  resolution: "@babel/compat-data@npm:7.23.5"
  checksum: 088f14f646ecbddd5ef89f120a60a1b3389a50a9705d44603dca77662707d0175a5e0e0da3943c3298f1907a4ab871468656fbbf74bb7842cd8b0686b2c19736
  languageName: node
  linkType: hard

"@babel/core@npm:^7.21.3":
  version: 7.23.5
  resolution: "@babel/core@npm:7.23.5"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.23.5"
    "@babel/generator": "npm:^7.23.5"
    "@babel/helper-compilation-targets": "npm:^7.22.15"
    "@babel/helper-module-transforms": "npm:^7.23.3"
    "@babel/helpers": "npm:^7.23.5"
    "@babel/parser": "npm:^7.23.5"
    "@babel/template": "npm:^7.22.15"
    "@babel/traverse": "npm:^7.23.5"
    "@babel/types": "npm:^7.23.5"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: f24265172610dbffe0e315b6a8e8f87cf87d2643c8915196adcddd81c66a8eaeb1b36fea851e2308961636a180089a5f10becaa340d5b707d5f64e2e5ffb2bc8
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.23.5":
  version: 7.23.5
  resolution: "@babel/generator@npm:7.23.5"
  dependencies:
    "@babel/types": "npm:^7.23.5"
    "@jridgewell/gen-mapping": "npm:^0.3.2"
    "@jridgewell/trace-mapping": "npm:^0.3.17"
    jsesc: "npm:^2.5.1"
  checksum: 094af79c2e8fdb0cfd06b42ff6a39a8a95639bc987cace44f52ed5c46127f5469eb20ab5f4c8991fc00fa9c1445a1977cde8e44289d6be29ddbb315fb0fc1b45
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.23.6":
  version: 7.23.6
  resolution: "@babel/generator@npm:7.23.6"
  dependencies:
    "@babel/types": "npm:^7.23.6"
    "@jridgewell/gen-mapping": "npm:^0.3.2"
    "@jridgewell/trace-mapping": "npm:^0.3.17"
    jsesc: "npm:^2.5.1"
  checksum: 864090d5122c0aa3074471fd7b79d8a880c1468480cbd28925020a3dcc7eb6e98bedcdb38983df299c12b44b166e30915b8085a7bc126e68fa7e2aadc7bd1ac5
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-annotate-as-pure@npm:7.22.5"
  dependencies:
    "@babel/types": "npm:^7.22.5"
  checksum: 53da330f1835c46f26b7bf4da31f7a496dee9fd8696cca12366b94ba19d97421ce519a74a837f687749318f94d1a37f8d1abcbf35e8ed22c32d16373b2f6198d
  languageName: node
  linkType: hard

"@babel/helper-builder-binary-assignment-operator-visitor@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/helper-builder-binary-assignment-operator-visitor@npm:7.22.15"
  dependencies:
    "@babel/types": "npm:^7.22.15"
  checksum: 639c697a1c729f9fafa2dd4c9af2e18568190299b5907bd4c2d0bc818fcbd1e83ffeecc2af24327a7faa7ac4c34edd9d7940510a5e66296c19bad17001cf5c7a
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.22.15, @babel/helper-compilation-targets@npm:^7.22.6":
  version: 7.22.15
  resolution: "@babel/helper-compilation-targets@npm:7.22.15"
  dependencies:
    "@babel/compat-data": "npm:^7.22.9"
    "@babel/helper-validator-option": "npm:^7.22.15"
    browserslist: "npm:^4.21.9"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 9706decaa1591cf44511b6f3447eb9653b50ca3538215fe2e5387a8598c258c062f4622da5b95e61f0415706534deee619bbf53a2889f9bd967949b8f6024e0e
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.22.15, @babel/helper-create-class-features-plugin@npm:^7.23.5":
  version: 7.23.5
  resolution: "@babel/helper-create-class-features-plugin@npm:7.23.5"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.22.5"
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-function-name": "npm:^7.23.0"
    "@babel/helper-member-expression-to-functions": "npm:^7.23.0"
    "@babel/helper-optimise-call-expression": "npm:^7.22.5"
    "@babel/helper-replace-supers": "npm:^7.22.20"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.22.5"
    "@babel/helper-split-export-declaration": "npm:^7.22.6"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: cd951e81b6a4ad79879f38edbe78d51cf29dfd5a7d33d7162aeaa3ac536dcc9a6679de8feb976bbd76d255a1654bf1742410517edd5c426fec66e0bf41eb8c45
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.22.15, @babel/helper-create-regexp-features-plugin@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.22.15"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.22.5"
    regexpu-core: "npm:^5.3.1"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 886b675e82f1327b4f7a2c69a68eefdb5dbb0b9d4762c2d4f42a694960a9ccf61e1a3bcad601efd92c110033eb1a944fcd1e5cac188aa6b2e2076b541e210e20
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.4.3":
  version: 0.4.3
  resolution: "@babel/helper-define-polyfill-provider@npm:0.4.3"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.22.6"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    debug: "npm:^4.1.1"
    lodash.debounce: "npm:^4.0.8"
    resolve: "npm:^1.14.2"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 9ab9d6a2cfaffc44f8b7ad661b642b03f31597282557686b7f4c64f67acd3c5844d4eac028e63d238819bcec0549ddef7dc0539d10966ace96f4c61e97b33138
  languageName: node
  linkType: hard

"@babel/helper-environment-visitor@npm:^7.22.20":
  version: 7.22.20
  resolution: "@babel/helper-environment-visitor@npm:7.22.20"
  checksum: d80ee98ff66f41e233f36ca1921774c37e88a803b2f7dca3db7c057a5fea0473804db9fb6729e5dbfd07f4bed722d60f7852035c2c739382e84c335661590b69
  languageName: node
  linkType: hard

"@babel/helper-function-name@npm:^7.22.5, @babel/helper-function-name@npm:^7.23.0":
  version: 7.23.0
  resolution: "@babel/helper-function-name@npm:7.23.0"
  dependencies:
    "@babel/template": "npm:^7.22.15"
    "@babel/types": "npm:^7.23.0"
  checksum: 7b2ae024cd7a09f19817daf99e0153b3bf2bc4ab344e197e8d13623d5e36117ed0b110914bc248faa64e8ccd3e97971ec7b41cc6fd6163a2b980220c58dcdf6d
  languageName: node
  linkType: hard

"@babel/helper-hoist-variables@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-hoist-variables@npm:7.22.5"
  dependencies:
    "@babel/types": "npm:^7.22.5"
  checksum: 394ca191b4ac908a76e7c50ab52102669efe3a1c277033e49467913c7ed6f7c64d7eacbeabf3bed39ea1f41731e22993f763b1edce0f74ff8563fd1f380d92cc
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.22.15, @babel/helper-member-expression-to-functions@npm:^7.23.0":
  version: 7.23.0
  resolution: "@babel/helper-member-expression-to-functions@npm:7.23.0"
  dependencies:
    "@babel/types": "npm:^7.23.0"
  checksum: 325feb6e200478c8cd6e10433fabe993a7d3315cc1a2a457e45514a5f95a73dff4c69bea04cc2daea0ffe72d8ed85d504b3f00b2e0767b7d4f5ae25fec9b35b2
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.0.0, @babel/helper-module-imports@npm:^7.16.7, @babel/helper-module-imports@npm:^7.22.15, @babel/helper-module-imports@npm:^7.22.5":
  version: 7.22.15
  resolution: "@babel/helper-module-imports@npm:7.22.15"
  dependencies:
    "@babel/types": "npm:^7.22.15"
  checksum: 5ecf9345a73b80c28677cfbe674b9f567bb0d079e37dcba9055e36cb337db24ae71992a58e1affa9d14a60d3c69907d30fe1f80aea105184501750a58d15c81c
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/helper-module-transforms@npm:7.23.3"
  dependencies:
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-module-imports": "npm:^7.22.15"
    "@babel/helper-simple-access": "npm:^7.22.5"
    "@babel/helper-split-export-declaration": "npm:^7.22.6"
    "@babel/helper-validator-identifier": "npm:^7.22.20"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 583fa580f8e50e6f45c4f46aa76a8e49c2528deb84e25f634d66461b9a0e2420e13979b0a607b67aef67eaf8db8668eb9edc038b4514b16e3879fe09e8fd294b
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-optimise-call-expression@npm:7.22.5"
  dependencies:
    "@babel/types": "npm:^7.22.5"
  checksum: c70ef6cc6b6ed32eeeec4482127e8be5451d0e5282d5495d5d569d39eb04d7f1d66ec99b327f45d1d5842a9ad8c22d48567e93fc502003a47de78d122e355f7c
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.22.5, @babel/helper-plugin-utils@npm:^7.8.0, @babel/helper-plugin-utils@npm:^7.8.3":
  version: 7.22.5
  resolution: "@babel/helper-plugin-utils@npm:7.22.5"
  checksum: ab220db218089a2aadd0582f5833fd17fa300245999f5f8784b10f5a75267c4e808592284a29438a0da365e702f05acb369f99e1c915c02f9f9210ec60eab8ea
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.22.20":
  version: 7.22.20
  resolution: "@babel/helper-remap-async-to-generator@npm:7.22.20"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.22.5"
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-wrap-function": "npm:^7.22.20"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 2fe6300a6f1b58211dffa0aed1b45d4958506d096543663dba83bd9251fe8d670fa909143a65b45e72acb49e7e20fbdb73eae315d9ddaced467948c3329986e7
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.22.20":
  version: 7.22.20
  resolution: "@babel/helper-replace-supers@npm:7.22.20"
  dependencies:
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-member-expression-to-functions": "npm:^7.22.15"
    "@babel/helper-optimise-call-expression": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 617666f57b0f94a2f430ee66b67c8f6fa94d4c22400f622947580d8f3638ea34b71280af59599ed4afbb54ae6e2bdd4f9083fe0e341184a4bb0bd26ef58d3017
  languageName: node
  linkType: hard

"@babel/helper-simple-access@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-simple-access@npm:7.22.5"
  dependencies:
    "@babel/types": "npm:^7.22.5"
  checksum: 7d5430eecf880937c27d1aed14245003bd1c7383ae07d652b3932f450f60bfcf8f2c1270c593ab063add185108d26198c69d1aca0e6fb7c6fdada4bcf72ab5b7
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.22.5"
  dependencies:
    "@babel/types": "npm:^7.22.5"
  checksum: 1012ef2295eb12dc073f2b9edf3425661e9b8432a3387e62a8bc27c42963f1f216ab3124228015c748770b2257b4f1fda882ca8fa34c0bf485e929ae5bc45244
  languageName: node
  linkType: hard

"@babel/helper-split-export-declaration@npm:^7.22.6":
  version: 7.22.6
  resolution: "@babel/helper-split-export-declaration@npm:7.22.6"
  dependencies:
    "@babel/types": "npm:^7.22.5"
  checksum: e141cace583b19d9195f9c2b8e17a3ae913b7ee9b8120246d0f9ca349ca6f03cb2c001fd5ec57488c544347c0bb584afec66c936511e447fd20a360e591ac921
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/helper-string-parser@npm:7.23.4"
  checksum: c352082474a2ee1d2b812bd116a56b2e8b38065df9678a32a535f151ec6f58e54633cc778778374f10544b930703cca6ddf998803888a636afa27e2658068a9c
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.22.20":
  version: 7.22.20
  resolution: "@babel/helper-validator-identifier@npm:7.22.20"
  checksum: df882d2675101df2d507b95b195ca2f86a3ef28cb711c84f37e79ca23178e13b9f0d8b522774211f51e40168bf5142be4c1c9776a150cddb61a0d5bf3e95750b
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.22.15, @babel/helper-validator-option@npm:^7.23.5":
  version: 7.23.5
  resolution: "@babel/helper-validator-option@npm:7.23.5"
  checksum: 537cde2330a8aede223552510e8a13e9c1c8798afee3757995a7d4acae564124fe2bf7e7c3d90d62d3657434a74340a274b3b3b1c6f17e9a2be1f48af29cb09e
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.22.20":
  version: 7.22.20
  resolution: "@babel/helper-wrap-function@npm:7.22.20"
  dependencies:
    "@babel/helper-function-name": "npm:^7.22.5"
    "@babel/template": "npm:^7.22.15"
    "@babel/types": "npm:^7.22.19"
  checksum: b22e4666dec3d401bdf8ebd01d448bb3733617dae5aa6fbd1b684a22a35653cca832edd876529fd139577713b44fb89b4f5e52b7315ab218620f78b8a8ae23de
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.23.5":
  version: 7.23.5
  resolution: "@babel/helpers@npm:7.23.5"
  dependencies:
    "@babel/template": "npm:^7.22.15"
    "@babel/traverse": "npm:^7.23.5"
    "@babel/types": "npm:^7.23.5"
  checksum: 84a813db55e03b5f47cef1210eb22751dae5dc3605bf62ff9acd4c248d857f94cb43dc7299e0edcec9312b31088f0d77f881282df2957e65a322b5412801cc24
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/highlight@npm:7.23.4"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.22.20"
    chalk: "npm:^2.4.2"
    js-tokens: "npm:^4.0.0"
  checksum: 62fef9b5bcea7131df4626d009029b1ae85332042f4648a4ce6e740c3fd23112603c740c45575caec62f260c96b11054d3be5987f4981a5479793579c3aac71f
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.22.15, @babel/parser@npm:^7.23.5":
  version: 7.23.5
  resolution: "@babel/parser@npm:7.23.5"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 828c250ace0c58f9dc311fd13ad3da34e86ed27a5c6b4183ce9d85be250e78eeb71a13f6d51a368c46f8cbe51106c726bfbb158bf46a89db3a168a0002d3050a
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.23.6":
  version: 7.23.6
  resolution: "@babel/parser@npm:7.23.6"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 6be3a63d3c9d07b035b5a79c022327cb7e16cbd530140ecb731f19a650c794c315a72c699a22413ebeafaff14aa8f53435111898d59e01a393d741b85629fa7d
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: ddbaf2c396b7780f15e80ee01d6dd790db076985f3dfeb6527d1a8d4cacf370e49250396a3aa005b2c40233cac214a106232f83703d5e8491848bde273938232
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.22.5"
    "@babel/plugin-transform-optional-chaining": "npm:^7.23.3"
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: 434b9d710ae856fa1a456678cc304fbc93915af86d581ee316e077af746a709a741ea39d7e1d4f5b98861b629cc7e87f002d3138f5e836775632466d4c74aef2
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:7.23.3"
  dependencies:
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 6e13f14949eb943d33cf4d3775a7195fa93c92851dfb648931038e9eb92a9b1709fdaa5a0ff6cf063cfcd68b3e52d280f3ebc0f3085b3e006e64dd6196ecb72a
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2":
  version: 7.21.0-placeholder-for-preset-env.2
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fab70f399aa869275690ec6c7cedb4ef361d4e8b6f55c3d7b04bfee61d52fb93c87cec2c65d73cddbaca89fb8ef5ec0921fce675c9169d9d51f18305ab34e78a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7ed1c1d9b9e5b64ef028ea5e755c0be2d4e5e4e3d6cf7df757b9a8c4cfa4193d268176d0f1f7fbecdda6fe722885c7fda681f480f3741d8a2d26854736f05367
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.12.13"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 24f34b196d6342f28d4bad303612d7ff566ab0a013ce89e775d98d6f832969462e7235f3e7eaf17678a533d4be0ba45d3ae34ab4e5a9dcbda5d98d49e5efa2fc
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3e80814b5b6d4fe17826093918680a351c2d34398a914ce6e55d8083d72a9bdde4fbaf6a2dcea0e23a03de26dc2917ae3efd603d27099e2b98380345703bf948
  languageName: node
  linkType: hard

"@babel/plugin-syntax-dynamic-import@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-dynamic-import@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ce307af83cf433d4ec42932329fad25fa73138ab39c7436882ea28742e1c0066626d224e0ad2988724c82644e41601cef607b36194f695cb78a1fcdc959637bd
  languageName: node
  linkType: hard

"@babel/plugin-syntax-export-namespace-from@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-export-namespace-from@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 85740478be5b0de185228e7814451d74ab8ce0a26fcca7613955262a26e99e8e15e9da58f60c754b84515d4c679b590dbd3f2148f0f58025f4ae706f1c5a5d4a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 883e6b35b2da205138caab832d54505271a3fee3fc1e8dc0894502434fc2b5d517cbe93bbfbfef8068a0fb6ec48ebc9eef3f605200a489065ba43d8cddc1c9a7
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9aed7661ffb920ca75df9f494757466ca92744e43072e0848d87fa4aa61a3f2ee5a22198ac1959856c036434b5614a8f46f1fb70298835dbe28220cdd1d4c11e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 166ac1125d10b9c0c430e4156249a13858c0366d38844883d75d27389621ebe651115cb2ceb6dc011534d5055719fa1727b59f39e1ab3ca97820eef3dcab5b9b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bf5aea1f3188c9a507e16efe030efb996853ca3cadd6512c51db7233cc58f3ac89ff8c6bdfb01d30843b161cfe7d321e1bf28da82f7ab8d7e6bc5464666f354a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.22.5, @babel/plugin-syntax-jsx@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-syntax-jsx@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 89037694314a74e7f0e7a9c8d3793af5bf6b23d80950c29b360db1c66859d67f60711ea437e70ad6b5b4b29affe17eababda841b6c01107c2b638e0493bafb4e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: aff33577037e34e515911255cdbb1fd39efee33658aa00b8a5fd3a4b903585112d037cce1cc9e4632f0487dc554486106b79ccd5ea63a2e00df4363f6d4ff886
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 87aca4918916020d1fedba54c0e232de408df2644a425d153be368313fdde40d96088feed6c4e5ab72aac89be5d07fef2ddf329a15109c5eb65df006bf2580d1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 01ec5547bd0497f76cc903ff4d6b02abc8c05f301c88d2622b6d834e33a5651aa7c7a3d80d8d57656a4588f7276eba357f6b7e006482f5b564b7a6488de493a1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fddcf581a57f77e80eb6b981b10658421bc321ba5f0a5b754118c6a92a5448f12a0c336f77b8abf734841e102e5126d69110a306eadb03ca3e1547cab31f5cbf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 910d90e72bc90ea1ce698e89c1027fed8845212d5ab588e35ef91f13b93143845f94e2539d831dc8d8ededc14ec02f04f7bd6a8179edd43a326c784e7ed7f0b9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: eef94d53a1453361553c1f98b68d17782861a04a392840341bc91780838dd4e695209c783631cf0de14c635758beafb6a3a65399846ffa4386bff90639347f30
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b317174783e6e96029b743ccff2a67d63d38756876e7e5d0ba53a322e38d9ca452c13354a57de1ad476b4c066dbae699e0ca157441da611117a47af88985ecda
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bbd1a56b095be7820029b209677b194db9b1d26691fe999856462e66b25b281f031f3dfd91b1619e9dcf95bebe336211833b854d0fb8780d618e35667c2d0d7e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-syntax-typescript@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: abfad3a19290d258b028e285a1f34c9b8a0cbe46ef79eafed4ed7ffce11b5d0720b5e536c82f91cbd8442cde35a3dd8e861fa70366d87ff06fdc0d4756e30876
  languageName: node
  linkType: hard

"@babel/plugin-syntax-unicode-sets-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-unicode-sets-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.18.6"
    "@babel/helper-plugin-utils": "npm:^7.18.6"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: a651d700fe63ff0ddfd7186f4ebc24447ca734f114433139e3c027bc94a900d013cf1ef2e2db8430425ba542e39ae160c3b05f06b59fd4656273a3df97679e9c
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1e99118176e5366c2636064d09477016ab5272b2a92e78b8edb571d20bc3eaa881789a905b20042942c3c2d04efc530726cf703f937226db5ebc495f5d067e66
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.23.4"
  dependencies:
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/helper-remap-async-to-generator": "npm:^7.22.20"
    "@babel/plugin-syntax-async-generators": "npm:^7.8.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e2fc132c9033711d55209f4781e1fc73f0f4da5e0ca80a2da73dec805166b73c92a6e83571a8994cd2c893a28302e24107e90856202b24781bab734f800102bb
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.23.3"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/helper-remap-async-to-generator": "npm:^7.22.20"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2e9d9795d4b3b3d8090332104e37061c677f29a1ce65bcbda4099a32d243e5d9520270a44bbabf0fb1fb40d463bd937685b1a1042e646979086c546d55319c3c
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e63b16d94ee5f4d917e669da3db5ea53d1e7e79141a2ec873c1e644678cdafe98daa556d0d359963c827863d6b3665d23d4938a94a4c5053a1619c4ebd01d020
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/plugin-transform-block-scoping@npm:7.23.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bbb965a3acdfb03559806d149efbd194ac9c983b260581a60efcb15eb9fbe20e3054667970800146d867446db1c1398f8e4ee87f4454233e49b8f8ce947bd99b
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-class-properties@npm:7.23.3"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9c6f8366f667897541d360246de176dd29efc7a13d80a5b48361882f7173d9173be4646c3b7d9b003ccc0e01e25df122330308f33db921fa553aa17ad544b3fc
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-static-block@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/plugin-transform-class-static-block@npm:7.23.4"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/plugin-syntax-class-static-block": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: c8bfaba19a674fc2eb54edad71e958647360474e3163e8226f1acd63e4e2dbec32a171a0af596c1dc5359aee402cc120fea7abd1fb0e0354b6527f0fc9e8aa1e
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.23.5":
  version: 7.23.5
  resolution: "@babel/plugin-transform-classes@npm:7.23.5"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.22.5"
    "@babel/helper-compilation-targets": "npm:^7.22.15"
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-function-name": "npm:^7.23.0"
    "@babel/helper-optimise-call-expression": "npm:^7.22.5"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/helper-replace-supers": "npm:^7.22.20"
    "@babel/helper-split-export-declaration": "npm:^7.22.6"
    globals: "npm:^11.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f6c4fed2f48bdd46a4726b829ea2ddb5c9c97edd0e55dc53791d82927daad5725052b7e785a8b7e90a53b0606166b9c554469dc94f10fba59ca9642e997d97ee
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-computed-properties@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/template": "npm:^7.22.15"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e75593e02c5ea473c17839e3c9d597ce3697bf039b66afe9a4d06d086a87fb3d95850b4174476897afc351dc1b46a9ec3165ee6e8fbad3732c0d65f676f855ad
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-destructuring@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5abd93718af5a61f8f6a97d2ccac9139499752dd5b2c533d7556fb02947ae01b2f51d4c4f5e64df569e8783d3743270018eb1fa979c43edec7dd1377acf107ed
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.23.3"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a2dbbf7f1ea16a97948c37df925cb364337668c41a3948b8d91453f140507bd8a3429030c7ce66d09c299987b27746c19a2dd18b6f17dcb474854b14fd9159a3
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c2a21c34dc0839590cd945192cbc46fde541a27e140c48fe1808315934664cdbf18db64889e23c4eeb6bad9d3e049482efdca91d29de5734ffc887c4fbabaa16
  languageName: node
  linkType: hard

"@babel/plugin-transform-dynamic-import@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/plugin-transform-dynamic-import@npm:7.23.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/plugin-syntax-dynamic-import": "npm:^7.8.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 57a722604c430d9f3dacff22001a5f31250e34785d4969527a2ae9160fa86858d0892c5b9ff7a06a04076f8c76c9e6862e0541aadca9c057849961343aab0845
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.23.3"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 00d05ab14ad0f299160fcf9d8f55a1cc1b740e012ab0b5ce30207d2365f091665115557af7d989cd6260d075a252d9e4283de5f2b247dfbbe0e42ae586e6bf66
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.23.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/plugin-syntax-export-namespace-from": "npm:^7.8.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9f770a81bfd03b48d6ba155d452946fd56d6ffe5b7d871e9ec2a0b15e0f424273b632f3ed61838b90015b25bbda988896b7a46c7d964fbf8f6feb5820b309f93
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-for-of@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 745054f125fba6dbaea3d863352c94266c97db87e3521bc6c436a8c05f384821907c0109ace437a90342e423a3365f4d8e592de06e4a241bbd7070e1f293604f
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-function-name@npm:7.23.3"
  dependencies:
    "@babel/helper-compilation-targets": "npm:^7.22.15"
    "@babel/helper-function-name": "npm:^7.23.0"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 355c6dbe07c919575ad42b2f7e020f320866d72f8b79181a16f8e0cd424a2c761d979f03f47d583d9471b55dcd68a8a9d829b58e1eebcd572145b934b48975a6
  languageName: node
  linkType: hard

"@babel/plugin-transform-json-strings@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/plugin-transform-json-strings@npm:7.23.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/plugin-syntax-json-strings": "npm:^7.8.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f9019820233cf8955d8ba346df709a0683c120fe86a24ed1c9f003f2db51197b979efc88f010d558a12e1491210fc195a43cd1c7fee5e23b92da38f793a875de
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-literals@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 519a544cd58586b9001c4c9b18da25a62f17d23c48600ff7a685d75ca9eb18d2c5e8f5476f067f0a8f1fea2a31107eff950b9864833061e6076dcc4bdc3e71ed
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.23.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/plugin-syntax-logical-assignment-operators": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2ae1dc9b4ff3bf61a990ff3accdecb2afe3a0ca649b3e74c010078d1cdf29ea490f50ac0a905306a2bcf9ac177889a39ac79bdcc3a0fdf220b3b75fac18d39b5
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 95cec13c36d447c5aa6b8e4c778b897eeba66dcb675edef01e0d2afcec9e8cb9726baf4f81b4bbae7a782595aed72e6a0d44ffb773272c3ca180fada99bf92db
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-modules-amd@npm:7.23.3"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.23.3"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 48c87dee2c7dae8ed40d16901f32c9e58be4ef87bf2c3985b51dd2e78e82081f3bad0a39ee5cf6e8909e13e954e2b4bedef0a8141922f281ed833ddb59ed9be2
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.23.3"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.23.3"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/helper-simple-access": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a3bc082d0dfe8327a29263a6d721cea608d440bc8141ba3ec6ba80ad73d84e4f9bbe903c27e9291c29878feec9b5dee2bd0563822f93dc951f5d7fc36bdfe85b
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.23.3"
  dependencies:
    "@babel/helper-hoist-variables": "npm:^7.22.5"
    "@babel/helper-module-transforms": "npm:^7.23.3"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/helper-validator-identifier": "npm:^7.22.20"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 051112de7585fff4ffd67865066401f01f90745d41f26b0edbeec0981342c10517ce1a6b4d7051b583a3e513088eece6a3f57b1663f1dd9418071cd05f14fef9
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-modules-umd@npm:7.23.3"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.23.3"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e3f3af83562d687899555c7826b3faf0ab93ee7976898995b1d20cbe7f4451c55e05b0e17bfb3e549937cbe7573daf5400b752912a241b0a8a64d2457c7626e5
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.22.5"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.22.5"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 3ee564ddee620c035b928fdc942c5d17e9c4b98329b76f9cefac65c111135d925eb94ed324064cd7556d4f5123beec79abea1d4b97d1c8a2a5c748887a2eb623
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-new-target@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e5053389316fce73ad5201b7777437164f333e24787fbcda4ae489cd2580dbbbdfb5694a7237bad91fabb46b591d771975d69beb1c740b82cb4761625379f00b
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.23.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/plugin-syntax-nullish-coalescing-operator": "npm:^7.8.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a27d73ea134d3d9560a6b2e26ab60012fba15f1db95865aa0153c18f5ec82cfef6a7b3d8df74e3c2fca81534fa5efeb6cacaf7b08bdb7d123e3dafdd079886a3
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.23.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/plugin-syntax-numeric-separator": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6ba0e5db3c620a3ec81f9e94507c821f483c15f196868df13fa454cbac719a5449baf73840f5b6eb7d77311b24a2cf8e45db53700d41727f693d46f7caf3eec3
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.23.4"
  dependencies:
    "@babel/compat-data": "npm:^7.23.3"
    "@babel/helper-compilation-targets": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/plugin-syntax-object-rest-spread": "npm:^7.8.3"
    "@babel/plugin-transform-parameters": "npm:^7.23.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 656f09c4ec629856e807d5b386559166ae417ff75943abce19656b2c6de5101dfd0aaf23f9074e854339370b4e09f57518d3202457046ee5b567ded531005479
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-object-super@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/helper-replace-supers": "npm:^7.22.20"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: e495497186f621fa79026e183b4f1fbb172fd9df812cbd2d7f02c05b08adbe58012b1a6eb6dd58d11a30343f6ec80d0f4074f9b501d70aa1c94df76d59164c53
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.23.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/plugin-syntax-optional-catch-binding": "npm:^7.8.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d50b5ee142cdb088d8b5de1ccf7cea85b18b85d85b52f86618f6e45226372f01ad4cdb29abd4fd35ea99a71fefb37009e0107db7a787dcc21d4d402f97470faf
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.23.3, @babel/plugin-transform-optional-chaining@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.23.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.22.5"
    "@babel/plugin-syntax-optional-chaining": "npm:^7.8.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0ef24e889d6151428953fc443af5f71f4dae73f373dc1b7f5dd3f6a61d511296eb77e9b870e8c2c02a933e3455ae24c1fa91738c826b72a4ff87e0337db527e8
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-parameters@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a8c36c3fc25f9daa46c4f6db47ea809c395dc4abc7f01c4b1391f6e5b0cd62b83b6016728b02a6a8ac21aca56207c9ec66daefc0336e9340976978de7e6e28df
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-private-methods@npm:7.23.3"
  dependencies:
    "@babel/helper-create-class-features-plugin": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cedc1285c49b5a6d9a3d0e5e413b756ac40b3ac2f8f68bdfc3ae268bc8d27b00abd8bb0861c72756ff5dd8bf1eb77211b7feb5baf4fdae2ebbaabe49b9adc1d0
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.23.4":
  version: 7.23.4
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.23.4"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.22.5"
    "@babel/helper-create-class-features-plugin": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/plugin-syntax-private-property-in-object": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 02eef2ee98fa86ee5052ed9bf0742d6d22b510b5df2fcce0b0f5615d6001f7786c6b31505e7f1c2f446406d8fb33603a5316d957cfa5b8365cbf78ddcc24fa42
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-property-literals@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 16b048c8e87f25095f6d53634ab7912992f78e6997a6ff549edc3cf519db4fca01c7b4e0798530d7f6a05228ceee479251245cdd850a5531c6e6f404104d6cc9
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-constant-elements@npm:^7.21.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-react-constant-elements@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0bc89f7e81bb455bf58a90bf78ed0d3b4b0ef41bb1abde1364922fece8f0fbf9ca43887685653104238636a0b385144c7fb952c0047edaf7e8bbbaa5d734587b
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-display-name@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-react-display-name@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7f86964e8434d3ddbd3c81d2690c9b66dbf1cd8bd9512e2e24500e9fa8cf378bc52c0853270b3b82143aba5965aec04721df7abdb768f952b44f5c6e0b198779
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-development@npm:^7.22.5":
  version: 7.22.5
  resolution: "@babel/plugin-transform-react-jsx-development@npm:7.22.5"
  dependencies:
    "@babel/plugin-transform-react-jsx": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 36bc3ff0b96bb0ef4723070a50cfdf2e72cfd903a59eba448f9fe92fea47574d6f22efd99364413719e1f3fb3c51b6c9b2990b87af088f8486a84b2a5f9e4560
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx@npm:^7.22.15, @babel/plugin-transform-react-jsx@npm:^7.22.5":
  version: 7.23.4
  resolution: "@babel/plugin-transform-react-jsx@npm:7.23.4"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.22.5"
    "@babel/helper-module-imports": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/plugin-syntax-jsx": "npm:^7.23.3"
    "@babel/types": "npm:^7.23.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d83806701349addfb77b8347b4f0dc8e76fb1c9ac21bdef69f4002394fce2396d61facfc6e1a3de54cbabcdadf991a1f642e69edb5116ac14f95e33d9f7c221d
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-pure-annotations@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-react-pure-annotations@npm:7.23.3"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.22.5"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9ea3698b1d422561d93c0187ac1ed8f2367e4250b10e259785ead5aa643c265830fd0f4cf5087a5bedbc4007444c06da2f2006686613220acf0949895f453666
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-regenerator@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    regenerator-transform: "npm:^0.15.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7fdacc7b40008883871b519c9e5cdea493f75495118ccc56ac104b874983569a24edd024f0f5894ba1875c54ee2b442f295d6241c3280e61c725d0dd3317c8e6
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-reserved-words@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 298c4440ddc136784ff920127cea137168e068404e635dc946ddb5d7b2a27b66f1dd4c4acb01f7184478ff7d5c3e7177a127279479926519042948fb7fa0fa48
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5d677a03676f9fff969b0246c423d64d77502e90a832665dc872a5a5e05e5708161ce1effd56bb3c0f2c20a1112fca874be57c8a759d8b08152755519281f326
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-spread@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c6372d2f788fd71d85aba12fbe08ee509e053ed27457e6674a4f9cae41ff885e2eb88aafea8fadd0ccf990601fc69ec596fa00959e05af68a15461a8d97a548d
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 53e55eb2575b7abfdb4af7e503a2bf7ef5faf8bf6b92d2cd2de0700bdd19e934e5517b23e6dfed94ba50ae516b62f3f916773ef7d9bc81f01503f585051e2949
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-template-literals@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b16c5cb0b8796be0118e9c144d15bdc0d20a7f3f59009c6303a6e9a8b74c146eceb3f05186f5b97afcba7cfa87e34c1585a22186e3d5b22f2fd3d27d959d92b2
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0af7184379d43afac7614fc89b1bdecce4e174d52f4efaeee8ec1a4f2c764356c6dba3525c0685231f1cbf435b6dd4ee9e738d7417f3b10ce8bbe869c32f4384
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.23.3":
  version: 7.23.5
  resolution: "@babel/plugin-transform-typescript@npm:7.23.5"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.22.5"
    "@babel/helper-create-class-features-plugin": "npm:^7.23.5"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/plugin-syntax-typescript": "npm:^7.23.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f8cfea916092e3604b78aa9e84d342572023e61036d797c23730aeee6efdc6ac8a836d2a5c0588eacfc0b2e9482df8a820923f23b7cfe4e9bf92d9de9c5e499f
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 561c429183a54b9e4751519a3dfba6014431e9cdc1484fad03bdaf96582dfc72c76a4f8661df2aeeae7c34efd0fa4d02d3b83a2f63763ecf71ecc925f9cc1f60
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-property-regex@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-unicode-property-regex@npm:7.23.3"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2298461a194758086d17c23c26c7de37aa533af910f9ebf31ebd0893d4aa317468043d23f73edc782ec21151d3c46cf0ff8098a83b725c49a59de28a1d4d6225
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.23.3"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c5f835d17483ba899787f92e313dfa5b0055e3deab332f1d254078a2bba27ede47574b6599fcf34d3763f0c048ae0779dc21d2d8db09295edb4057478dc80a9a
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-sets-regex@npm:^7.23.3":
  version: 7.23.3
  resolution: "@babel/plugin-transform-unicode-sets-regex@npm:7.23.3"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 79d0b4c951955ca68235c87b91ab2b393c96285f8aeaa34d6db416d2ddac90000c9bd6e8c4d82b60a2b484da69930507245035f28ba63c6cae341cf3ba68fdef
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.20.2":
  version: 7.23.5
  resolution: "@babel/preset-env@npm:7.23.5"
  dependencies:
    "@babel/compat-data": "npm:^7.23.5"
    "@babel/helper-compilation-targets": "npm:^7.22.15"
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/helper-validator-option": "npm:^7.23.5"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "npm:^7.23.3"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "npm:^7.23.3"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": "npm:^7.23.3"
    "@babel/plugin-proposal-private-property-in-object": "npm:7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-async-generators": "npm:^7.8.4"
    "@babel/plugin-syntax-class-properties": "npm:^7.12.13"
    "@babel/plugin-syntax-class-static-block": "npm:^7.14.5"
    "@babel/plugin-syntax-dynamic-import": "npm:^7.8.3"
    "@babel/plugin-syntax-export-namespace-from": "npm:^7.8.3"
    "@babel/plugin-syntax-import-assertions": "npm:^7.23.3"
    "@babel/plugin-syntax-import-attributes": "npm:^7.23.3"
    "@babel/plugin-syntax-import-meta": "npm:^7.10.4"
    "@babel/plugin-syntax-json-strings": "npm:^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators": "npm:^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator": "npm:^7.8.3"
    "@babel/plugin-syntax-numeric-separator": "npm:^7.10.4"
    "@babel/plugin-syntax-object-rest-spread": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-chaining": "npm:^7.8.3"
    "@babel/plugin-syntax-private-property-in-object": "npm:^7.14.5"
    "@babel/plugin-syntax-top-level-await": "npm:^7.14.5"
    "@babel/plugin-syntax-unicode-sets-regex": "npm:^7.18.6"
    "@babel/plugin-transform-arrow-functions": "npm:^7.23.3"
    "@babel/plugin-transform-async-generator-functions": "npm:^7.23.4"
    "@babel/plugin-transform-async-to-generator": "npm:^7.23.3"
    "@babel/plugin-transform-block-scoped-functions": "npm:^7.23.3"
    "@babel/plugin-transform-block-scoping": "npm:^7.23.4"
    "@babel/plugin-transform-class-properties": "npm:^7.23.3"
    "@babel/plugin-transform-class-static-block": "npm:^7.23.4"
    "@babel/plugin-transform-classes": "npm:^7.23.5"
    "@babel/plugin-transform-computed-properties": "npm:^7.23.3"
    "@babel/plugin-transform-destructuring": "npm:^7.23.3"
    "@babel/plugin-transform-dotall-regex": "npm:^7.23.3"
    "@babel/plugin-transform-duplicate-keys": "npm:^7.23.3"
    "@babel/plugin-transform-dynamic-import": "npm:^7.23.4"
    "@babel/plugin-transform-exponentiation-operator": "npm:^7.23.3"
    "@babel/plugin-transform-export-namespace-from": "npm:^7.23.4"
    "@babel/plugin-transform-for-of": "npm:^7.23.3"
    "@babel/plugin-transform-function-name": "npm:^7.23.3"
    "@babel/plugin-transform-json-strings": "npm:^7.23.4"
    "@babel/plugin-transform-literals": "npm:^7.23.3"
    "@babel/plugin-transform-logical-assignment-operators": "npm:^7.23.4"
    "@babel/plugin-transform-member-expression-literals": "npm:^7.23.3"
    "@babel/plugin-transform-modules-amd": "npm:^7.23.3"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.23.3"
    "@babel/plugin-transform-modules-systemjs": "npm:^7.23.3"
    "@babel/plugin-transform-modules-umd": "npm:^7.23.3"
    "@babel/plugin-transform-named-capturing-groups-regex": "npm:^7.22.5"
    "@babel/plugin-transform-new-target": "npm:^7.23.3"
    "@babel/plugin-transform-nullish-coalescing-operator": "npm:^7.23.4"
    "@babel/plugin-transform-numeric-separator": "npm:^7.23.4"
    "@babel/plugin-transform-object-rest-spread": "npm:^7.23.4"
    "@babel/plugin-transform-object-super": "npm:^7.23.3"
    "@babel/plugin-transform-optional-catch-binding": "npm:^7.23.4"
    "@babel/plugin-transform-optional-chaining": "npm:^7.23.4"
    "@babel/plugin-transform-parameters": "npm:^7.23.3"
    "@babel/plugin-transform-private-methods": "npm:^7.23.3"
    "@babel/plugin-transform-private-property-in-object": "npm:^7.23.4"
    "@babel/plugin-transform-property-literals": "npm:^7.23.3"
    "@babel/plugin-transform-regenerator": "npm:^7.23.3"
    "@babel/plugin-transform-reserved-words": "npm:^7.23.3"
    "@babel/plugin-transform-shorthand-properties": "npm:^7.23.3"
    "@babel/plugin-transform-spread": "npm:^7.23.3"
    "@babel/plugin-transform-sticky-regex": "npm:^7.23.3"
    "@babel/plugin-transform-template-literals": "npm:^7.23.3"
    "@babel/plugin-transform-typeof-symbol": "npm:^7.23.3"
    "@babel/plugin-transform-unicode-escapes": "npm:^7.23.3"
    "@babel/plugin-transform-unicode-property-regex": "npm:^7.23.3"
    "@babel/plugin-transform-unicode-regex": "npm:^7.23.3"
    "@babel/plugin-transform-unicode-sets-regex": "npm:^7.23.3"
    "@babel/preset-modules": "npm:0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2: "npm:^0.4.6"
    babel-plugin-polyfill-corejs3: "npm:^0.8.5"
    babel-plugin-polyfill-regenerator: "npm:^0.5.3"
    core-js-compat: "npm:^3.31.0"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9c2c2ca7a8ac7ea5a36866f5c1df43936f60b4b5988693c574d531a2abfbcd2804d8a67db3560a8e505cf11e2c3e3031ce4104a84685cff6fbd46b884592146c
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:0.1.6-no-external-plugins":
  version: 0.1.6-no-external-plugins
  resolution: "@babel/preset-modules@npm:0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.0.0"
    "@babel/types": "npm:^7.4.4"
    esutils: "npm:^2.0.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0
  checksum: 039aba98a697b920d6440c622aaa6104bb6076d65356b29dad4b3e6627ec0354da44f9621bafbeefd052cd4ac4d7f88c9a2ab094efcb50963cb352781d0c6428
  languageName: node
  linkType: hard

"@babel/preset-react@npm:^7.18.6":
  version: 7.23.3
  resolution: "@babel/preset-react@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/helper-validator-option": "npm:^7.22.15"
    "@babel/plugin-transform-react-display-name": "npm:^7.23.3"
    "@babel/plugin-transform-react-jsx": "npm:^7.22.15"
    "@babel/plugin-transform-react-jsx-development": "npm:^7.22.5"
    "@babel/plugin-transform-react-pure-annotations": "npm:^7.23.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ef6aef131b2f36e2883e9da0d832903643cb3c9ad4f32e04fb3eecae59e4221d583139e8d8f973e25c28d15aafa6b3e60fe9f25c5fd09abd3e2df03b8637bdd2
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.21.0":
  version: 7.23.3
  resolution: "@babel/preset-typescript@npm:7.23.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.22.5"
    "@babel/helper-validator-option": "npm:^7.22.15"
    "@babel/plugin-syntax-jsx": "npm:^7.23.3"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.23.3"
    "@babel/plugin-transform-typescript": "npm:^7.23.3"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c4add0f3fcbb3f4a305c48db9ccb32694f1308ed9971ccbc1a8a3c76d5a13726addb3c667958092287d7aa080186c5c83dbfefa55eacf94657e6cde39e172848
  languageName: node
  linkType: hard

"@babel/regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "@babel/regjsgen@npm:0.8.0"
  checksum: c57fb730b17332b7572574b74364a77d70faa302a281a62819476fa3b09822974fd75af77aea603ad77378395be64e81f89f0e800bf86cbbf21652d49ce12ee8
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.0.0, @babel/runtime@npm:^7.15.4, @babel/runtime@npm:^7.3.1, @babel/runtime@npm:^7.4.4, @babel/runtime@npm:^7.8.3":
  version: 7.23.8
  resolution: "@babel/runtime@npm:7.23.8"
  dependencies:
    regenerator-runtime: "npm:^0.14.0"
  checksum: ec8f1967a36164da6cac868533ffdff97badd76d23d7d820cc84f0818864accef972f22f9c6a710185db1e3810e353fc18c3da721e5bb3ee8bc61bdbabce03ff
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.12.1, @babel/runtime@npm:^7.12.5, @babel/runtime@npm:^7.18.3, @babel/runtime@npm:^7.21.0, @babel/runtime@npm:^7.22.5, @babel/runtime@npm:^7.23.2, @babel/runtime@npm:^7.23.4, @babel/runtime@npm:^7.5.5, @babel/runtime@npm:^7.6.2, @babel/runtime@npm:^7.7.2, @babel/runtime@npm:^7.8.4, @babel/runtime@npm:^7.8.7, @babel/runtime@npm:^7.9.2":
  version: 7.23.5
  resolution: "@babel/runtime@npm:7.23.5"
  dependencies:
    regenerator-runtime: "npm:^0.14.0"
  checksum: 0f1669f639af30a0a2948ffcefa2c61935f337b0777bd94f8d7bc66bba8e7d4499e725caeb0449540d9c6d67399b733c4e719babb43ce9a0f33095aa01b42b37
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.23.9":
  version: 7.25.0
  resolution: "@babel/runtime@npm:7.25.0"
  dependencies:
    regenerator-runtime: "npm:^0.14.0"
  checksum: 6870e9e0e9125075b3aeba49a266f442b10820bfc693019eb6c1785c5a0edbe927e98b8238662cdcdba17842107c040386c3b69f39a0a3b217f9d00ffe685b27
  languageName: node
  linkType: hard

"@babel/template@npm:^7.22.15":
  version: 7.22.15
  resolution: "@babel/template@npm:7.22.15"
  dependencies:
    "@babel/code-frame": "npm:^7.22.13"
    "@babel/parser": "npm:^7.22.15"
    "@babel/types": "npm:^7.22.15"
  checksum: 21e768e4eed4d1da2ce5d30aa51db0f4d6d8700bc1821fec6292587df7bba2fe1a96451230de8c64b989740731888ebf1141138bfffb14cacccf4d05c66ad93f
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.23.5":
  version: 7.23.5
  resolution: "@babel/traverse@npm:7.23.5"
  dependencies:
    "@babel/code-frame": "npm:^7.23.5"
    "@babel/generator": "npm:^7.23.5"
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-function-name": "npm:^7.23.0"
    "@babel/helper-hoist-variables": "npm:^7.22.5"
    "@babel/helper-split-export-declaration": "npm:^7.22.6"
    "@babel/parser": "npm:^7.23.5"
    "@babel/types": "npm:^7.23.5"
    debug: "npm:^4.1.0"
    globals: "npm:^11.1.0"
  checksum: 281cae2765caad88c7af6214eab3647db0e9cadc7ffcd3fd924f09fbb9bd09d97d6fb210794b7545c317ce417a30016636530043a455ba6922349e39c1ba622a
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.4.5":
  version: 7.23.7
  resolution: "@babel/traverse@npm:7.23.7"
  dependencies:
    "@babel/code-frame": "npm:^7.23.5"
    "@babel/generator": "npm:^7.23.6"
    "@babel/helper-environment-visitor": "npm:^7.22.20"
    "@babel/helper-function-name": "npm:^7.23.0"
    "@babel/helper-hoist-variables": "npm:^7.22.5"
    "@babel/helper-split-export-declaration": "npm:^7.22.6"
    "@babel/parser": "npm:^7.23.6"
    "@babel/types": "npm:^7.23.6"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 3215e59429963c8dac85c26933372cdd322952aa9930e4bc5ef2d0e4bd7a1510d1ecf8f8fd860ace5d4d9fe496d23805a1ea019a86410aee4111de5f63ee84f9
  languageName: node
  linkType: hard

"@babel/types@npm:^7.21.3, @babel/types@npm:^7.22.15, @babel/types@npm:^7.22.19, @babel/types@npm:^7.22.5, @babel/types@npm:^7.23.0, @babel/types@npm:^7.23.4, @babel/types@npm:^7.23.5, @babel/types@npm:^7.4.4, @babel/types@npm:^7.8.3":
  version: 7.23.5
  resolution: "@babel/types@npm:7.23.5"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.23.4"
    "@babel/helper-validator-identifier": "npm:^7.22.20"
    to-fast-properties: "npm:^2.0.0"
  checksum: a623a4e7f396f1903659099da25bfa059694a49f42820f6b5288347f1646f0b37fb7cc550ba45644e9067149368ef34ccb1bd4a4251ec59b83b3f7765088f363
  languageName: node
  linkType: hard

"@babel/types@npm:^7.23.6":
  version: 7.23.6
  resolution: "@babel/types@npm:7.23.6"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.23.4"
    "@babel/helper-validator-identifier": "npm:^7.22.20"
    to-fast-properties: "npm:^2.0.0"
  checksum: 07e70bb94d30b0231396b5e9a7726e6d9227a0a62e0a6830c0bd3232f33b024092e3d5a7d1b096a65bbf2bb43a9ab4c721bf618e115bfbb87b454fa060f88cbf
  languageName: node
  linkType: hard

"@emotion/babel-plugin@npm:^11.11.0":
  version: 11.11.0
  resolution: "@emotion/babel-plugin@npm:11.11.0"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.16.7"
    "@babel/runtime": "npm:^7.18.3"
    "@emotion/hash": "npm:^0.9.1"
    "@emotion/memoize": "npm:^0.8.1"
    "@emotion/serialize": "npm:^1.1.2"
    babel-plugin-macros: "npm:^3.1.0"
    convert-source-map: "npm:^1.5.0"
    escape-string-regexp: "npm:^4.0.0"
    find-root: "npm:^1.1.0"
    source-map: "npm:^0.5.7"
    stylis: "npm:4.2.0"
  checksum: 8de017666838fc06b1a961d7a49b4e6dc0c83dbb064ea33512bae056594f0811a87e3242ef90fa2aa49fc080fab1cc7af536e7aee9398eaca7a1fc020d2dd527
  languageName: node
  linkType: hard

"@emotion/cache@npm:^11.10.5, @emotion/cache@npm:^11.11.0":
  version: 11.11.0
  resolution: "@emotion/cache@npm:11.11.0"
  dependencies:
    "@emotion/memoize": "npm:^0.8.1"
    "@emotion/sheet": "npm:^1.2.2"
    "@emotion/utils": "npm:^1.2.1"
    "@emotion/weak-memoize": "npm:^0.3.1"
    stylis: "npm:4.2.0"
  checksum: ef29756247dafb87168b4ffb76ee60feb06b8a1016323ecb1d3ba8aed3f4300ca10049bedbfe83aa11e0d81e616c328002a9d50020ebb3af6e4f5337a785c1fe
  languageName: node
  linkType: hard

"@emotion/hash@npm:^0.8.0":
  version: 0.8.0
  resolution: "@emotion/hash@npm:0.8.0"
  checksum: 4b35d88a97e67275c1d990c96d3b0450451d089d1508619488fc0acb882cb1ac91e93246d471346ebd1b5402215941ef4162efe5b51534859b39d8b3a0e3ffaa
  languageName: node
  linkType: hard

"@emotion/hash@npm:^0.9.1":
  version: 0.9.1
  resolution: "@emotion/hash@npm:0.9.1"
  checksum: 716e17e48bf9047bf9383982c071de49f2615310fb4e986738931776f5a823bc1f29c84501abe0d3df91a3803c80122d24e28b57351bca9e01356ebb33d89876
  languageName: node
  linkType: hard

"@emotion/is-prop-valid@npm:^0.7.3":
  version: 0.7.3
  resolution: "@emotion/is-prop-valid@npm:0.7.3"
  dependencies:
    "@emotion/memoize": "npm:0.7.1"
  checksum: b28e38a3978094afcb83bffb1d5c2d350ca4f9b233b779095a3b84bf4e63f28f1e4318af63623d55d2f885e041840abca9cc7b31ee87afc9065c18719574b4c6
  languageName: node
  linkType: hard

"@emotion/is-prop-valid@npm:^0.8.2":
  version: 0.8.8
  resolution: "@emotion/is-prop-valid@npm:0.8.8"
  dependencies:
    "@emotion/memoize": "npm:0.7.4"
  checksum: e85bdeb9d9d23de422f271e0f5311a0142b15055bb7e610440dbf250f0cdfd049df88af72a49e2c6081954481f1cbeca9172e2116ff536b38229397dfbed8082
  languageName: node
  linkType: hard

"@emotion/is-prop-valid@npm:^1.1.0, @emotion/is-prop-valid@npm:^1.2.1":
  version: 1.2.1
  resolution: "@emotion/is-prop-valid@npm:1.2.1"
  dependencies:
    "@emotion/memoize": "npm:^0.8.1"
  checksum: fe231c472d38b3bbe519bcc9a5585cd41c45604147f3a065e333caf0f695d668aa21bc4229e657c1b6ea7398e096899e6ad54662548c73f11f6ba594aebd76a1
  languageName: node
  linkType: hard

"@emotion/memoize@npm:0.7.1":
  version: 0.7.1
  resolution: "@emotion/memoize@npm:0.7.1"
  checksum: fec25e74c3a4af920bfdb0f552c16f648c8f4343d51cb073af85fcec1a382ce041a4e082f458a999dc3599e9d768c0dd28e5accd6066169e01364b270b7036cf
  languageName: node
  linkType: hard

"@emotion/memoize@npm:0.7.4":
  version: 0.7.4
  resolution: "@emotion/memoize@npm:0.7.4"
  checksum: 4e3920d4ec95995657a37beb43d3f4b7d89fed6caa2b173a4c04d10482d089d5c3ea50bbc96618d918b020f26ed6e9c4026bbd45433566576c1f7b056c3271dc
  languageName: node
  linkType: hard

"@emotion/memoize@npm:^0.8.1":
  version: 0.8.1
  resolution: "@emotion/memoize@npm:0.8.1"
  checksum: a19cc01a29fcc97514948eaab4dc34d8272e934466ed87c07f157887406bc318000c69ae6f813a9001c6a225364df04249842a50e692ef7a9873335fbcc141b0
  languageName: node
  linkType: hard

"@emotion/react@npm:^11.11.1":
  version: 11.11.1
  resolution: "@emotion/react@npm:11.11.1"
  dependencies:
    "@babel/runtime": "npm:^7.18.3"
    "@emotion/babel-plugin": "npm:^11.11.0"
    "@emotion/cache": "npm:^11.11.0"
    "@emotion/serialize": "npm:^1.1.2"
    "@emotion/use-insertion-effect-with-fallbacks": "npm:^1.0.1"
    "@emotion/utils": "npm:^1.2.1"
    "@emotion/weak-memoize": "npm:^0.3.1"
    hoist-non-react-statics: "npm:^3.3.1"
  peerDependencies:
    react: ">=16.8.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: dfc140718d0a8051a74e51c379226d9de6b19f6a5dd595fb282ef72f4413695a2d012ba919f1e9eeff761c6659e6f7398da8e0e36eb7997a4fdf54cef88644ae
  languageName: node
  linkType: hard

"@emotion/serialize@npm:^1.1.2":
  version: 1.1.2
  resolution: "@emotion/serialize@npm:1.1.2"
  dependencies:
    "@emotion/hash": "npm:^0.9.1"
    "@emotion/memoize": "npm:^0.8.1"
    "@emotion/unitless": "npm:^0.8.1"
    "@emotion/utils": "npm:^1.2.1"
    csstype: "npm:^3.0.2"
  checksum: 71ed270ee4e9678d6d1c541cb111f8247aef862a28729e511f7036f22b12822e976b5843f5829a1c2a7b959a9728dcac831f39de3084664725eba1345a03b4a0
  languageName: node
  linkType: hard

"@emotion/sheet@npm:^1.2.2":
  version: 1.2.2
  resolution: "@emotion/sheet@npm:1.2.2"
  checksum: cc46b20ef7273dc28de889927ae1498f854be2890905745fcc3154fbbacaa54df1e28c3d89ff3339c2022782c78933f51955bb950d105d5a219576db1eadfb7a
  languageName: node
  linkType: hard

"@emotion/styled@npm:^11.11.0":
  version: 11.11.0
  resolution: "@emotion/styled@npm:11.11.0"
  dependencies:
    "@babel/runtime": "npm:^7.18.3"
    "@emotion/babel-plugin": "npm:^11.11.0"
    "@emotion/is-prop-valid": "npm:^1.2.1"
    "@emotion/serialize": "npm:^1.1.2"
    "@emotion/use-insertion-effect-with-fallbacks": "npm:^1.0.1"
    "@emotion/utils": "npm:^1.2.1"
  peerDependencies:
    "@emotion/react": ^11.0.0-rc.0
    react: ">=16.8.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: ac471a40645ee7bc950378ff9453028078bc2e45a6317f77636e4ed27f7ea61eb549b1efefdc5433640f73246ae5ee212e6c864085dc042b6541b2ffa0e21a49
  languageName: node
  linkType: hard

"@emotion/stylis@npm:^0.8.4":
  version: 0.8.5
  resolution: "@emotion/stylis@npm:0.8.5"
  checksum: ceaa673457f501a393cb52873b2bc34dbe35ef0fb8faa4b943d73ecbbb42bc3cea53b87cbf482038b7b9b1f95859be3d8b58d508422b4d15aec5b62314cc3c1e
  languageName: node
  linkType: hard

"@emotion/unitless@npm:^0.7.4":
  version: 0.7.5
  resolution: "@emotion/unitless@npm:0.7.5"
  checksum: f976e5345b53fae9414a7b2e7a949aa6b52f8bdbcc84458b1ddc0729e77ba1d1dfdff9960e0da60183877873d3a631fa24d9695dd714ed94bcd3ba5196586a6b
  languageName: node
  linkType: hard

"@emotion/unitless@npm:^0.8.1":
  version: 0.8.1
  resolution: "@emotion/unitless@npm:0.8.1"
  checksum: 918f73c46ac0b7161e3c341cc07d651ce87e31ab1695e74b12adb7da6bb98dfbff8c69cf68a4e40d9eb3d820ca055dc1267aeb3007927ce88f98b885bf729b63
  languageName: node
  linkType: hard

"@emotion/use-insertion-effect-with-fallbacks@npm:^1.0.1":
  version: 1.0.1
  resolution: "@emotion/use-insertion-effect-with-fallbacks@npm:1.0.1"
  peerDependencies:
    react: ">=16.8.0"
  checksum: 7d7ead9ba3f615510f550aea67815281ec5a5487de55aafc250f820317afc1fd419bd9e9e27602a0206ec5c152f13dc6130bccad312c1036706c584c65d66ef7
  languageName: node
  linkType: hard

"@emotion/utils@npm:^1.2.1":
  version: 1.2.1
  resolution: "@emotion/utils@npm:1.2.1"
  checksum: 472fa529c64a13edff80aa11698092e8841c1ffb5001c739d84eb9d0fdd6d8e1cd1848669310578ccfa6383b8601132eca54f8749fca40af85d21fdfc9b776c4
  languageName: node
  linkType: hard

"@emotion/weak-memoize@npm:^0.3.1":
  version: 0.3.1
  resolution: "@emotion/weak-memoize@npm:0.3.1"
  checksum: b2be47caa24a8122622ea18cd2d650dbb4f8ad37b636dc41ed420c2e082f7f1e564ecdea68122b546df7f305b159bf5ab9ffee872abd0f052e687428459af594
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.4.0
  resolution: "@eslint-community/eslint-utils@npm:4.4.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.3.0"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 8d70bcdcd8cd279049183aca747d6c2ed7092a5cf0cf5916faac1ef37ffa74f0c245c2a3a3d3b9979d9dfdd4ca59257b4c5621db699d637b847a2c5e02f491c2
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.5.1, @eslint-community/regexpp@npm:^4.6.1":
  version: 4.10.0
  resolution: "@eslint-community/regexpp@npm:4.10.0"
  checksum: 8c36169c815fc5d726078e8c71a5b592957ee60d08c6470f9ce0187c8046af1a00afbda0a065cc40ff18d5d83f82aed9793c6818f7304a74a7488dc9f3ecbd42
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^2.1.4":
  version: 2.1.4
  resolution: "@eslint/eslintrc@npm:2.1.4"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^9.6.0"
    globals: "npm:^13.19.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 7a3b14f4b40fc1a22624c3f84d9f467a3d9ea1ca6e9a372116cb92507e485260359465b58e25bcb6c9981b155416b98c9973ad9b796053fd7b3f776a6946bce8
  languageName: node
  linkType: hard

"@eslint/js@npm:8.55.0":
  version: 8.55.0
  resolution: "@eslint/js@npm:8.55.0"
  checksum: 34b001a95b16501fd64f525b1de3ab0e4c252e5820b74069004934cb13977fc04ba4522a3e8f8074bd6af49da10d3444cd49fa711819f425ad73d6bf46eea82d
  languageName: node
  linkType: hard

"@fast-csv/format@npm:4.3.5":
  version: 4.3.5
  resolution: "@fast-csv/format@npm:4.3.5"
  dependencies:
    "@types/node": "npm:^14.0.1"
    lodash.escaperegexp: "npm:^4.1.2"
    lodash.isboolean: "npm:^3.0.3"
    lodash.isequal: "npm:^4.5.0"
    lodash.isfunction: "npm:^3.0.9"
    lodash.isnil: "npm:^4.0.0"
  checksum: 94fcc061422ad82c7973926acba96c7f0e539d39f8c9c986f4d369ba0bbda535407a5243ddafa0a41a310261205824577b66e74bd0ed81aaaff0d9c33db9e426
  languageName: node
  linkType: hard

"@fast-csv/parse@npm:4.3.6":
  version: 4.3.6
  resolution: "@fast-csv/parse@npm:4.3.6"
  dependencies:
    "@types/node": "npm:^14.0.1"
    lodash.escaperegexp: "npm:^4.1.2"
    lodash.groupby: "npm:^4.6.0"
    lodash.isfunction: "npm:^3.0.9"
    lodash.isnil: "npm:^4.0.0"
    lodash.isundefined: "npm:^3.0.1"
    lodash.uniq: "npm:^4.5.0"
  checksum: 12b338134de8801c895f50f8bb5315b67a6181d5b39d99445be80898633541b06be77a2b14a8395fc51c3f028138e9fb8a2b5bc5258f50c08bef22fd9dd07ee0
  languageName: node
  linkType: hard

"@floating-ui/core@npm:^1.4.2":
  version: 1.5.2
  resolution: "@floating-ui/core@npm:1.5.2"
  dependencies:
    "@floating-ui/utils": "npm:^0.1.3"
  checksum: a1102f8713f8971771fea11d2e0c0c3dbff421db302ca6b4a0b4b9f0f0b082c2baa9b71c9b0ee4b8708bf9d5b91f5e561e189b85b0336f562df6ed414dcdb296
  languageName: node
  linkType: hard

"@floating-ui/core@npm:^1.6.0":
  version: 1.6.7
  resolution: "@floating-ui/core@npm:1.6.7"
  dependencies:
    "@floating-ui/utils": "npm:^0.2.7"
  checksum: e15fbb49830bef39c4ce2b2d00febc0140939c1f86f0441e38e43cbe83456fd05be674812bf747bce425318d8730e3c51c291104115f8637ce7bce2f00446743
  languageName: node
  linkType: hard

"@floating-ui/dom@npm:^1.0.0":
  version: 1.6.10
  resolution: "@floating-ui/dom@npm:1.6.10"
  dependencies:
    "@floating-ui/core": "npm:^1.6.0"
    "@floating-ui/utils": "npm:^0.2.7"
  checksum: c100f5ecb37fc1bea4e551977eae3992f8eba351e6b7f2642e2f84a4abd269406d5a46a14505bc583caf25ddee900a667829244c4eecf1cf60f08c1dabdf3ee9
  languageName: node
  linkType: hard

"@floating-ui/dom@npm:^1.5.1":
  version: 1.5.3
  resolution: "@floating-ui/dom@npm:1.5.3"
  dependencies:
    "@floating-ui/core": "npm:^1.4.2"
    "@floating-ui/utils": "npm:^0.1.3"
  checksum: d2d5ae7a0949c0ebf7fbf97a21612bf94dbd29cb6c847e00588b8e2a5575ade27c47cb19f5d230fc21a571d99aa0c714b301c9221d33921047408c0ed9d91a30
  languageName: node
  linkType: hard

"@floating-ui/react-dom@npm:^2.0.4":
  version: 2.0.4
  resolution: "@floating-ui/react-dom@npm:2.0.4"
  dependencies:
    "@floating-ui/dom": "npm:^1.5.1"
  peerDependencies:
    react: ">=16.8.0"
    react-dom: ">=16.8.0"
  checksum: 4240a718502c797fd2e174cd06dcd7321a6eda9c8966dbaf61864b9e16445e95649a59bfe7c19ee13f68c11f3693724d7970c7e618089a3d3915bd343639cfae
  languageName: node
  linkType: hard

"@floating-ui/react-dom@npm:^2.0.8":
  version: 2.1.1
  resolution: "@floating-ui/react-dom@npm:2.1.1"
  dependencies:
    "@floating-ui/dom": "npm:^1.0.0"
  peerDependencies:
    react: ">=16.8.0"
    react-dom: ">=16.8.0"
  checksum: cafabfb5dd0b25547863520b3bcf6faee7f087d0c3187a8779910a6838d496bf494f237bf1fe883bbfae1a7fcc399611ae52377b696065d8118bd7c1b9c0d253
  languageName: node
  linkType: hard

"@floating-ui/utils@npm:^0.1.3":
  version: 0.1.6
  resolution: "@floating-ui/utils@npm:0.1.6"
  checksum: 450ec4ecc1dd8161b1904d4e1e9d95e653cc06f79af6c3b538b79efb10541d90bcc88646ab3cdffc5b92e00c4804ca727b025d153ad285f42dbbb39aec219ec9
  languageName: node
  linkType: hard

"@floating-ui/utils@npm:^0.2.7":
  version: 0.2.7
  resolution: "@floating-ui/utils@npm:0.2.7"
  checksum: 56b1bb3f73f6ec9aabf9b1fd3dc584e0f2384d319c1a6119050eab102ae6ca8b9b0eed711c2f235ffe035188cbe9727bf36e8dcb54c8bd32176737e4be47efa8
  languageName: node
  linkType: hard

"@formatjs/ecma402-abstract@npm:1.11.4":
  version: 1.11.4
  resolution: "@formatjs/ecma402-abstract@npm:1.11.4"
  dependencies:
    "@formatjs/intl-localematcher": "npm:0.2.25"
    tslib: "npm:^2.1.0"
  checksum: 2bdab58db3fc78cba3bb6b6c4abe98bde20c599ab2834438faaff45d7dbdaa32b1a8a7a898bc4a038e8ae3e2baf9d48ec287be798c2588dc7a32e4cdbfd0039f
  languageName: node
  linkType: hard

"@formatjs/fast-memoize@npm:1.2.1":
  version: 1.2.1
  resolution: "@formatjs/fast-memoize@npm:1.2.1"
  dependencies:
    tslib: "npm:^2.1.0"
  checksum: 7df9e941142be16e5862afe7387926cec44ec136d2c2f9a7e1598cb6c8c23a65e420ed90251ec9b48df083f5473b10d6fbbee2e9fc7233d5bf1f27efffba59a7
  languageName: node
  linkType: hard

"@formatjs/icu-messageformat-parser@npm:2.1.0":
  version: 2.1.0
  resolution: "@formatjs/icu-messageformat-parser@npm:2.1.0"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:1.11.4"
    "@formatjs/icu-skeleton-parser": "npm:1.3.6"
    tslib: "npm:^2.1.0"
  checksum: 74b5bee8ec2d793e2ad6084f53013dc6a5a4205190fe7fb15d36a5169a659fda6ccb1815812acc9d4e02edaaba9656180cad7e74cd9c4484aeb981ae3f5f81d7
  languageName: node
  linkType: hard

"@formatjs/icu-skeleton-parser@npm:1.3.6":
  version: 1.3.6
  resolution: "@formatjs/icu-skeleton-parser@npm:1.3.6"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:1.11.4"
    tslib: "npm:^2.1.0"
  checksum: 01a1b86d208cc8453707d688e5c0d023c7a7ea1d26e59968cdeb2f33976bcb1d22030a4438d88587b32563c651108186cb4ddebf23a047e3b263d47c6d905b2f
  languageName: node
  linkType: hard

"@formatjs/intl-displaynames@npm:5.4.3":
  version: 5.4.3
  resolution: "@formatjs/intl-displaynames@npm:5.4.3"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:1.11.4"
    "@formatjs/intl-localematcher": "npm:0.2.25"
    tslib: "npm:^2.1.0"
  checksum: 6310303769e9a8fea04f5bda13468d22c7b96917c74fe3e9b8527191b48b6a4b79ab919f247c0ca8f90e3a2905adad27e3d355630b29d71934888e18eb0fa290
  languageName: node
  linkType: hard

"@formatjs/intl-listformat@npm:6.5.3":
  version: 6.5.3
  resolution: "@formatjs/intl-listformat@npm:6.5.3"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:1.11.4"
    "@formatjs/intl-localematcher": "npm:0.2.25"
    tslib: "npm:^2.1.0"
  checksum: 2c46d44dda21e27645dd15146d7b2cf071dc35cdfcfd4337c74bbb9925ee36d749254ec838d9f29058919d0e9b18ed4c98987c64106ff26ed352847e326684a8
  languageName: node
  linkType: hard

"@formatjs/intl-localematcher@npm:0.2.25":
  version: 0.2.25
  resolution: "@formatjs/intl-localematcher@npm:0.2.25"
  dependencies:
    tslib: "npm:^2.1.0"
  checksum: ee00ddc23317dc47a58831aaca5112e101d8bb1f38adc0ecfe1a9d7e008d0bb1091519f07e1d7d805b0c1e28f2c3e75f697ae479e22423445814412c7669284c
  languageName: node
  linkType: hard

"@formatjs/intl@npm:2.2.1":
  version: 2.2.1
  resolution: "@formatjs/intl@npm:2.2.1"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:1.11.4"
    "@formatjs/fast-memoize": "npm:1.2.1"
    "@formatjs/icu-messageformat-parser": "npm:2.1.0"
    "@formatjs/intl-displaynames": "npm:5.4.3"
    "@formatjs/intl-listformat": "npm:6.5.3"
    intl-messageformat: "npm:9.13.0"
    tslib: "npm:^2.1.0"
  peerDependencies:
    typescript: ^4.5
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 65a3e112ae777c117385626f8981325fc0363ac0d48f8a31c5fbe1a45ac158cebffab734d2357ede67ae173bb8fea4aca40f4960d834c576378664ae92aa7d6b
  languageName: node
  linkType: hard

"@hookform/resolvers@npm:^3.3.1":
  version: 3.3.2
  resolution: "@hookform/resolvers@npm:3.3.2"
  peerDependencies:
    react-hook-form: ^7.0.0
  checksum: 354930674c708c8b1c974fc944ad8f3980f9fed99e97ae57cb288c23f398b49276458755b45a1014de6dd2a587270377c38057cb16fb65b16c72431b96a34f81
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.11.13":
  version: 0.11.13
  resolution: "@humanwhocodes/config-array@npm:0.11.13"
  dependencies:
    "@humanwhocodes/object-schema": "npm:^2.0.1"
    debug: "npm:^4.1.1"
    minimatch: "npm:^3.0.5"
  checksum: 9f655e1df7efa5a86822cd149ca5cef57240bb8ffd728f0c07cc682cc0a15c6bdce68425fbfd58f9b3e8b16f79b3fd8cb1e96b10c434c9a76f20b2a89f213272
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: e993950e346331e5a32eefb27948ecdee2a2c4ab3f072b8f566cd213ef485dd50a3ca497050608db91006f5479e43f91a439aef68d2a313bd3ded06909c7c5b3
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^2.0.1":
  version: 2.0.1
  resolution: "@humanwhocodes/object-schema@npm:2.0.1"
  checksum: dbddfd0465aecf92ed845ec30d06dba3f7bb2496d544b33b53dac7abc40370c0e46b8787b268d24a366730d5eeb5336ac88967232072a183905ee4abf7df4dab
  languageName: node
  linkType: hard

"@iconify/react@npm:^4.1.1":
  version: 4.1.1
  resolution: "@iconify/react@npm:4.1.1"
  dependencies:
    "@iconify/types": "npm:^2.0.0"
  peerDependencies:
    react: ">=16"
  checksum: 66405fe14dbf7a2f4cc9bae364a1f4a0b2cebdd74c8eb4fe6eb369050e2f34989fccb4d0ac259f8a3873d66eb506bbbd7a12e3251f5ee9023f374250ddc2a6c0
  languageName: node
  linkType: hard

"@iconify/types@npm:^2.0.0":
  version: 2.0.0
  resolution: "@iconify/types@npm:2.0.0"
  checksum: 1b3425ecbc0eef44f23d3f27355ae7ef306d5119c566f013ef1849995b016e1fdcc5af6b74c3bc0554485d70cf5179cb9c1095b14d662a55abcae1148e1a13c9
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: e9ed5fd27c3aec1095e3a16e0c0cf148d1fee55a38665c35f7b3f86a9b5d00d042ddaabc98e8a1cb7463b9378c15f22a94eb35e99469c201453eb8375191f243
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.0, @jridgewell/gen-mapping@npm:^0.3.2":
  version: 0.3.3
  resolution: "@jridgewell/gen-mapping@npm:0.3.3"
  dependencies:
    "@jridgewell/set-array": "npm:^1.0.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.9"
  checksum: 072ace159c39ab85944bdabe017c3de15c5e046a4a4a772045b00ff05e2ebdcfa3840b88ae27e897d473eb4d4845b37be3c78e28910c779f5aeeeae2fb7f0cc2
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.1
  resolution: "@jridgewell/resolve-uri@npm:3.1.1"
  checksum: 64d59df8ae1a4e74315eb1b61e012f1c7bc8aac47a3a1e683f6fe7008eab07bc512a742b7aa7c0405685d1421206de58c9c2e6adbfe23832f8bd69408ffc183e
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.0.1":
  version: 1.1.2
  resolution: "@jridgewell/set-array@npm:1.1.2"
  checksum: 69a84d5980385f396ff60a175f7177af0b8da4ddb81824cb7016a9ef914eee9806c72b6b65942003c63f7983d4f39a5c6c27185bbca88eb4690b62075602e28e
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.4.15
  resolution: "@jridgewell/sourcemap-codec@npm:1.4.15"
  checksum: 89960ac087781b961ad918978975bcdf2051cd1741880469783c42de64239703eab9db5230d776d8e6a09d73bb5e4cb964e07d93ee6e2e7aea5a7d726e865c09
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.17, @jridgewell/trace-mapping@npm:^0.3.9":
  version: 0.3.20
  resolution: "@jridgewell/trace-mapping@npm:0.3.20"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 683117e4e6707ef50c725d6d0ec4234687ff751f36fa46c2b3068931eb6a86b49af374d3030200777666579a992b7470d1bd1c591e9bf64d764dda5295f33093
  languageName: node
  linkType: hard

"@material-ui/core@npm:4.11.3":
  version: 4.11.3
  resolution: "@material-ui/core@npm:4.11.3"
  dependencies:
    "@babel/runtime": "npm:^7.4.4"
    "@material-ui/styles": "npm:^4.11.3"
    "@material-ui/system": "npm:^4.11.3"
    "@material-ui/types": "npm:^5.1.0"
    "@material-ui/utils": "npm:^4.11.2"
    "@types/react-transition-group": "npm:^4.2.0"
    clsx: "npm:^1.0.4"
    hoist-non-react-statics: "npm:^3.3.2"
    popper.js: "npm:1.16.1-lts"
    prop-types: "npm:^15.7.2"
    react-is: "npm:^16.8.0 || ^17.0.0"
    react-transition-group: "npm:^4.4.0"
  peerDependencies:
    "@types/react": ^16.8.6 || ^17.0.0
    react: ^16.8.0 || ^17.0.0
    react-dom: ^16.8.0 || ^17.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: ea4d97b9609970a983b19df31a09c3ae091dc9dfd0994cb16a516afc8bfd74cf7e60a998f28e55ae4c69a77c2f417ca5c33bc6676e4bc5fe8d08f3e1857189d6
  languageName: node
  linkType: hard

"@material-ui/styles@npm:^4.11.3":
  version: 4.11.5
  resolution: "@material-ui/styles@npm:4.11.5"
  dependencies:
    "@babel/runtime": "npm:^7.4.4"
    "@emotion/hash": "npm:^0.8.0"
    "@material-ui/types": "npm:5.1.0"
    "@material-ui/utils": "npm:^4.11.3"
    clsx: "npm:^1.0.4"
    csstype: "npm:^2.5.2"
    hoist-non-react-statics: "npm:^3.3.2"
    jss: "npm:^10.5.1"
    jss-plugin-camel-case: "npm:^10.5.1"
    jss-plugin-default-unit: "npm:^10.5.1"
    jss-plugin-global: "npm:^10.5.1"
    jss-plugin-nested: "npm:^10.5.1"
    jss-plugin-props-sort: "npm:^10.5.1"
    jss-plugin-rule-value-function: "npm:^10.5.1"
    jss-plugin-vendor-prefixer: "npm:^10.5.1"
    prop-types: "npm:^15.7.2"
  peerDependencies:
    "@types/react": ^16.8.6 || ^17.0.0
    react: ^16.8.0 || ^17.0.0
    react-dom: ^16.8.0 || ^17.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: dd6ca6d0a9e82fe098581bcf6d70b6b7e0a7bd21f15c5e66f4ddc4d55d5164b7d979cd0eb8164854b845606f4915210027ceb4dfda4ac1649008539d47d70dc9
  languageName: node
  linkType: hard

"@material-ui/system@npm:^4.11.3":
  version: 4.12.2
  resolution: "@material-ui/system@npm:4.12.2"
  dependencies:
    "@babel/runtime": "npm:^7.4.4"
    "@material-ui/utils": "npm:^4.11.3"
    csstype: "npm:^2.5.2"
    prop-types: "npm:^15.7.2"
  peerDependencies:
    "@types/react": ^16.8.6 || ^17.0.0
    react: ^16.8.0 || ^17.0.0
    react-dom: ^16.8.0 || ^17.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: e4a64e7ab515689053df2f2ba49a8f18f6b4e750446ad5d8f176e853f0a64e26951755424c2daa4277fd454ba395e5e018a98ee55a9b9de2c4492d5f3090385a
  languageName: node
  linkType: hard

"@material-ui/types@npm:5.1.0":
  version: 5.1.0
  resolution: "@material-ui/types@npm:5.1.0"
  peerDependencies:
    "@types/react": "*"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 64ac0938ee6f48011ba596f7422ab0660d9a8d9b4f5f183b39bd63185b1ce724209f65580f0af686d59b524603ffa57418ca2d443b69bec894303f80779c61f8
  languageName: node
  linkType: hard

"@material-ui/types@npm:^5.1.0":
  version: 5.1.8
  resolution: "@material-ui/types@npm:5.1.8"
  peerDependencies:
    "@types/react": "*"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 706060deee86cf15b6a20125fbc660a6db63c46d33cc8c4e926cf18ee58398b8590b2949e1df29e5f47ca07ad40950df8f2ccc8b055ed28001381c513e2848a0
  languageName: node
  linkType: hard

"@material-ui/utils@npm:^4.11.2, @material-ui/utils@npm:^4.11.3":
  version: 4.11.3
  resolution: "@material-ui/utils@npm:4.11.3"
  dependencies:
    "@babel/runtime": "npm:^7.4.4"
    prop-types: "npm:^15.7.2"
    react-is: "npm:^16.8.0 || ^17.0.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0
    react-dom: ^16.8.0 || ^17.0.0
  checksum: fb31d6914c0cd7919da2bc62b3c03302e26f31f7afe5231c80ff93fcf58e4d71cfcafdc042e37c411418fcb25a5e0a5602a586e904b605d3653c4208df5c69df
  languageName: node
  linkType: hard

"@minimal-kit/starter-next-ts@workspace:.":
  version: 0.0.0-use.local
  resolution: "@minimal-kit/starter-next-ts@workspace:."
  dependencies:
    "@auth0/auth0-react": "npm:^2.2.1"
    "@emotion/cache": "npm:^11.10.5"
    "@emotion/react": "npm:^11.11.1"
    "@emotion/styled": "npm:^11.11.0"
    "@hookform/resolvers": "npm:^3.3.1"
    "@iconify/react": "npm:^4.1.1"
    "@mui/icons-material": "npm:^5.14.18"
    "@mui/lab": "npm:^5.0.0-alpha.173"
    "@mui/material": "npm:^5.16.7"
    "@mui/system": "npm:^5.14.18"
    "@mui/x-data-grid": "npm:^6.18.2"
    "@mui/x-date-pickers": "npm:^6.18.5"
    "@next/swc-linux-arm64-musl": "npm:13.4.12"
    "@next/swc-linux-x64-musl": "npm:13.4.12"
    "@react-dnd/invariant": "npm:^4.0.2"
    "@stoneleigh/api-lib": "npm:^6.1.7"
    "@stoneleigh/sdk-core": "npm:^1.1.82"
    "@stoneleigh/sdk-nextjs": "npm:^1.1.95"
    "@svgr/webpack": "npm:^8.1.0"
    "@tanstack/react-query": "npm:^5.8.9"
    "@tanstack/react-query-devtools": "npm:^5.8.9"
    "@tanstack/react-table": "npm:^8.10.7"
    "@types/autosuggest-highlight": "npm:^3.2.0"
    "@types/lodash": "npm:^4.14.199"
    "@types/node": "npm:^20.8.2"
    "@types/nprogress": "npm:^0.2.1"
    "@types/numeral": "npm:^2"
    "@types/react": "npm:^18.2.38"
    "@types/react-dom": "npm:^18.2.17"
    "@types/react-lazy-load-image-component": "npm:^1"
    "@types/stylis": "npm:^4.2.1"
    "@typescript-eslint/eslint-plugin": "npm:^6.7.4"
    "@typescript-eslint/parser": "npm:^6.7.4"
    "@yudiel/react-qr-scanner": "npm:^1.2.3"
    autosuggest-highlight: "npm:^3.3.4"
    axios: "npm:^1.5.1"
    chonky: "npm:^2.3.2"
    cookies-next: "npm:^4.1.0"
    cross-env: "npm:^7.0.3"
    date-fns: "npm:^2.30.0"
    dayjs: "npm:^1.11.10"
    dayjs-plugin-utc: "npm:^0.1.2"
    eslint: "npm:^8.50.0"
    eslint-config-airbnb: "npm:^19.0.4"
    eslint-config-airbnb-typescript: "npm:^17.1.0"
    eslint-config-prettier: "npm:^9.0.0"
    eslint-import-resolver-typescript: "npm:^3.6.1"
    eslint-plugin-import: "npm:^2.28.1"
    eslint-plugin-jsx-a11y: "npm:^6.7.1"
    eslint-plugin-perfectionist: "npm:^2.1.0"
    eslint-plugin-prettier: "npm:^5.0.0"
    eslint-plugin-react: "npm:^7.33.2"
    eslint-plugin-react-hooks: "npm:^4.6.0"
    eslint-plugin-unused-imports: "npm:^3.0.0"
    exceljs: "npm:^4.4.0"
    framer-motion: "npm:^10.16.4"
    highlight.js: "npm:^11.9.0"
    i18next: "npm:^23.7.7"
    i18next-browser-languagedetector: "npm:^7.2.0"
    immutable: "npm:^5.0.0-beta.4"
    lodash: "npm:^4.17.21"
    material-react-table: "npm:^2.0.4"
    next: "npm:^14.0.2"
    notistack: "npm:^3.0.1"
    nprogress: "npm:^0.2.0"
    numeral: "npm:^2.0.6"
    prettier: "npm:^3.0.3"
    react: "npm:^18.2.0"
    react-dom: "npm:^18.2.0"
    react-hook-form: "npm:^7.47.0"
    react-i18next: "npm:^13.5.0"
    react-lazy-load-image-component: "npm:^1.6.0"
    react-quill: "npm:^2.0.0"
    sass: "npm:^1.69.5"
    simplebar-react: "npm:^3.2.4"
    stylis: "npm:^4.3.0"
    stylis-plugin-rtl: "npm:^2.1.1"
    typescript: "npm:^5.2.2"
    yup: "npm:^1.3.2"
  languageName: unknown
  linkType: soft

"@mui/base@npm:5.0.0-beta.40":
  version: 5.0.0-beta.40
  resolution: "@mui/base@npm:5.0.0-beta.40"
  dependencies:
    "@babel/runtime": "npm:^7.23.9"
    "@floating-ui/react-dom": "npm:^2.0.8"
    "@mui/types": "npm:^7.2.14"
    "@mui/utils": "npm:^5.15.14"
    "@popperjs/core": "npm:^2.11.8"
    clsx: "npm:^2.1.0"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: ebee3d9e1136710dcb2af5828acc6bd8d54f6b124785d011585c2665a48dc66e35ccb344d5ebc7fd8bfd776cccb8ea434911f151a62bee193677ee9dc67fc7fc
  languageName: node
  linkType: hard

"@mui/base@npm:^5.0.0-beta.22":
  version: 5.0.0-beta.26
  resolution: "@mui/base@npm:5.0.0-beta.26"
  dependencies:
    "@babel/runtime": "npm:^7.23.4"
    "@floating-ui/react-dom": "npm:^2.0.4"
    "@mui/types": "npm:^7.2.10"
    "@mui/utils": "npm:^5.14.20"
    "@popperjs/core": "npm:^2.11.8"
    clsx: "npm:^2.0.0"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: aac30be4a34e650aa8ae7eabe016e35d2a04d9c9ef52627280d75c3f04f01b02d5f10e46c3dee1d154f9342b5f76bde92ef2df04c0258be89b8de797ee1709eb
  languageName: node
  linkType: hard

"@mui/core-downloads-tracker@npm:^5.16.7":
  version: 5.16.7
  resolution: "@mui/core-downloads-tracker@npm:5.16.7"
  checksum: b65c48ba2bf6bba6435ba9f2d6c33db0c8a85b3ff7599136a9682b72205bec76470ab5ed5e6e625d5bd012ed9bcbc641ed677548be80d217c9fb5d0435567062
  languageName: node
  linkType: hard

"@mui/icons-material@npm:^5.14.18":
  version: 5.14.19
  resolution: "@mui/icons-material@npm:5.14.19"
  dependencies:
    "@babel/runtime": "npm:^7.23.4"
  peerDependencies:
    "@mui/material": ^5.0.0
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 648a44af9a18a39062126d80268d0d4816d408d253d46f097cf8ad525e37dad8301a7727942f4908a15c432de361dabe29de0595f3771cf8acf7da311374441e
  languageName: node
  linkType: hard

"@mui/lab@npm:^5.0.0-alpha.173":
  version: 5.0.0-alpha.173
  resolution: "@mui/lab@npm:5.0.0-alpha.173"
  dependencies:
    "@babel/runtime": "npm:^7.23.9"
    "@mui/base": "npm:5.0.0-beta.40"
    "@mui/system": "npm:^5.16.5"
    "@mui/types": "npm:^7.2.15"
    "@mui/utils": "npm:^5.16.5"
    clsx: "npm:^2.1.0"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@emotion/react": ^11.5.0
    "@emotion/styled": ^11.3.0
    "@mui/material": ">=5.15.0"
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    "@types/react":
      optional: true
  checksum: 6b5bd0665f524d074bba41f76ea70d2938be16dbb7c0360d4d6be6c61d540a4fc06d765ed277ca205a40686212361e528c945b9150caefe40bac483cdec525e0
  languageName: node
  linkType: hard

"@mui/material@npm:^5.16.7":
  version: 5.16.7
  resolution: "@mui/material@npm:5.16.7"
  dependencies:
    "@babel/runtime": "npm:^7.23.9"
    "@mui/core-downloads-tracker": "npm:^5.16.7"
    "@mui/system": "npm:^5.16.7"
    "@mui/types": "npm:^7.2.15"
    "@mui/utils": "npm:^5.16.6"
    "@popperjs/core": "npm:^2.11.8"
    "@types/react-transition-group": "npm:^4.4.10"
    clsx: "npm:^2.1.0"
    csstype: "npm:^3.1.3"
    prop-types: "npm:^15.8.1"
    react-is: "npm:^18.3.1"
    react-transition-group: "npm:^4.4.5"
  peerDependencies:
    "@emotion/react": ^11.5.0
    "@emotion/styled": ^11.3.0
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    "@types/react":
      optional: true
  checksum: 67f118e5a4bc89553d87b1b5bfe8c37b979ee981415dfda39fba0b27d08636be91fa9f270ea674d19f5a23186f53be67e3eb397f03333a7342170f43db8d0058
  languageName: node
  linkType: hard

"@mui/private-theming@npm:^5.14.20":
  version: 5.14.20
  resolution: "@mui/private-theming@npm:5.14.20"
  dependencies:
    "@babel/runtime": "npm:^7.23.4"
    "@mui/utils": "npm:^5.14.20"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 7f4ceac63a298707b3a9d56e96375d6a32aeaee15a16e08d4b0e208dddca7bc0a65f6e1ab9d0b43a945d218ae9b7614c5aefd431c8c81149b0195f7fac01ea52
  languageName: node
  linkType: hard

"@mui/private-theming@npm:^5.16.6":
  version: 5.16.6
  resolution: "@mui/private-theming@npm:5.16.6"
  dependencies:
    "@babel/runtime": "npm:^7.23.9"
    "@mui/utils": "npm:^5.16.6"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 3a7ba9fc5c2f0c8311b5ecadd967e5529ce43c1c5682bfc88d4fe37efdac75e986dd33a45cfecea9561370ad5be659dc32e457e1aff31b861ac93ddd1172a720
  languageName: node
  linkType: hard

"@mui/styled-engine@npm:^5.14.19":
  version: 5.14.20
  resolution: "@mui/styled-engine@npm:5.14.20"
  dependencies:
    "@babel/runtime": "npm:^7.23.4"
    "@emotion/cache": "npm:^11.11.0"
    csstype: "npm:^3.1.2"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@emotion/react": ^11.4.1
    "@emotion/styled": ^11.3.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
  checksum: e5ac3e985b41b2078e1fe2e69e7eff2fce702151ec41d9896f11d66a3fe7ab7763c194fe63c7e4b474acee7c26a3294527e27b3f9dff92939aab3507f88033e1
  languageName: node
  linkType: hard

"@mui/styled-engine@npm:^5.16.6":
  version: 5.16.6
  resolution: "@mui/styled-engine@npm:5.16.6"
  dependencies:
    "@babel/runtime": "npm:^7.23.9"
    "@emotion/cache": "npm:^11.11.0"
    csstype: "npm:^3.1.3"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@emotion/react": ^11.4.1
    "@emotion/styled": ^11.3.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
  checksum: 8e241269c2f95038102f4b6b44eda71f5dd5c2e99c5a5902fe41778f609ae83c75ca8c77f94aaf61f07c7275d0d333e53ae9d9ea7a7a402602ec594045c30be3
  languageName: node
  linkType: hard

"@mui/system@npm:^5.14.18":
  version: 5.14.20
  resolution: "@mui/system@npm:5.14.20"
  dependencies:
    "@babel/runtime": "npm:^7.23.4"
    "@mui/private-theming": "npm:^5.14.20"
    "@mui/styled-engine": "npm:^5.14.19"
    "@mui/types": "npm:^7.2.10"
    "@mui/utils": "npm:^5.14.20"
    clsx: "npm:^2.0.0"
    csstype: "npm:^3.1.2"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@emotion/react": ^11.5.0
    "@emotion/styled": ^11.3.0
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    "@types/react":
      optional: true
  checksum: faaf3c09868e77e2b8870ff1d04c9811056807677aee9aff6b0ed0d89c78bf3beccfc2bf96863aad55d2c5f1dede1ed4296183cbd166ea0b3060654ea34203a1
  languageName: node
  linkType: hard

"@mui/system@npm:^5.16.5, @mui/system@npm:^5.16.7":
  version: 5.16.7
  resolution: "@mui/system@npm:5.16.7"
  dependencies:
    "@babel/runtime": "npm:^7.23.9"
    "@mui/private-theming": "npm:^5.16.6"
    "@mui/styled-engine": "npm:^5.16.6"
    "@mui/types": "npm:^7.2.15"
    "@mui/utils": "npm:^5.16.6"
    clsx: "npm:^2.1.0"
    csstype: "npm:^3.1.3"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@emotion/react": ^11.5.0
    "@emotion/styled": ^11.3.0
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    "@types/react":
      optional: true
  checksum: 736d8a7e22b6682fa791caad485462914f0f395043e168e4a09067a2d4f3e3320a6b33fa764b85244bd648d016ec7b539a6d5dfab45302e45f377c64d9c342ca
  languageName: node
  linkType: hard

"@mui/types@npm:^7.2.10":
  version: 7.2.10
  resolution: "@mui/types@npm:7.2.10"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 83067cd1da9c79f7ee82f171ba718435d8aa12c6e72c4d5d24bee9e310416ccb91cb86cb0502f463e683e9707d90befc5112509fe7af54cacd39f8556b150f9b
  languageName: node
  linkType: hard

"@mui/types@npm:^7.2.14, @mui/types@npm:^7.2.15":
  version: 7.2.15
  resolution: "@mui/types@npm:7.2.15"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 235b4af48a76cbe121e4cf7c4c71c7f9e4eaa458eaff5df2ac8a8f2d4ae93eafa929aba7848a2dfbb3c97dd8d50f4e13828dc17ec136b777bcfdd7d654263996
  languageName: node
  linkType: hard

"@mui/utils@npm:^5.14.16, @mui/utils@npm:^5.14.20":
  version: 5.14.20
  resolution: "@mui/utils@npm:5.14.20"
  dependencies:
    "@babel/runtime": "npm:^7.23.4"
    "@types/prop-types": "npm:^15.7.11"
    prop-types: "npm:^15.8.1"
    react-is: "npm:^18.2.0"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10fcbaef04c34ec45215b7c29f5fda918c37fa05c3d5944c332c309496b6861a0924a0fdbcd078f8849f149105e163c87bcba72fd6665da6d6122017bc208141
  languageName: node
  linkType: hard

"@mui/utils@npm:^5.15.14, @mui/utils@npm:^5.16.5, @mui/utils@npm:^5.16.6":
  version: 5.16.6
  resolution: "@mui/utils@npm:5.16.6"
  dependencies:
    "@babel/runtime": "npm:^7.23.9"
    "@mui/types": "npm:^7.2.15"
    "@types/prop-types": "npm:^15.7.12"
    clsx: "npm:^2.1.1"
    prop-types: "npm:^15.8.1"
    react-is: "npm:^18.3.1"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0
    react: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 214bc3e9fe49579c5aee264477c802e5f5ced3473cafb1ed0aacd63db223e2668a08fb1f7304e70ea0511f68200dd80c3b49cc58050c7b0962228758a003371d
  languageName: node
  linkType: hard

"@mui/x-data-grid@npm:^6.18.2":
  version: 6.18.3
  resolution: "@mui/x-data-grid@npm:6.18.3"
  dependencies:
    "@babel/runtime": "npm:^7.23.2"
    "@mui/utils": "npm:^5.14.16"
    clsx: "npm:^2.0.0"
    prop-types: "npm:^15.8.1"
    reselect: "npm:^4.1.8"
  peerDependencies:
    "@mui/material": ^5.4.1
    "@mui/system": ^5.4.1
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  checksum: 520fe1d172995d9048ca99a80de85c5fe04a912911754590c7bd4c5a9598c75f44109042b77fb37be1f7b7172cd600908166a40df6525267fc87617663ab6a5c
  languageName: node
  linkType: hard

"@mui/x-date-pickers@npm:^6.18.5":
  version: 6.18.5
  resolution: "@mui/x-date-pickers@npm:6.18.5"
  dependencies:
    "@babel/runtime": "npm:^7.23.2"
    "@mui/base": "npm:^5.0.0-beta.22"
    "@mui/utils": "npm:^5.14.16"
    "@types/react-transition-group": "npm:^4.4.8"
    clsx: "npm:^2.0.0"
    prop-types: "npm:^15.8.1"
    react-transition-group: "npm:^4.4.5"
  peerDependencies:
    "@emotion/react": ^11.9.0
    "@emotion/styled": ^11.8.1
    "@mui/material": ^5.8.6
    "@mui/system": ^5.8.0
    date-fns: ^2.25.0
    date-fns-jalali: ^2.13.0-0
    dayjs: ^1.10.7
    luxon: ^3.0.2
    moment: ^2.29.4
    moment-hijri: ^2.1.2
    moment-jalaali: ^0.7.4 || ^0.8.0 || ^0.9.0 || ^0.10.0
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    date-fns:
      optional: true
    date-fns-jalali:
      optional: true
    dayjs:
      optional: true
    luxon:
      optional: true
    moment:
      optional: true
    moment-hijri:
      optional: true
    moment-jalaali:
      optional: true
  checksum: 3137266a8a0fdcece0a5a5d068e86a955e8f72e9132cab274c824372ae694f410e934b8631e7d63e28babd5952a0968dc75e245e1012bb5e124388276fe448fd
  languageName: node
  linkType: hard

"@next/env@npm:14.0.4":
  version: 14.0.4
  resolution: "@next/env@npm:14.0.4"
  checksum: 781eede471730264812d8c744d33eb42da997b4403b06a5b0e58597645152af21f3619a6cb8fc0ba1c1b26d89910c0a8ade6d4242ae13d0b7baa70e3a83cac0f
  languageName: node
  linkType: hard

"@next/swc-darwin-arm64@npm:14.0.4":
  version: 14.0.4
  resolution: "@next/swc-darwin-arm64@npm:14.0.4"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-darwin-x64@npm:14.0.4":
  version: 14.0.4
  resolution: "@next/swc-darwin-x64@npm:14.0.4"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-gnu@npm:14.0.4":
  version: 14.0.4
  resolution: "@next/swc-linux-arm64-gnu@npm:14.0.4"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-musl@npm:13.4.12":
  version: 13.4.12
  resolution: "@next/swc-linux-arm64-musl@npm:13.4.12"
  checksum: 48f3ad676fe4fde9c0bc93f7b926b01cb400b23e5ff450f32dc254c55038ca0b9ce8f4bf6be68203842470b52c4ee7776384e9f43852d20032b91ea7411d64cb
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-musl@npm:14.0.4":
  version: 14.0.4
  resolution: "@next/swc-linux-arm64-musl@npm:14.0.4"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-linux-x64-gnu@npm:14.0.4":
  version: 14.0.4
  resolution: "@next/swc-linux-x64-gnu@npm:14.0.4"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-x64-musl@npm:13.4.12":
  version: 13.4.12
  resolution: "@next/swc-linux-x64-musl@npm:13.4.12"
  checksum: 197bc396f7e1cfe7a18f8a1ae4b2a22cc88ad49716f1e61746053436df2714ad140f483b789913ff037017643a7e80b3235f83aa2fbe6db446c7dabe9e1067ca
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-linux-x64-musl@npm:14.0.4":
  version: 14.0.4
  resolution: "@next/swc-linux-x64-musl@npm:14.0.4"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-win32-arm64-msvc@npm:14.0.4":
  version: 14.0.4
  resolution: "@next/swc-win32-arm64-msvc@npm:14.0.4"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-win32-ia32-msvc@npm:14.0.4":
  version: 14.0.4
  resolution: "@next/swc-win32-ia32-msvc@npm:14.0.4"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@next/swc-win32-x64-msvc@npm:14.0.4":
  version: 14.0.4
  resolution: "@next/swc-win32-x64-msvc@npm:14.0.4"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 6ab2a9b8a1d67b067922c36f259e3b3dfd6b97b219c540877a4944549a4d49ea5ceba5663905ab5289682f1f3c15ff441d02f0447f620a42e1cb5e1937174d4b
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3, @nodelib/fs.walk@npm:^1.2.8":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 40033e33e96e97d77fba5a238e4bba4487b8284678906a9f616b5579ddaf868a18874c0054a75402c9fbaaa033a25ceae093af58c9c30278e35c23c9479e79b0
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^2.0.0":
  version: 2.2.0
  resolution: "@npmcli/agent@npm:2.2.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.1"
  checksum: 822ea077553cd9cfc5cbd6d92380b0950fcb054a7027cd1b63a33bd0cbb16b0c6626ea75d95ec0e804643c8904472d3361d2da8c2444b1fb02a9b525d9c07c41
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^3.1.0":
  version: 3.1.0
  resolution: "@npmcli/fs@npm:3.1.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: f3a7ab3a31de65e42aeb6ed03ed035ef123d2de7af4deb9d4a003d27acc8618b57d9fb9d259fe6c28ca538032a028f37337264388ba27d26d37fff7dde22476e
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 115e8ceeec6bc69dff2048b35c0ab4f8bbee12d8bb6c1f4af758604586d802b6e669dcb02dda61d078de42c2b4ddce41b3d9e726d7daa6b4b850f4adbf7333ff
  languageName: node
  linkType: hard

"@pkgr/utils@npm:^2.4.2":
  version: 2.4.2
  resolution: "@pkgr/utils@npm:2.4.2"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    fast-glob: "npm:^3.3.0"
    is-glob: "npm:^4.0.3"
    open: "npm:^9.1.0"
    picocolors: "npm:^1.0.0"
    tslib: "npm:^2.6.0"
  checksum: f0b0b305a83bd65fac5637d28ad3e33f19194043e03ceef6b4e13d260bfa2678b73df76dc56ed906469ffe0494d4bd214e6b92ca80684f38547982edf982dd15
  languageName: node
  linkType: hard

"@popperjs/core@npm:^2.11.8":
  version: 2.11.8
  resolution: "@popperjs/core@npm:2.11.8"
  checksum: ddd16090cde777aaf102940f05d0274602079a95ad9805bd20bc55dcc7c3a2ba1b99dd5c73e5cc2753c3d31250ca52a67d58059459d7d27debb983a9f552936c
  languageName: node
  linkType: hard

"@react-dnd/asap@npm:^4.0.0":
  version: 4.0.1
  resolution: "@react-dnd/asap@npm:4.0.1"
  checksum: 4fd8912de1c689f79be653b09ee561ebf1f67878cc69ea5ce54cd30e462853ca581121ef8e632864427cfe5ad206539bb5b59fce08fb471cec16be30886380dd
  languageName: node
  linkType: hard

"@react-dnd/invariant@npm:^2.0.0":
  version: 2.0.0
  resolution: "@react-dnd/invariant@npm:2.0.0"
  checksum: ef1e989920d70b15c80dccb01af9b598081d76993311aa22d2e9a3ec41d10a88540eeec4b4de7a8b2a2ea52dfc3495ab45e39192c2d27795a9258bd6b79d000e
  languageName: node
  linkType: hard

"@react-dnd/invariant@npm:^4.0.2":
  version: 4.0.2
  resolution: "@react-dnd/invariant@npm:4.0.2"
  checksum: b638e9643e6e93da03ef463be3c1b92055daadc391fc08e4ce8639ef8c7738f91058ec83ee52a0d0df0d3a6dd2811a7703e1450737708f043c2e909c0a99dd31
  languageName: node
  linkType: hard

"@react-dnd/shallowequal@npm:^2.0.0":
  version: 2.0.0
  resolution: "@react-dnd/shallowequal@npm:2.0.0"
  checksum: b5bbdc795d65945bb7ba2322bed5cf8d4c6fe91dced98c3b10e3d16822c438f558751135ff296f8d1aa1eaa9d0037dacab2b522ca5eb812175123b9996966dcb
  languageName: node
  linkType: hard

"@reduxjs/toolkit@npm:^1.5.0, @reduxjs/toolkit@npm:^1.9.7":
  version: 1.9.7
  resolution: "@reduxjs/toolkit@npm:1.9.7"
  dependencies:
    immer: "npm:^9.0.21"
    redux: "npm:^4.2.1"
    redux-thunk: "npm:^2.4.2"
    reselect: "npm:^4.1.8"
  peerDependencies:
    react: ^16.9.0 || ^17.0.0 || ^18
    react-redux: ^7.2.1 || ^8.0.2
  peerDependenciesMeta:
    react:
      optional: true
    react-redux:
      optional: true
  checksum: 11c718270bb378e5b26e172eb84cc549d6f263748b6f330b07ee9c366c6474b013fd410e5b2f65a5742e73b7873a3ac14e06cae4bb01480ba03b423c4fd92583
  languageName: node
  linkType: hard

"@stoneleigh/api-lib@npm:6.1.1":
  version: 6.1.1
  resolution: "@stoneleigh/api-lib@npm:6.1.1"
  dependencies:
    react: "npm:18.2.0"
    react-query: "npm:^3.39.3"
    swr: "npm:^1.3.0"
  dependenciesMeta:
    react:
      optional: true
    react-query:
      optional: true
    swr:
      optional: true
  checksum: fc13ec24a0f2d0573ac57a844b24e89a296bb4f66b9602686ef61f466a97cff149ff2ac2aa028225bbfe401d3421f904223e3bd1bc7037c48c9061d852964c3d
  languageName: node
  linkType: hard

"@stoneleigh/api-lib@npm:^6.1.7":
  version: 6.1.7
  resolution: "@stoneleigh/api-lib@npm:6.1.7"
  dependencies:
    "@tanstack/react-query": "npm:^5.8.9"
    "@tanstack/react-query-devtools": "npm:^5.8.9"
    react: "npm:18.2.0"
  dependenciesMeta:
    "@tanstack/react-query":
      optional: true
    "@tanstack/react-query-devtools":
      optional: true
    react:
      optional: true
  checksum: d0c110fd37c68c2ffeb0d82b3cf365f455bebb8bd7e5ae11b3fb67a2eec1bcccf3db3b7233215c6238a87733a4014e746438b924480189672896aae15b93f552
  languageName: node
  linkType: hard

"@stoneleigh/sdk-core@npm:^1.1.82":
  version: 1.1.85
  resolution: "@stoneleigh/sdk-core@npm:1.1.85"
  dependencies:
    "@reduxjs/toolkit": "npm:^1.9.7"
    "@stoneleigh/api-lib": "npm:6.1.1"
    axios: "npm:^1.6.1"
    cookie: "npm:^0.5.0"
    cookies-next: "npm:^4.1.0"
    dayjs: "npm:^1.11.10"
    dayjs-plugin-utc: "npm:^0.1.2"
    immutable: "npm:4.3.4"
    react-animate-height: "npm:^2.1.2"
    react-redux: "npm:^8.1.3"
    redux: "npm:^4.2.1"
    redux-persist: "npm:^6.0.0"
    rxjs: "npm:7.8.0"
  peerDependencies:
    react: ^18.1.0
    react-dom: ^18.1.0
  checksum: 8ac06d3ccb345aef1cff02074c6898b7279b059e51a877e9740d80e353a9c923144093dd29e53a40cccfe5cce6f5b0c3bb1bf8666c48cc543c258fbcf9962365
  languageName: node
  linkType: hard

"@stoneleigh/sdk-nextjs@npm:^1.1.95":
  version: 1.1.95
  resolution: "@stoneleigh/sdk-nextjs@npm:1.1.95"
  peerDependencies:
    next: ^14.0.2
  checksum: b9d13a1da89d47cdb5af35aec7ba97d0d2d17bdc56b6447d2121fc1c877fc76ee502c46991f19806acdd3d960b88fc32295642b2c443fcba4ed33bb64a347a47
  languageName: node
  linkType: hard

"@svgr/babel-plugin-add-jsx-attribute@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-add-jsx-attribute@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3fc8e35d16f5abe0af5efe5851f27581225ac405d6a1ca44cda0df064cddfcc29a428c48c2e4bef6cebf627c9ac2f652a096030edb02cf5a120ce28d3c234710
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-attribute@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-attribute@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ff992893c6c4ac802713ba3a97c13be34e62e6d981c813af40daabcd676df68a72a61bd1e692bb1eda3587f1b1d700ea462222ae2153bb0f46886632d4f88d08
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-empty-expression@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-empty-expression@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0fb691b63a21bac00da3aa2dccec50d0d5a5b347ff408d60803b84410d8af168f2656e4ba1ee1f24dab0ae4e4af77901f2928752bb0434c1f6788133ec599ec8
  languageName: node
  linkType: hard

"@svgr/babel-plugin-replace-jsx-attribute-value@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-replace-jsx-attribute-value@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1edda65ef4f4dd8f021143c8ec276a08f6baa6f733b8e8ee2e7775597bf6b97afb47fdeefd579d6ae6c959fe2e634f55cd61d99377631212228c8cfb351b8921
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-dynamic-title@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-svg-dynamic-title@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 876cec891488992e6a9aebb8155e2bea4ec461b4718c51de36e988e00e271c6d9d01ef6be17b9effd44b2b3d7db0b41c161a5904a46ae6f38b26b387ad7f3709
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-em-dimensions@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-svg-em-dimensions@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: be0e2d391164428327d9ec469a52cea7d93189c6b0e2c290999e048f597d777852f701c64dca44cd45b31ed14a7f859520326e2e4ad7c3a4545d0aa235bc7e9a
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-react-native-svg@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/babel-plugin-transform-react-native-svg@npm:8.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 85b434a57572f53bd2b9f0606f253e1fcf57b4a8c554ec3f2d43ed17f50d8cae200cb3aaf1ec9d626e1456e8b135dce530ae047eb0bed6d4bf98a752d6640459
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-svg-component@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-transform-svg-component@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 86ca139c0be0e7df05f103c5f10874387ada1434ca0286584ba9cd367c259d74bf9c86700b856449f46cf674bd6f0cf18f8f034f6d3f0e2ce5e5435c25dbff4b
  languageName: node
  linkType: hard

"@svgr/babel-preset@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/babel-preset@npm:8.1.0"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute": "npm:8.0.0"
    "@svgr/babel-plugin-remove-jsx-attribute": "npm:8.0.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression": "npm:8.0.0"
    "@svgr/babel-plugin-replace-jsx-attribute-value": "npm:8.0.0"
    "@svgr/babel-plugin-svg-dynamic-title": "npm:8.0.0"
    "@svgr/babel-plugin-svg-em-dimensions": "npm:8.0.0"
    "@svgr/babel-plugin-transform-react-native-svg": "npm:8.1.0"
    "@svgr/babel-plugin-transform-svg-component": "npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3a67930f080b8891e1e8e2595716b879c944d253112bae763dce59807ba23454d162216c8d66a0a0e3d4f38a649ecd6c387e545d1e1261dd69a68e9a3392ee08
  languageName: node
  linkType: hard

"@svgr/core@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/core@npm:8.1.0"
  dependencies:
    "@babel/core": "npm:^7.21.3"
    "@svgr/babel-preset": "npm:8.1.0"
    camelcase: "npm:^6.2.0"
    cosmiconfig: "npm:^8.1.3"
    snake-case: "npm:^3.0.4"
  checksum: bc98cd5fc349ab9dcf0c13c2279164726d45878cdac8999090765379c6e897a1b24aca641c12a3c33f578d06f7a09252fb090962a4695c753fb02b627a56bfe6
  languageName: node
  linkType: hard

"@svgr/hast-util-to-babel-ast@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/hast-util-to-babel-ast@npm:8.0.0"
  dependencies:
    "@babel/types": "npm:^7.21.3"
    entities: "npm:^4.4.0"
  checksum: 243aa9c92d66aa3f1fc82851fe1fa376808a08fcc02719fed38ebfb4e25cf3e3c1282c185300c29953d047c36acb9e3ac588d46b0af55a3b7a5186a6badec8a9
  languageName: node
  linkType: hard

"@svgr/plugin-jsx@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/plugin-jsx@npm:8.1.0"
  dependencies:
    "@babel/core": "npm:^7.21.3"
    "@svgr/babel-preset": "npm:8.1.0"
    "@svgr/hast-util-to-babel-ast": "npm:8.0.0"
    svg-parser: "npm:^2.0.4"
  peerDependencies:
    "@svgr/core": "*"
  checksum: 0418a9780753d3544912ee2dad5d2cf8d12e1ba74df8053651b3886aeda54d5f0f7d2dece0af5e0d838332c4f139a57f0dabaa3ca1afa4d1a765efce6a7656f2
  languageName: node
  linkType: hard

"@svgr/plugin-svgo@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/plugin-svgo@npm:8.1.0"
  dependencies:
    cosmiconfig: "npm:^8.1.3"
    deepmerge: "npm:^4.3.1"
    svgo: "npm:^3.0.2"
  peerDependencies:
    "@svgr/core": "*"
  checksum: 59d9d214cebaacca9ca71a561f463d8b7e5a68ca9443e4792a42d903acd52259b1790c0680bc6afecc3f00a255a6cbd7ea278a9f625bac443620ea58a590c2d0
  languageName: node
  linkType: hard

"@svgr/webpack@npm:^8.1.0":
  version: 8.1.0
  resolution: "@svgr/webpack@npm:8.1.0"
  dependencies:
    "@babel/core": "npm:^7.21.3"
    "@babel/plugin-transform-react-constant-elements": "npm:^7.21.3"
    "@babel/preset-env": "npm:^7.20.2"
    "@babel/preset-react": "npm:^7.18.6"
    "@babel/preset-typescript": "npm:^7.21.0"
    "@svgr/core": "npm:8.1.0"
    "@svgr/plugin-jsx": "npm:8.1.0"
    "@svgr/plugin-svgo": "npm:8.1.0"
  checksum: c6eec5b0cf2fb2ecd3a7a362d272eda35330b17c76802a3481f499b5d07ff8f87b31d2571043bff399b051a1767b1e2e499dbf186104d1c06d76f9f1535fac01
  languageName: node
  linkType: hard

"@swc/helpers@npm:0.5.2":
  version: 0.5.2
  resolution: "@swc/helpers@npm:0.5.2"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 3a3b179b3369acd26c5da89a0e779c756ae5231eb18a5507524c7abf955f488d34d86649f5b8417a0e19879688470d06319f5cfca2273d6d6b2046950e0d79af
  languageName: node
  linkType: hard

"@tanstack/match-sorter-utils@npm:8.8.4":
  version: 8.8.4
  resolution: "@tanstack/match-sorter-utils@npm:8.8.4"
  dependencies:
    remove-accents: "npm:0.4.2"
  checksum: 25a109c72414ce439dba914f0756b52ef29e1d0a008f9c0f3f79af24cdf8a129500c9304a9877f5c9cd2713e394915297cfc07c249b8e1f78bd2f026643de0b8
  languageName: node
  linkType: hard

"@tanstack/query-core@npm:5.12.1":
  version: 5.12.1
  resolution: "@tanstack/query-core@npm:5.12.1"
  checksum: 64b06563f8bb6abba2cd2fc8fb99b963da7dba5fa837d5bb6ebc0e0a01b5fa946be6e365bddabf895d533a9196611bb12a007c1d2fff4017b0226b1c06e822dc
  languageName: node
  linkType: hard

"@tanstack/query-devtools@npm:5.13.3":
  version: 5.13.3
  resolution: "@tanstack/query-devtools@npm:5.13.3"
  checksum: 67b09dea50042a07d3c217d439e1125dea22b55729cd45e6bef37082a82963b4a54ba58d6303cf9ace37445e378ebfb52d0cc3cab010538a8f3d2586ffa4f722
  languageName: node
  linkType: hard

"@tanstack/react-query-devtools@npm:^5.8.9":
  version: 5.13.3
  resolution: "@tanstack/react-query-devtools@npm:5.13.3"
  dependencies:
    "@tanstack/query-devtools": "npm:5.13.3"
  peerDependencies:
    "@tanstack/react-query": ^5.12.2
    react: ^18.0.0
  checksum: 4c35a9ed48e0217b029ebfaa841fd3d03021bc31f7ccfcddbadf31abc08fec1c5295014de0d78577aa0cdef4aeab7c7ca1011e073cdb3371b7d8d7ecf3c7ec9d
  languageName: node
  linkType: hard

"@tanstack/react-query@npm:^5.8.9":
  version: 5.12.2
  resolution: "@tanstack/react-query@npm:5.12.2"
  dependencies:
    "@tanstack/query-core": "npm:5.12.1"
  peerDependencies:
    react: ^18.0.0
  checksum: bf954750cff2b38846136ef6fa3d79b6861eb886af80ff577f644ebdbe70840603bed4bb073c9cde34c4b9e61cadbb495dfea17344e5ec8c49bc1c7fcaf2f155
  languageName: node
  linkType: hard

"@tanstack/react-table@npm:8.10.7, @tanstack/react-table@npm:^8.10.7":
  version: 8.10.7
  resolution: "@tanstack/react-table@npm:8.10.7"
  dependencies:
    "@tanstack/table-core": "npm:8.10.7"
  peerDependencies:
    react: ">=16"
    react-dom: ">=16"
  checksum: ed4eaa648854ce95264c66fd7cc0effafee35d4c34fc2d04b047be0bd48b70695e3e69b2798a4d8b4797de36bf3c0aa24973255d434d7fd4f95856760eb16031
  languageName: node
  linkType: hard

"@tanstack/react-virtual@npm:3.0.1":
  version: 3.0.1
  resolution: "@tanstack/react-virtual@npm:3.0.1"
  dependencies:
    "@tanstack/virtual-core": "npm:3.0.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: c923a524964a5d1da68464edff26267b5c5f84809ed6f2001f6a3d36219bba97c779633d801e4801b1653969a24b714b8262593355a3e7036fb510d8ac40963d
  languageName: node
  linkType: hard

"@tanstack/table-core@npm:8.10.7":
  version: 8.10.7
  resolution: "@tanstack/table-core@npm:8.10.7"
  checksum: aa4150e03e4efe3d7a52485debbd7dc297e487a1ba0d1e1171ae84907cde8ea7e88794f812226542266ead8b7a2017c433323426eb50a1d5bc57a8b493fd5af2
  languageName: node
  linkType: hard

"@tanstack/virtual-core@npm:3.0.0":
  version: 3.0.0
  resolution: "@tanstack/virtual-core@npm:3.0.0"
  checksum: 667941444b70773a8fc37b907718fcdbcf90b15660f427b06d6c140b72eea636d8609a8ba5ee3d2cb363066a8085fbf5b804fd08d5cfefd432cf1a14fa7c2355
  languageName: node
  linkType: hard

"@trysound/sax@npm:0.2.0":
  version: 0.2.0
  resolution: "@trysound/sax@npm:0.2.0"
  checksum: 7379713eca480ac0d9b6c7b063e06b00a7eac57092354556c81027066eb65b61ea141a69d0cc2e15d32e05b2834d4c9c2184793a5e36bbf5daf05ee5676af18c
  languageName: node
  linkType: hard

"@types/autosuggest-highlight@npm:^3.2.0":
  version: 3.2.3
  resolution: "@types/autosuggest-highlight@npm:3.2.3"
  checksum: de115f7a838482e5af750f2b51249fcbe1f3473d1c31119731d392dc1dfb119d6b7275f97db737cd4d01b626d3c32d88554377ba4aa55fac8bbae73ab7881756
  languageName: node
  linkType: hard

"@types/classnames@npm:^2.2.11":
  version: 2.3.0
  resolution: "@types/classnames@npm:2.3.0"
  dependencies:
    classnames: "npm:*"
  checksum: e1bd6de60fc5e4ead601838078b10f378d6a66ad21112922856de59a1f450b1361f597aa3ca34f2cc33bc030f950112872fddfde43c5189bec89737a8b237f24
  languageName: node
  linkType: hard

"@types/cookie@npm:^0.4.1":
  version: 0.4.1
  resolution: "@types/cookie@npm:0.4.1"
  checksum: 427c9220217d3d74f3e5d53d68cd39502f3bbebdb1af4ecf0d05076bcbe9ddab299ad6369fe0f517389296ba4ca49ddf9a8c22f68e5e9eb8ae6d0076cfab90b2
  languageName: node
  linkType: hard

"@types/fuzzy-search@npm:^2.1.0":
  version: 2.1.5
  resolution: "@types/fuzzy-search@npm:2.1.5"
  checksum: 451560afabc36a3c5a52fc189f4a5704ab8c031cb99d4f7f7d4d51aa48f595532f7829c04d8894c1983335644cd20e987ca376e83e5438170de2f59ee5edf3af
  languageName: node
  linkType: hard

"@types/hoist-non-react-statics@npm:^3.3.0, @types/hoist-non-react-statics@npm:^3.3.1":
  version: 3.3.5
  resolution: "@types/hoist-non-react-statics@npm:3.3.5"
  dependencies:
    "@types/react": "npm:*"
    hoist-non-react-statics: "npm:^3.3.0"
  checksum: b645b062a20cce6ab1245ada8274051d8e2e0b2ee5c6bd58215281d0ec6dae2f26631af4e2e7c8abe238cdcee73fcaededc429eef569e70908f82d0cc0ea31d7
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.12":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 1a3c3e06236e4c4aab89499c428d585527ce50c24fe8259e8b3926d3df4cfbbbcf306cfc73ddfb66cbafc973116efd15967020b0f738f63e09e64c7d260519e7
  languageName: node
  linkType: hard

"@types/json5@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/json5@npm:0.0.29"
  checksum: 4e5aed58cabb2bbf6f725da13421aa50a49abb6bc17bfab6c31b8774b073fa7b50d557c61f961a09a85f6056151190f8ac95f13f5b48136ba5841f7d4484ec56
  languageName: node
  linkType: hard

"@types/lodash-es@npm:^4.17.6":
  version: 4.17.12
  resolution: "@types/lodash-es@npm:4.17.12"
  dependencies:
    "@types/lodash": "npm:*"
  checksum: 56b9a433348b11c31051c6fa9028540a033a08fb80b400c589d740446c19444d73b217cf1471d4036448ef686a83e8cf2a35d1fadcb3f2105f26701f94aebb07
  languageName: node
  linkType: hard

"@types/lodash@npm:*, @types/lodash@npm:^4.14.199":
  version: 4.14.202
  resolution: "@types/lodash@npm:4.14.202"
  checksum: 1bb9760a5b1dda120132c4b987330d67979c95dbc22612678682cd61b00302e190f4207228f3728580059cdab5582362262e3819aea59960c1017bd2b9fb26f6
  languageName: node
  linkType: hard

"@types/memoizee@npm:^0.4.5":
  version: 0.4.11
  resolution: "@types/memoizee@npm:0.4.11"
  checksum: 1ecc8b617738a97d9063fbd9aa9bf366ec984d94100ba570392cd9fab632a2066b925d8c8b98139a51931fc081e5e893770b63a7678abe991f48d4f35d897c0b
  languageName: node
  linkType: hard

"@types/node@npm:^14.0.1":
  version: 14.18.63
  resolution: "@types/node@npm:14.18.63"
  checksum: 82a7775898c2ea6db0b610a463512206fb2c7adc1af482c7eb44b99d94375fff51c74f67ae75a63c5532971159f30c866a4d308000624ef02fd9a7175e277019
  languageName: node
  linkType: hard

"@types/node@npm:^16.10.2":
  version: 16.18.68
  resolution: "@types/node@npm:16.18.68"
  checksum: a7df7d5761ec339aff812929fa7ab3f1b3951b2372fda7eeedf8bba134c52cbea9fb721ae99f3f1427f782094bffa0d85509b15bfe426858dd04f12c71d55988
  languageName: node
  linkType: hard

"@types/node@npm:^20.8.2":
  version: 20.10.4
  resolution: "@types/node@npm:20.10.4"
  dependencies:
    undici-types: "npm:~5.26.4"
  checksum: c10c1dd13f5c2341ad866777dc32946538a99e1ebd203ae127730814b8e5fa4aedfbcb01cb3e24a5466f1af64bcdfa16e7de6e745ff098fff0942aa779b7fe03
  languageName: node
  linkType: hard

"@types/nprogress@npm:^0.2.1":
  version: 0.2.3
  resolution: "@types/nprogress@npm:0.2.3"
  checksum: 2bd96ba1167f548e8f898ae8a790b6501a5422217621c9b8ea75962f3e8473f0e371058b79d83e04e80a6bfee2d66fc6612cdb28296648007b35d3c2a6d64e5c
  languageName: node
  linkType: hard

"@types/numeral@npm:^2":
  version: 2.0.5
  resolution: "@types/numeral@npm:2.0.5"
  checksum: 0ec9d195248da1865bae5f1939bf93d2464fd2ddc5171444266c66092efb7335031852005842ec4449e1ae5742cd9717cc3af117d6f8cd6e51f083bc08f35239
  languageName: node
  linkType: hard

"@types/parse-json@npm:^4.0.0":
  version: 4.0.2
  resolution: "@types/parse-json@npm:4.0.2"
  checksum: 5bf62eec37c332ad10059252fc0dab7e7da730764869c980b0714777ad3d065e490627be9f40fc52f238ffa3ac4199b19de4127196910576c2fe34dd47c7a470
  languageName: node
  linkType: hard

"@types/prop-types@npm:*, @types/prop-types@npm:^15.7.11":
  version: 15.7.11
  resolution: "@types/prop-types@npm:15.7.11"
  checksum: 7519ff11d06fbf6b275029fe03fff9ec377b4cb6e864cac34d87d7146c7f5a7560fd164bdc1d2dbe00b60c43713631251af1fd3d34d46c69cd354602bc0c7c54
  languageName: node
  linkType: hard

"@types/prop-types@npm:^15.7.12":
  version: 15.7.12
  resolution: "@types/prop-types@npm:15.7.12"
  checksum: ac16cc3d0a84431ffa5cfdf89579ad1e2269549f32ce0c769321fdd078f84db4fbe1b461ed5a1a496caf09e637c0e367d600c541435716a55b1d9713f5035dfe
  languageName: node
  linkType: hard

"@types/quill@npm:^1.3.10":
  version: 1.3.10
  resolution: "@types/quill@npm:1.3.10"
  dependencies:
    parchment: "npm:^1.1.2"
  checksum: 146ee94088092097aee4b5f1f4460a702f86bf8986e22c4767f0b1beeb510ab9bf20fba0af3067f02543b10c1e55d086b1bdd0e41e6299a3041fb4263c2d2ed2
  languageName: node
  linkType: hard

"@types/react-dom@npm:^18.2.17":
  version: 18.2.17
  resolution: "@types/react-dom@npm:18.2.17"
  dependencies:
    "@types/react": "npm:*"
  checksum: fe0dbb3224b48515da8fe25559e3777d756a27c3f22903f0b1b020de8d68bd57eb1f0af62b52ee65d9632637950afed8cbad24d158c4f3d910d083d49bd73fba
  languageName: node
  linkType: hard

"@types/react-lazy-load-image-component@npm:^1":
  version: 1.6.3
  resolution: "@types/react-lazy-load-image-component@npm:1.6.3"
  dependencies:
    "@types/react": "npm:*"
    csstype: "npm:^3.0.2"
  checksum: ccad5b7c5d801cae2bf34f2d9528d6e0dd615f4c675d6092ce9ab9296d43e8df171e99d65ca28897f98345c42c0bb37f3a69f4891f4be81af26eaae02c4f2031
  languageName: node
  linkType: hard

"@types/react-redux@npm:^7.1.16, @types/react-redux@npm:^7.1.20":
  version: 7.1.33
  resolution: "@types/react-redux@npm:7.1.33"
  dependencies:
    "@types/hoist-non-react-statics": "npm:^3.3.0"
    "@types/react": "npm:*"
    hoist-non-react-statics: "npm:^3.3.0"
    redux: "npm:^4.0.0"
  checksum: 65f4e0a3f0e532bbbe44ae6522d1fce91bfcb3bacc90904c35d3f819e77932cc489b6945988acb4a2320f6e78c57dd1c149556aa241a68efc51de15a2cd73fc0
  languageName: node
  linkType: hard

"@types/react-transition-group@npm:^4.2.0, @types/react-transition-group@npm:^4.4.8":
  version: 4.4.10
  resolution: "@types/react-transition-group@npm:4.4.10"
  dependencies:
    "@types/react": "npm:*"
  checksum: b429f3bd54d9aea6c0395943ce2dda6b76fb458e902365bd91fd99bf72064fb5d59e2b74e78d10f2871908501d350da63e230d81bda2b616c967cab8dc51bd16
  languageName: node
  linkType: hard

"@types/react-transition-group@npm:^4.4.10":
  version: 4.4.11
  resolution: "@types/react-transition-group@npm:4.4.11"
  dependencies:
    "@types/react": "npm:*"
  checksum: a7f4de6e5f57d9fcdea027e22873c633f96a803c96d422db8b99a45c36a9cceb7882d152136bbc31c7158fc1827e37aea5070d369724bb71dd11b5687332bc4d
  languageName: node
  linkType: hard

"@types/react-virtualized-auto-sizer@npm:^1.0.0":
  version: 1.0.4
  resolution: "@types/react-virtualized-auto-sizer@npm:1.0.4"
  dependencies:
    "@types/react": "npm:*"
  checksum: e0d41ac6cf0f48dfef45c0cd70146578f42ad83dad90911aa0dfe72b0e1ea62ca9ff56d4f43f322ec6dae2e7cb4d025aefe0e85241f7782c249aeface1050512
  languageName: node
  linkType: hard

"@types/react-window@npm:^1.8.2":
  version: 1.8.8
  resolution: "@types/react-window@npm:1.8.8"
  dependencies:
    "@types/react": "npm:*"
  checksum: 79b70b7c33161efb14bf69115792843de8e038594136a8373cfbbcc4066c49fd611dd2d3592a9a81d19d21c075bf14e5e73a64f4d9ad32e45d4d5493f5f53918
  languageName: node
  linkType: hard

"@types/react@npm:*, @types/react@npm:^18.2.38":
  version: 18.2.42
  resolution: "@types/react@npm:18.2.42"
  dependencies:
    "@types/prop-types": "npm:*"
    "@types/scheduler": "npm:*"
    csstype: "npm:^3.0.2"
  checksum: b6ee1873ba551ca7bf87cefff00a615aa4322cd68d425858a2e09be260d8037d7fc68865739d2b05cc88cefa7acd009afdaea43e9856fc6302b322cc8c19464e
  languageName: node
  linkType: hard

"@types/react@npm:16 || 17 || 18":
  version: 18.2.47
  resolution: "@types/react@npm:18.2.47"
  dependencies:
    "@types/prop-types": "npm:*"
    "@types/scheduler": "npm:*"
    csstype: "npm:^3.0.2"
  checksum: 0a98c2ef8303909f78c973ac9731cb671f3a0b96bc5213b538d1a50cbaae6e51b6befd64845a9cb95af8528767315d5bd99a85608eb716c020393c7d33a9b477
  languageName: node
  linkType: hard

"@types/react@npm:^17.0.3":
  version: 17.0.74
  resolution: "@types/react@npm:17.0.74"
  dependencies:
    "@types/prop-types": "npm:*"
    "@types/scheduler": "npm:*"
    csstype: "npm:^3.0.2"
  checksum: 4d8c44a01afc07a9d8e22d6592d7528460865aed29935280ab317cdfc9d00867c0d5190d95ebaaeb4bea40f2b0a5d5034f55182d274aabd2f7040e31edc7e78f
  languageName: node
  linkType: hard

"@types/redux-watch@npm:^1.1.0":
  version: 1.1.2
  resolution: "@types/redux-watch@npm:1.1.2"
  checksum: 29b5104ff3eecf5837fb92e3001ed21327500871359e226f9510e4d78785d5fb104d2759983582a23ed3887278831d384a067bcede5dfd873de6837f56ed75a8
  languageName: node
  linkType: hard

"@types/scheduler@npm:*":
  version: 0.16.8
  resolution: "@types/scheduler@npm:0.16.8"
  checksum: 6c091b096daa490093bf30dd7947cd28e5b2cd612ec93448432b33f724b162587fed9309a0acc104d97b69b1d49a0f3fc755a62282054d62975d53d7fd13472d
  languageName: node
  linkType: hard

"@types/semver@npm:^7.5.0":
  version: 7.5.6
  resolution: "@types/semver@npm:7.5.6"
  checksum: e77282b17f74354e17e771c0035cccb54b94cc53d0433fa7e9ba9d23fd5d7edcd14b6c8b7327d58bbd89e83b1c5eda71dfe408e06b929007e2b89586e9b63459
  languageName: node
  linkType: hard

"@types/shortid@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/shortid@npm:0.0.29"
  checksum: f0ba8dcc3ba0da60a16fe11edb745b0cc6b1d1acbe26f33d4193ad23a5a86a05220b4637619673dee0009f7e6ba80bd5bb72e6346bb49e1e4477d52a9653630d
  languageName: node
  linkType: hard

"@types/stylis@npm:^4.2.1":
  version: 4.2.4
  resolution: "@types/stylis@npm:4.2.4"
  checksum: 0734b4136192f97f4c8792ea41f1293091dfda53434ede08281fa42689d31f16cd1ad0e058de88c11980c18aae29e62a87027b235b98ab0cb237641b6ec44bcb
  languageName: node
  linkType: hard

"@types/use-sync-external-store@npm:^0.0.3":
  version: 0.0.3
  resolution: "@types/use-sync-external-store@npm:0.0.3"
  checksum: 161ddb8eec5dbe7279ac971531217e9af6b99f7783213566d2b502e2e2378ea19cf5e5ea4595039d730aa79d3d35c6567d48599f69773a02ffcff1776ec2a44e
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^6.7.4":
  version: 6.13.2
  resolution: "@typescript-eslint/eslint-plugin@npm:6.13.2"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.5.1"
    "@typescript-eslint/scope-manager": "npm:6.13.2"
    "@typescript-eslint/type-utils": "npm:6.13.2"
    "@typescript-eslint/utils": "npm:6.13.2"
    "@typescript-eslint/visitor-keys": "npm:6.13.2"
    debug: "npm:^4.3.4"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.2.4"
    natural-compare: "npm:^1.4.0"
    semver: "npm:^7.5.4"
    ts-api-utils: "npm:^1.0.1"
  peerDependencies:
    "@typescript-eslint/parser": ^6.0.0 || ^6.0.0-alpha
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: e6665fc5de0ae2b7ada9150d3d119157521a04208b8da385a4c39538b9471871efe6eef272dbcee4c76f599e09453b6f7317f3b3b80d89291f6b2ace4125c51b
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^6.7.4":
  version: 6.13.2
  resolution: "@typescript-eslint/parser@npm:6.13.2"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:6.13.2"
    "@typescript-eslint/types": "npm:6.13.2"
    "@typescript-eslint/typescript-estree": "npm:6.13.2"
    "@typescript-eslint/visitor-keys": "npm:6.13.2"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: a2b32d2ad1aa12ce31790e80e059fd5b0699265541f3799b58f2e5c8b40f2e21ac7010519802bc406f4b74c14a1f52081570fa6119b949f68392d0968628b3b8
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:6.13.2":
  version: 6.13.2
  resolution: "@typescript-eslint/scope-manager@npm:6.13.2"
  dependencies:
    "@typescript-eslint/types": "npm:6.13.2"
    "@typescript-eslint/visitor-keys": "npm:6.13.2"
  checksum: a6505cc73e90dfed3b9b03816213610f05be58548f468ce24c05ce49a3c6d029ef02233db51cff3b780aa8d040a0c7b3268ea28244c704932c9ee6ef82088509
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:6.13.2":
  version: 6.13.2
  resolution: "@typescript-eslint/type-utils@npm:6.13.2"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:6.13.2"
    "@typescript-eslint/utils": "npm:6.13.2"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^1.0.1"
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: c8de5ab94295980a378e4f22dd51dedb3838761969ad1a355386a801c9bc332837651747cdc2edf7f399c88d0bbd787d2233d09e14e4e2849fb01a57bd0cab00
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:6.13.2":
  version: 6.13.2
  resolution: "@typescript-eslint/types@npm:6.13.2"
  checksum: 3ed2622468b2c61bff0828a3675357b498360bada85740dd72e4ec3c80ee112bce8808d7688aa0104b9d0a655a30e2deb0fee69468474c7e046fc9285f549fe6
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:6.13.2":
  version: 6.13.2
  resolution: "@typescript-eslint/typescript-estree@npm:6.13.2"
  dependencies:
    "@typescript-eslint/types": "npm:6.13.2"
    "@typescript-eslint/visitor-keys": "npm:6.13.2"
    debug: "npm:^4.3.4"
    globby: "npm:^11.1.0"
    is-glob: "npm:^4.0.3"
    semver: "npm:^7.5.4"
    ts-api-utils: "npm:^1.0.1"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 8fa1344228858fa8171a9660e11eb0d5ed88de2ada343bce4a02a957724ccbeb9b67da1083eada29247f444aeba6908a4145d1758b528d320928abbb4e48dca7
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:6.13.2, @typescript-eslint/utils@npm:^6.13.0":
  version: 6.13.2
  resolution: "@typescript-eslint/utils@npm:6.13.2"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.4.0"
    "@types/json-schema": "npm:^7.0.12"
    "@types/semver": "npm:^7.5.0"
    "@typescript-eslint/scope-manager": "npm:6.13.2"
    "@typescript-eslint/types": "npm:6.13.2"
    "@typescript-eslint/typescript-estree": "npm:6.13.2"
    semver: "npm:^7.5.4"
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  checksum: 934282b612e5f78423bc375122258c5aec65fcdf9c25fd0521e3984686d1e5b950500f093c5f1c21a267be164bc7025d8f629dcfaa60573ad98c6e3861092076
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:6.13.2":
  version: 6.13.2
  resolution: "@typescript-eslint/visitor-keys@npm:6.13.2"
  dependencies:
    "@typescript-eslint/types": "npm:6.13.2"
    eslint-visitor-keys: "npm:^3.4.1"
  checksum: eb6f3a3fa4dae6003533eac41bd2a8181a0353f352640e92b619e353b4bd5a5cd4c076018cbdf4b1ba45b826be0c1d15293d87e956fc9a8aa2fb8d8aa04a7c98
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.2.0":
  version: 1.2.0
  resolution: "@ungap/structured-clone@npm:1.2.0"
  checksum: c6fe89a505e513a7592e1438280db1c075764793a2397877ff1351721fe8792a966a5359769e30242b3cd023f2efb9e63ca2ca88019d73b564488cc20e3eab12
  languageName: node
  linkType: hard

"@yudiel/react-qr-scanner@npm:^1.2.3":
  version: 1.2.4
  resolution: "@yudiel/react-qr-scanner@npm:1.2.4"
  dependencies:
    "@zxing/library": "npm:^0.20.0"
  peerDependencies:
    react: ^18.0.0
    react-dom: ^18.0.0
  checksum: a5f0e60afca3ddcd66c04d05f1b5fbd6b3829656fbaf08d3abad0acfa8e1a61789af18a4349e5d82ad34c7fae0ebd060d6d7fdb680b9195162cbc297317bfe17
  languageName: node
  linkType: hard

"@zxing/library@npm:^0.20.0":
  version: 0.20.0
  resolution: "@zxing/library@npm:0.20.0"
  dependencies:
    "@zxing/text-encoding": "npm:~0.9.0"
    ts-custom-error: "npm:^3.2.1"
  dependenciesMeta:
    "@zxing/text-encoding":
      optional: true
  checksum: 866ec2adc9f59b5176046a6219951122bd075eac846aa739285b9b20372635c29ad13bc49a4a721e91d13b0d078c5f50fe81087a510920b0ea202dda41b0a5a4
  languageName: node
  linkType: hard

"@zxing/text-encoding@npm:~0.9.0":
  version: 0.9.0
  resolution: "@zxing/text-encoding@npm:0.9.0"
  checksum: 268e4ef64b8eaa32b990240bdfd1f7b3e2b501a6ed866a565f7c9747f04ac884fbe0537fe12bb05d9241b98fb111270c0fd0023ef0a02d23a6619b4589e98f6b
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: ca0a54e35bea4ece0ecb68a47b312e1a9a6f772408d5bcb9051230aaa94b0460671c5b5c9cb3240eb5b7bc94c52476550eb221f65a0bbd0145bdc9f3113a6707
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: d4371eaef7995530b5b5ca4183ff6f062ca17901a6d3f673c9ac011b01ede37e7a1f7f61f8f5cfe709e88054757bb8f3277dc4061087cdf4f2a1f90ccbcdb977
  languageName: node
  linkType: hard

"acorn@npm:^8.9.0":
  version: 8.11.2
  resolution: "acorn@npm:8.11.2"
  bin:
    acorn: bin/acorn
  checksum: ff559b891382ad4cd34cc3c493511d0a7075a51f5f9f02a03440e92be3705679367238338566c5fbd3521ecadd565d29301bc8e16cb48379206bffbff3d72500
  languageName: node
  linkType: hard

"agent-base@npm:^7.0.2, agent-base@npm:^7.1.0":
  version: 7.1.0
  resolution: "agent-base@npm:7.1.0"
  dependencies:
    debug: "npm:^4.3.4"
  checksum: f7828f991470a0cc22cb579c86a18cbae83d8a3cbed39992ab34fc7217c4d126017f1c74d0ab66be87f71455318a8ea3e757d6a37881b8d0f2a2c6aa55e5418f
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: "npm:^2.0.0"
    indent-string: "npm:^4.0.0"
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 48d6ad21138d12eb4d16d878d630079a2bda25a04e745c07846a4ad768319533031e28872a9b3c5790fa1ec41aabdf2abed30a56e5a03ebc2cf92184b8ee306c
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.0.1
  resolution: "ansi-regex@npm:6.0.1"
  checksum: 1ff8b7667cded1de4fa2c9ae283e979fc87036864317da86a2e546725f96406746411d0d85e87a2d12fa5abd715d90006de7fa4fa0477c92321ad3b4c7d4e169
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: "npm:^1.9.0"
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: b4494dfbfc7e4591b4711a396bd27e540f8153914123dccb4cdbbcb514015ada63a3809f362b9d8d4f6b17a706f1d7bea3c6f974b15fa5ae76b5b502070889ff
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 70fdf883b704d17a5dfc9cde206e698c16bcd74e7f196ab821511651aee4f9f76c9514bdfa6ca3a27b5e49138b89cb222a28caf3afe4567570139577f991df32
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"archiver-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "archiver-utils@npm:2.1.0"
  dependencies:
    glob: "npm:^7.1.4"
    graceful-fs: "npm:^4.2.0"
    lazystream: "npm:^1.0.0"
    lodash.defaults: "npm:^4.2.0"
    lodash.difference: "npm:^4.5.0"
    lodash.flatten: "npm:^4.4.0"
    lodash.isplainobject: "npm:^4.0.6"
    lodash.union: "npm:^4.6.0"
    normalize-path: "npm:^3.0.0"
    readable-stream: "npm:^2.0.0"
  checksum: 4df493c0e6a3a544119b08b350308923500e2c6efee6a283cba4c3202293ce3acb70897e54e24f735e3a38ff43e5a65f66e2e5225fdfc955bf2335491377be2e
  languageName: node
  linkType: hard

"archiver-utils@npm:^3.0.4":
  version: 3.0.4
  resolution: "archiver-utils@npm:3.0.4"
  dependencies:
    glob: "npm:^7.2.3"
    graceful-fs: "npm:^4.2.0"
    lazystream: "npm:^1.0.0"
    lodash.defaults: "npm:^4.2.0"
    lodash.difference: "npm:^4.5.0"
    lodash.flatten: "npm:^4.4.0"
    lodash.isplainobject: "npm:^4.0.6"
    lodash.union: "npm:^4.6.0"
    normalize-path: "npm:^3.0.0"
    readable-stream: "npm:^3.6.0"
  checksum: a838c325a1e1d6798c07e6a3af08f480fdce57cba2964bff8761126715aa1b71e9a119442eac19b7ec6313f5298e54a180dc6612ae548825fbc9be6836e50487
  languageName: node
  linkType: hard

"archiver@npm:^5.0.0":
  version: 5.3.2
  resolution: "archiver@npm:5.3.2"
  dependencies:
    archiver-utils: "npm:^2.1.0"
    async: "npm:^3.2.4"
    buffer-crc32: "npm:^0.2.1"
    readable-stream: "npm:^3.6.0"
    readdir-glob: "npm:^1.1.2"
    tar-stream: "npm:^2.2.0"
    zip-stream: "npm:^4.1.0"
  checksum: 9384b3b20d330f95140c2b7a9b51140d14e9bc7b133be6cf573067ed8fc67a6e9618cfbfe60b1ba78b8034857001fd02c8900f2fba4864514670a2274d36dc9e
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 18640244e641a417ec75a9bd38b0b2b6b95af5199aa241b131d4b2fb206f334d7ecc600bd194861610a5579084978bfcbb02baa399dbe442d56d0ae5e60dbaef
  languageName: node
  linkType: hard

"aria-query@npm:^5.3.0":
  version: 5.3.0
  resolution: "aria-query@npm:5.3.0"
  dependencies:
    dequal: "npm:^2.0.3"
  checksum: c3e1ed127cc6886fea4732e97dd6d3c3938e64180803acfb9df8955517c4943760746ffaf4020ce8f7ffaa7556a3b5f85c3769a1f5ca74a1288e02d042f9ae4e
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.0":
  version: 1.0.0
  resolution: "array-buffer-byte-length@npm:1.0.0"
  dependencies:
    call-bind: "npm:^1.0.2"
    is-array-buffer: "npm:^3.0.1"
  checksum: 044e101ce150f4804ad19c51d6c4d4cfa505c5b2577bd179256e4aa3f3f6a0a5e9874c78cd428ee566ac574c8a04d7ce21af9fe52e844abfdccb82b33035a7c3
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.6, array-includes@npm:^3.1.7":
  version: 3.1.7
  resolution: "array-includes@npm:3.1.7"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
    get-intrinsic: "npm:^1.2.1"
    is-string: "npm:^1.0.7"
  checksum: 856a8be5d118967665936ad33ff3b07adfc50b06753e596e91fb80c3da9b8c022e92e3cc6781156d6ad95db7109b9f603682c7df2d6a529ed01f7f6b39a4a360
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"array.prototype.findlastindex@npm:^1.2.3":
  version: 1.2.3
  resolution: "array.prototype.findlastindex@npm:1.2.3"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
    es-shim-unscopables: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.1"
  checksum: 063cbab8eeac3aa01f3e980eecb9a8c5d87723032b49f7f814ecc6d75c33c03c17e3f43a458127a62e16303cab412f95d6ad9dc7e0ae6d9dc27a9bb76c24df7a
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1, array.prototype.flat@npm:^1.3.2":
  version: 1.3.2
  resolution: "array.prototype.flat@npm:1.3.2"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
    es-shim-unscopables: "npm:^1.0.0"
  checksum: d9d2f6f27584de92ec7995bc931103e6de722cd2498bdbfc4cba814fc3e52f056050a93be883018811f7c0a35875f5056584a0e940603a5e5934f0279896aebe
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.1, array.prototype.flatmap@npm:^1.3.2":
  version: 1.3.2
  resolution: "array.prototype.flatmap@npm:1.3.2"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
    es-shim-unscopables: "npm:^1.0.0"
  checksum: 33f20006686e0cbe844fde7fd290971e8366c6c5e3380681c2df15738b1df766dd02c7784034aeeb3b037f65c496ee54de665388288edb323a2008bb550f77ea
  languageName: node
  linkType: hard

"array.prototype.tosorted@npm:^1.1.1":
  version: 1.1.2
  resolution: "array.prototype.tosorted@npm:1.1.2"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
    es-shim-unscopables: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.1"
  checksum: aadb7725bb923f594be8121c80def8193ff2871ce1bfa1180b7e7ef705b8a7b32327fcc0d998c5569bb0cabc1c11ad93b1ef11443a26091e8bd1a55b382ab715
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.2":
  version: 1.0.2
  resolution: "arraybuffer.prototype.slice@npm:1.0.2"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.0"
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
    get-intrinsic: "npm:^1.2.1"
    is-array-buffer: "npm:^3.0.2"
    is-shared-array-buffer: "npm:^1.0.2"
  checksum: c200faf437786f5b2c80d4564ff5481c886a16dee642ef02abdc7306c7edd523d1f01d1dd12b769c7eb42ac9bc53874510db19a92a2c035c0f6696172aafa5d3
  languageName: node
  linkType: hard

"ast-types-flow@npm:^0.0.8":
  version: 0.0.8
  resolution: "ast-types-flow@npm:0.0.8"
  checksum: 85a1c24af4707871c27cfe456bd2ff7fcbe678f3d1c878ac968c9557735a171a17bdcc8c8f903ceab3fc3c49d5b3da2194e6ab0a6be7fec0e133fa028f21ba1b
  languageName: node
  linkType: hard

"async@npm:^3.2.4":
  version: 3.2.5
  resolution: "async@npm:3.2.5"
  checksum: 323c3615c3f0ab1ac25a6f953296bc0ac3213d5e0f1c0debdb12964e55963af288d570293c11e44f7967af58c06d2a88d0ea588c86ec0fbf62fa98037f604a0f
  languageName: node
  linkType: hard

"asynciterator.prototype@npm:^1.0.0":
  version: 1.0.0
  resolution: "asynciterator.prototype@npm:1.0.0"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: e8ebfd9493ac651cf9b4165e9d64030b3da1d17181bb1963627b59e240cdaf021d9b59d44b827dc1dde4e22387ec04c2d0f8720cf58a1c282e34e40cc12721b3
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 3ce727cbc78f69d6a4722517a58ee926c8c21083633b1d3fdf66fd688f6c127a53a592141bd4866f9b63240a86e9d8e974b13919450bd17fa33c2d22c4558ad8
  languageName: node
  linkType: hard

"autosuggest-highlight@npm:^3.3.4":
  version: 3.3.4
  resolution: "autosuggest-highlight@npm:3.3.4"
  dependencies:
    remove-accents: "npm:^0.4.2"
  checksum: 4808b84f63baac7709e93fb2f73ed5b6638aa25af18ebc6236b4b11db9460c36a67d316270720d58d06a5f8fdf8c19b9308132263e305ddeb705a434562c5533
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.5":
  version: 1.0.5
  resolution: "available-typed-arrays@npm:1.0.5"
  checksum: 4d4d5e86ea0425696f40717882f66a570647b94ac8d273ddc7549a9b61e5da099e149bf431530ccbd776bd74e02039eb8b5edf426e3e2211ee61af16698a9064
  languageName: node
  linkType: hard

"axe-core@npm:=4.7.0":
  version: 4.7.0
  resolution: "axe-core@npm:4.7.0"
  checksum: 615c0f7722c3c9fcf353dbd70b00e2ceae234d4c17cbc839dd85c01d16797c4e4da45f8d27c6118e9e6b033fb06efd196106e13651a1b2f3a10e0f11c7b2f660
  languageName: node
  linkType: hard

"axios@npm:^1.5.1, axios@npm:^1.6.1":
  version: 1.6.2
  resolution: "axios@npm:1.6.2"
  dependencies:
    follow-redirects: "npm:^1.15.0"
    form-data: "npm:^4.0.0"
    proxy-from-env: "npm:^1.1.0"
  checksum: 612bc93f8f738a518e7c5f9de9cc782bcd36aac6bae279160ef6a10260378e21c1786520eab3336898e3d66e0839ebdf739f327fb6d0431baa4d3235703a7652
  languageName: node
  linkType: hard

"axobject-query@npm:^3.2.1":
  version: 3.2.1
  resolution: "axobject-query@npm:3.2.1"
  dependencies:
    dequal: "npm:^2.0.3"
  checksum: 675af2548ed4ece75ad6d50cc0473cfdec7579eac77ec9861e7088d03ffb171aa697b70d2877423bee2ce16460ef62c698c6442a105612cc015719e8ea06b0bd
  languageName: node
  linkType: hard

"babel-plugin-macros@npm:^3.1.0":
  version: 3.1.0
  resolution: "babel-plugin-macros@npm:3.1.0"
  dependencies:
    "@babel/runtime": "npm:^7.12.5"
    cosmiconfig: "npm:^7.0.0"
    resolve: "npm:^1.19.0"
  checksum: 30be6ca45e9a124c58ca00af9a0753e5410ec0b79a737714fc4722bbbeb693e55d9258f05c437145ef4a867c2d1603e06a1c292d66c243ce1227458c8ea2ca8c
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.6":
  version: 0.4.6
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.6"
  dependencies:
    "@babel/compat-data": "npm:^7.22.6"
    "@babel/helper-define-polyfill-provider": "npm:^0.4.3"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 736b1bb8e570be029f941a374c769972af870c96b5c324a5387c6b6994aabdad045ce560c530038c8626f02ec70f711ad7445f2572c32ba81fa0e13402cc23f8
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.8.5":
  version: 0.8.6
  resolution: "babel-plugin-polyfill-corejs3@npm:0.8.6"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.4.3"
    core-js-compat: "npm:^3.33.1"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 2d9c926fda31d800dea7843d82a41b8914a8aaa67d7fb293dd2594e82cd6ce4c9fc67c9d469587b7c14ba38f5ab5689bdc9c21c268888598f464fe77a5f4c964
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.5.3":
  version: 0.5.3
  resolution: "babel-plugin-polyfill-regenerator@npm:0.5.3"
  dependencies:
    "@babel/helper-define-polyfill-provider": "npm:^0.4.3"
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 2bb546582cda1870d19e646a7183baeb2cccd56e0ef3e4eaeabd28e120daf17cb87399194a9ccdcf32506bcaa68d23e73440fc8ab990a7a0f8c5a77c12d5d4bc
  languageName: node
  linkType: hard

"babel-plugin-styled-components@npm:>= 1.12.0":
  version: 2.1.4
  resolution: "babel-plugin-styled-components@npm:2.1.4"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.22.5"
    "@babel/helper-module-imports": "npm:^7.22.5"
    "@babel/plugin-syntax-jsx": "npm:^7.22.5"
    lodash: "npm:^4.17.21"
    picomatch: "npm:^2.3.1"
  peerDependencies:
    styled-components: ">= 2"
  checksum: 34f10dd4d44cf1c8605097dd4796e2d1443266ebc686f10a9f56b5d1492b5c3de9c13d7e30b075756610adf592ed807cc8145189d00b4454f6af9879a19a5e0b
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"big-integer@npm:^1.6.16, big-integer@npm:^1.6.17, big-integer@npm:^1.6.44":
  version: 1.6.52
  resolution: "big-integer@npm:1.6.52"
  checksum: 4bc6ae152a96edc9f95020f5fc66b13d26a9ad9a021225a9f0213f7e3dc44269f423aa8c42e19d6ac4a63bb2b22140b95d10be8f9ca7a6d9aa1b22b330d1f514
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.2.0
  resolution: "binary-extensions@npm:2.2.0"
  checksum: ccd267956c58d2315f5d3ea6757cf09863c5fc703e50fbeb13a7dc849b812ef76e3cf9ca8f35a0c48498776a7478d7b4a0418e1e2b8cb9cb9731f2922aaad7f8
  languageName: node
  linkType: hard

"binary@npm:~0.3.0":
  version: 0.3.0
  resolution: "binary@npm:0.3.0"
  dependencies:
    buffers: "npm:~0.1.1"
    chainsaw: "npm:~0.1.0"
  checksum: 127591ebb7bfca242ec11be9ef874bcde17c520f249d764810045971b6617b659e8af4452f8a1586db7fd47e1b481a75d22c8f207fc1466c0f099b9435e51679
  languageName: node
  linkType: hard

"bl@npm:^4.0.3":
  version: 4.1.0
  resolution: "bl@npm:4.1.0"
  dependencies:
    buffer: "npm:^5.5.0"
    inherits: "npm:^2.0.4"
    readable-stream: "npm:^3.4.0"
  checksum: b7904e66ed0bdfc813c06ea6c3e35eafecb104369dbf5356d0f416af90c1546de3b74e5b63506f0629acf5e16a6f87c3798f16233dcff086e9129383aa02ab55
  languageName: node
  linkType: hard

"bluebird@npm:~3.4.1":
  version: 3.4.7
  resolution: "bluebird@npm:3.4.7"
  checksum: 340e4d11d4b6a26d90371180effb4e500197c2943e5426472d6b6bffca0032a534226ad10255fc0e39c025bea197341c6b2a4258f8c0f18217c7b3a254c76c14
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"bplist-parser@npm:^0.2.0":
  version: 0.2.0
  resolution: "bplist-parser@npm:0.2.0"
  dependencies:
    big-integer: "npm:^1.6.44"
  checksum: 15d31c1b0c7e0fb384e96349453879a33609d92d91b55a9ccee04b4be4b0645f1c823253d73326a1a23104521fbc45c2dd97fb05adf61863841b68cbb2ca7a3d
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.2, braces@npm:~3.0.2":
  version: 3.0.2
  resolution: "braces@npm:3.0.2"
  dependencies:
    fill-range: "npm:^7.0.1"
  checksum: 966b1fb48d193b9d155f810e5efd1790962f2c4e0829f8440b8ad236ba009222c501f70185ef732fef17a4c490bb33a03b90dab0631feafbdf447da91e8165b1
  languageName: node
  linkType: hard

"broadcast-channel@npm:^3.4.1":
  version: 3.7.0
  resolution: "broadcast-channel@npm:3.7.0"
  dependencies:
    "@babel/runtime": "npm:^7.7.2"
    detect-node: "npm:^2.1.0"
    js-sha3: "npm:0.8.0"
    microseconds: "npm:0.2.0"
    nano-time: "npm:1.0.0"
    oblivious-set: "npm:1.0.0"
    rimraf: "npm:3.0.2"
    unload: "npm:2.2.0"
  checksum: ccf6be63c5ed03965f00c28f2cc55028ca3d6eb6f47cb430cc7a5e1ed404c54601c32bc87db24d11f229c80201fd2e606f5c9683543875a7e26ca06e23079782
  languageName: node
  linkType: hard

"browserslist@npm:^4.21.9, browserslist@npm:^4.22.2":
  version: 4.22.2
  resolution: "browserslist@npm:4.22.2"
  dependencies:
    caniuse-lite: "npm:^1.0.30001565"
    electron-to-chromium: "npm:^1.4.601"
    node-releases: "npm:^2.0.14"
    update-browserslist-db: "npm:^1.0.13"
  bin:
    browserslist: cli.js
  checksum: e3590793db7f66ad3a50817e7b7f195ce61e029bd7187200244db664bfbe0ac832f784e4f6b9c958aef8ea4abe001ae7880b7522682df521f4bc0a5b67660b5e
  languageName: node
  linkType: hard

"buffer-crc32@npm:^0.2.1, buffer-crc32@npm:^0.2.13":
  version: 0.2.13
  resolution: "buffer-crc32@npm:0.2.13"
  checksum: 06252347ae6daca3453b94e4b2f1d3754a3b146a111d81c68924c22d91889a40623264e95e67955b1cb4a68cbedf317abeabb5140a9766ed248973096db5ce1c
  languageName: node
  linkType: hard

"buffer-indexof-polyfill@npm:~1.0.0":
  version: 1.0.2
  resolution: "buffer-indexof-polyfill@npm:1.0.2"
  checksum: 808c58a3f06cc6ee2231060959eaa31c490248465f2847e8cfebd3e62563521e67346391caad03ce7616fd765374eb53e941bdd22edb2336431171f46fddcd89
  languageName: node
  linkType: hard

"buffer@npm:^5.5.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.1.13"
  checksum: 997434d3c6e3b39e0be479a80288875f71cd1c07d75a3855e6f08ef848a3c966023f79534e22e415ff3a5112708ce06127277ab20e527146d55c84566405c7c6
  languageName: node
  linkType: hard

"buffers@npm:~0.1.1":
  version: 0.1.1
  resolution: "buffers@npm:0.1.1"
  checksum: 9f0b64bbb8ac4783b1740219ab3532b03ef978fa38e70a0ba8c0695a2f6bc7e2af0ce42f0756b0c1a127070493055adbaf490fb68d95bebd7ccc310c6a483860
  languageName: node
  linkType: hard

"bundle-name@npm:^3.0.0":
  version: 3.0.0
  resolution: "bundle-name@npm:3.0.0"
  dependencies:
    run-applescript: "npm:^5.0.0"
  checksum: edf2b1fbe6096ed32e7566947ace2ea937ee427391744d7510a2880c4b9a5b3543d3f6c551236a29e5c87d3195f8e2912516290e638c15bcbede7b37cc375615
  languageName: node
  linkType: hard

"busboy@npm:1.6.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: "npm:^1.1.0"
  checksum: bee10fa10ea58e7e3e7489ffe4bda6eacd540a17de9f9cd21cc37e297b2dd9fe52b2715a5841afaec82900750d810d01d7edb4b2d456427f449b92b417579763
  languageName: node
  linkType: hard

"cacache@npm:^18.0.0":
  version: 18.0.1
  resolution: "cacache@npm:18.0.1"
  dependencies:
    "@npmcli/fs": "npm:^3.1.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^4.0.0"
    ssri: "npm:^10.0.0"
    tar: "npm:^6.1.11"
    unique-filename: "npm:^3.0.0"
  checksum: aecafd368fbfb2fc0cda1f2f831fe5a1d8161d2121317c92ac089bcd985085e8a588e810b4471e69946f91c6d2661849400e963231563c519aa1e3dac2cf6187
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.0, call-bind@npm:^1.0.2, call-bind@npm:^1.0.4, call-bind@npm:^1.0.5":
  version: 1.0.5
  resolution: "call-bind@npm:1.0.5"
  dependencies:
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.1"
    set-function-length: "npm:^1.1.1"
  checksum: 246d44db6ef9bbd418828dbd5337f80b46be4398d522eded015f31554cbb2ea33025b0203b75c7ab05a1a255b56ef218880cca1743e4121e306729f9e414da39
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"camelize@npm:^1.0.0":
  version: 1.0.1
  resolution: "camelize@npm:1.0.1"
  checksum: 0e147b4299ac6363c50050716aadfae42831257ec56ce54773ffd2a94a88abb2e2540c5ccc38345e8a39963105b76d86cb24477165a36b78c9958fb304513db3
  languageName: node
  linkType: hard

"can-use-dom@npm:^0.1.0":
  version: 0.1.0
  resolution: "can-use-dom@npm:0.1.0"
  checksum: 4b465d2d176a3580428a7d406cb03dd0f05e068b1e1ac500c244eb7dd8c62613c87620c56b4a2c5c68c3d33dfd5a6fd56bde7bfb335dc5882f7489d68013600c
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001406, caniuse-lite@npm:^1.0.30001565":
  version: 1.0.30001566
  resolution: "caniuse-lite@npm:1.0.30001566"
  checksum: fdff43ed498201bf4f6074bd1112bd853e91973b6ccb016049b030948a7d197cba235ac4d93e712d1862b33a3c947bf4e62bad7011ccdac78e5179501b28d04a
  languageName: node
  linkType: hard

"chainsaw@npm:~0.1.0":
  version: 0.1.0
  resolution: "chainsaw@npm:0.1.0"
  dependencies:
    traverse: "npm:>=0.3.0 <0.4"
  checksum: d85627cd3440eb908b9cd72a1ddce4a36bb1ebc9d431a4a2f44b4435cbefdd83625c05114d870381ba765849c34ad05f236c3f590b1581ea03c22897fe6883d0
  languageName: node
  linkType: hard

"chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: "npm:^3.2.1"
    escape-string-regexp: "npm:^1.0.5"
    supports-color: "npm:^5.3.0"
  checksum: 3d1d103433166f6bfe82ac75724951b33769675252d8417317363ef9d54699b7c3b2d46671b772b893a8e50c3ece70c4b933c73c01e81bc60ea4df9b55afa303
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: cb3f3e594913d63b1814d7ca7c9bafbf895f75fbf93b92991980610dfd7b48500af4e3a5d4e3a8f337990a96b168d7eb84ee55efdce965e2ee8efc20f8c8f139
  languageName: node
  linkType: hard

"chokidar@npm:>=3.0.0 <4.0.0":
  version: 3.5.3
  resolution: "chokidar@npm:3.5.3"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 863e3ff78ee7a4a24513d2a416856e84c8e4f5e60efbe03e8ab791af1a183f569b62fc6f6b8044e2804966cb81277ddbbc1dc374fba3265bd609ea8efd62f5b3
  languageName: node
  linkType: hard

"chonky@npm:^2.3.2":
  version: 2.3.2
  resolution: "chonky@npm:2.3.2"
  dependencies:
    "@material-ui/core": "npm:4.11.3"
    "@reduxjs/toolkit": "npm:^1.5.0"
    "@types/classnames": "npm:^2.2.11"
    "@types/fuzzy-search": "npm:^2.1.0"
    "@types/memoizee": "npm:^0.4.5"
    "@types/react": "npm:^17.0.3"
    "@types/react-redux": "npm:^7.1.16"
    "@types/react-virtualized-auto-sizer": "npm:^1.0.0"
    "@types/react-window": "npm:^1.8.2"
    "@types/redux-watch": "npm:^1.1.0"
    "@types/shortid": "npm:^0.0.29"
    classnames: "npm:^2.2.6"
    deepmerge: "npm:^4.2.2"
    exact-trie: "npm:^1.0.13"
    fast-sort: "npm:^2.2.0"
    filesize: "npm:^6.1.0"
    fuzzy-search: "npm:^3.2.1"
    hotkeys-js: "npm:^3.8.3"
    react-dnd: "npm:11"
    react-dnd-html5-backend: "npm:11"
    react-intl: "npm:^5.13.2"
    react-jss: "npm:^10.5.1"
    react-redux: "npm:^7.2.2"
    react-virtualized-auto-sizer: "npm:^1.0.5"
    react-window: "npm:^1.8.6"
    redux-watch: "npm:^1.2.0"
    shortid: "npm:^2.2.16"
    styled-components: "npm:^5.3.0"
    tsdef: "npm:^0.0.14"
  peerDependencies:
    react: ">=16"
  checksum: 37c69ce2294738b6def67881039e7e2deeb345c423be5ff99ba2348799aab5df5de88eda2b989bb9f536992abcd065b58d1d4e462c34fb1da9fb6d826a82c1d4
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"classnames@npm:*, classnames@npm:^2.2.6":
  version: 2.5.1
  resolution: "classnames@npm:2.5.1"
  checksum: 58eb394e8817021b153bb6e7d782cfb667e4ab390cb2e9dac2fc7c6b979d1cc2b2a733093955fc5c94aa79ef5c8c89f11ab77780894509be6afbb91dddd79d15
  languageName: node
  linkType: hard

"classnames@npm:^2.2.5":
  version: 2.3.2
  resolution: "classnames@npm:2.3.2"
  checksum: ba3151c12e8b6a84c64b340ab4259ad0408947652009314462d828e94631505989c6a7d7e796bec1d309be9295d3111b498ad18a9d533fe3e6f859e51e574cbb
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"client-only@npm:0.0.1":
  version: 0.0.1
  resolution: "client-only@npm:0.0.1"
  checksum: 0c16bf660dadb90610553c1d8946a7fdfb81d624adea073b8440b7d795d5b5b08beb3c950c6a2cf16279365a3265158a236876d92bce16423c485c322d7dfaf8
  languageName: node
  linkType: hard

"clone@npm:^2.1.1":
  version: 2.1.2
  resolution: "clone@npm:2.1.2"
  checksum: d9c79efba655f0bf601ab299c57eb54cbaa9860fb011aee9d89ed5ac0d12df1660ab7642fddaabb9a26b7eff0e117d4520512cb70798319ff5d30a111b5310c2
  languageName: node
  linkType: hard

"clsx@npm:^1.0.4, clsx@npm:^1.1.0":
  version: 1.2.1
  resolution: "clsx@npm:1.2.1"
  checksum: 5ded6f61f15f1fa0350e691ccec43a28b12fb8e64c8e94715f2a937bc3722d4c3ed41d6e945c971fc4dcc2a7213a43323beaf2e1c28654af63ba70c9968a8643
  languageName: node
  linkType: hard

"clsx@npm:^2.0.0":
  version: 2.0.0
  resolution: "clsx@npm:2.0.0"
  checksum: 943766d1b02fee3538c871e56638d87f973fbc2d6291ce221215ea436fdecb9be97ad323f411839c2d52c45640c449b1a53fbfe7e8b3d529b4e263308b630c9a
  languageName: node
  linkType: hard

"clsx@npm:^2.1.0, clsx@npm:^2.1.1":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: cdfb57fa6c7649bbff98d9028c2f0de2f91c86f551179541cf784b1cfdc1562dcb951955f46d54d930a3879931a980e32a46b598acaea274728dbe068deca919
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: "npm:1.1.3"
  checksum: ffa319025045f2973919d155f25e7c00d08836b6b33ea2d205418c59bd63a665d713c52d9737a9e0fe467fb194b40fbef1d849bae80d674568ee220a31ef3d10
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: fa00c91b4332b294de06b443923246bccebe9fab1b253f7fe1772d37b06a2269b4039a85e309abe1fe11b267b11c08d1d0473fda3badd6167f57313af2887a64
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 2e969e637d05d09fa50b02d74c83a1186f6914aae89e6653b62595cc75a221464f884f55f231b8f4df7a49537fba60bdc0427acd2bf324c09a1dbb84837e36e4
  languageName: node
  linkType: hard

"commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 9973af10727ad4b44f26703bf3e9fdc323528660a7590efe3aa9ad5042b4584c0deed84ba443f61c9d6f02dade54a5a5d3c95e306a1e1630f8374ae6db16c06d
  languageName: node
  linkType: hard

"compress-commons@npm:^4.1.2":
  version: 4.1.2
  resolution: "compress-commons@npm:4.1.2"
  dependencies:
    buffer-crc32: "npm:^0.2.13"
    crc32-stream: "npm:^4.0.2"
    normalize-path: "npm:^3.0.0"
    readable-stream: "npm:^3.6.0"
  checksum: 76fa281412e4a95f89893dc1e3399e797de20253365cf53102ac4738fa004d3540abb12c26e3a54156f8fb4e4392ef9a9c5eecbe752f3a7d30e28c808b671e1b
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 9680699c8e2b3af0ae22592cb764acaf973f292a7b71b8a06720233011853a58e256c89216a10cbe889727532fd77f8bcd49a760cedfde271b8e006c20e079f2
  languageName: node
  linkType: hard

"confusing-browser-globals@npm:^1.0.10":
  version: 1.0.11
  resolution: "confusing-browser-globals@npm:1.0.11"
  checksum: 3afc635abd37e566477f610e7978b15753f0e84025c25d49236f1f14d480117185516bdd40d2a2167e6bed8048641a9854964b9c067e3dcdfa6b5d0ad3c3a5ef
  languageName: node
  linkType: hard

"convert-source-map@npm:^1.5.0":
  version: 1.9.0
  resolution: "convert-source-map@npm:1.9.0"
  checksum: dc55a1f28ddd0e9485ef13565f8f756b342f9a46c4ae18b843fe3c30c675d058d6a4823eff86d472f187b176f0adf51ea7b69ea38be34be4a63cbbf91b0593c8
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: c987be3ec061348cdb3c2bfb924bec86dea1eacad10550a85ca23edb0fe3556c3a61c7399114f3331ccb3499d7fd0285ab24566e5745929412983494c3926e15
  languageName: node
  linkType: hard

"cookie@npm:^0.4.0":
  version: 0.4.2
  resolution: "cookie@npm:0.4.2"
  checksum: 2e1de9fdedca54881eab3c0477aeb067f281f3155d9cfee9d28dfb252210d09e85e9d175c0a60689661feb9e35e588515352f2456bc1f8e8db4267e05fd70137
  languageName: node
  linkType: hard

"cookie@npm:^0.5.0":
  version: 0.5.0
  resolution: "cookie@npm:0.5.0"
  checksum: aae7911ddc5f444a9025fbd979ad1b5d60191011339bce48e555cb83343d0f98b865ff5c4d71fecdfb8555a5cafdc65632f6fce172f32aaf6936830a883a0380
  languageName: node
  linkType: hard

"cookies-next@npm:^4.1.0":
  version: 4.1.0
  resolution: "cookies-next@npm:4.1.0"
  dependencies:
    "@types/cookie": "npm:^0.4.1"
    "@types/node": "npm:^16.10.2"
    cookie: "npm:^0.4.0"
  checksum: 876bdc3b89bb749d4201cafd406cc54f637c72dcb156aaaad7b12d30c93c106135bbed501873690951947100b8a1563a9059ebc3ba98b4f5b03fe01e245f2f4a
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.31.0, core-js-compat@npm:^3.33.1":
  version: 3.34.0
  resolution: "core-js-compat@npm:3.34.0"
  dependencies:
    browserslist: "npm:^4.22.2"
  checksum: e29571cc524b4966e331b5876567f13c2b82ed48ac9b02784f3156b29ee1cd82fe3e60052d78b017c429eb61969fd238c22684bb29180908d335266179a29155
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"cosmiconfig@npm:^7.0.0":
  version: 7.1.0
  resolution: "cosmiconfig@npm:7.1.0"
  dependencies:
    "@types/parse-json": "npm:^4.0.0"
    import-fresh: "npm:^3.2.1"
    parse-json: "npm:^5.0.0"
    path-type: "npm:^4.0.0"
    yaml: "npm:^1.10.0"
  checksum: 03600bb3870c80ed151b7b706b99a1f6d78df8f4bdad9c95485072ea13358ef294b13dd99f9e7bf4cc0b43bcd3599d40df7e648750d21c2f6817ca2cd687e071
  languageName: node
  linkType: hard

"cosmiconfig@npm:^8.1.3":
  version: 8.3.6
  resolution: "cosmiconfig@npm:8.3.6"
  dependencies:
    import-fresh: "npm:^3.3.0"
    js-yaml: "npm:^4.1.0"
    parse-json: "npm:^5.2.0"
    path-type: "npm:^4.0.0"
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 91d082baca0f33b1c085bf010f9ded4af43cbedacba8821da0fb5667184d0a848addc52c31fadd080007f904a555319c238cf5f4c03e6d58ece2e4876b2e73d6
  languageName: node
  linkType: hard

"crc-32@npm:^1.2.0":
  version: 1.2.2
  resolution: "crc-32@npm:1.2.2"
  bin:
    crc32: bin/crc32.njs
  checksum: 824f696a5baaf617809aa9cd033313c8f94f12d15ebffa69f10202480396be44aef9831d900ab291638a8022ed91c360696dd5b1ba691eb3f34e60be8835b7c3
  languageName: node
  linkType: hard

"crc32-stream@npm:^4.0.2":
  version: 4.0.3
  resolution: "crc32-stream@npm:4.0.3"
  dependencies:
    crc-32: "npm:^1.2.0"
    readable-stream: "npm:^3.4.0"
  checksum: d44d0ec6f04d8a1bed899ac3e4fbb82111ed567ea6d506be39147362af45c747887fce1032f4beca1646b4824e5a9614cd3332bfa94bbc5577ca5445e7f75ddd
  languageName: node
  linkType: hard

"cross-env@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-env@npm:7.0.3"
  dependencies:
    cross-spawn: "npm:^7.0.1"
  bin:
    cross-env: src/bin/cross-env.js
    cross-env-shell: src/bin/cross-env-shell.js
  checksum: e99911f0d31c20e990fd92d6fd001f4b01668a303221227cc5cb42ed155f086351b1b3bd2699b200e527ab13011b032801f8ce638e6f09f854bdf744095e604c
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.1, cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: e1a13869d2f57d974de0d9ef7acbf69dc6937db20b918525a01dacb5032129bd552d290d886d981e99f1b624cb03657084cc87bd40f115c07ecf376821c729ce
  languageName: node
  linkType: hard

"css-color-keywords@npm:^1.0.0":
  version: 1.0.0
  resolution: "css-color-keywords@npm:1.0.0"
  checksum: 8f125e3ad477bd03c77b533044bd9e8a6f7c0da52d49bbc0bbe38327b3829d6ba04d368ca49dd9ff3b667d2fc8f1698d891c198bbf8feade1a5501bf5a296408
  languageName: node
  linkType: hard

"css-jss@npm:10.10.0":
  version: 10.10.0
  resolution: "css-jss@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    jss: "npm:^10.10.0"
    jss-preset-default: "npm:^10.10.0"
  checksum: e257b076f8c5c7088b39c1683c77db50490c376e50bd5fa996b4b80ef58038014eb3a3515228550e0d7263c4511f2321faf671d1c62c5cf1fb3fe95bdf932a71
  languageName: node
  linkType: hard

"css-select@npm:^5.1.0":
  version: 5.1.0
  resolution: "css-select@npm:5.1.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^6.1.0"
    domhandler: "npm:^5.0.2"
    domutils: "npm:^3.0.1"
    nth-check: "npm:^2.0.1"
  checksum: d486b1e7eb140468218a5ab5af53257e01f937d2173ac46981f6b7de9c5283d55427a36715dc8decfc0c079cf89259ac5b41ef58f6e1a422eee44ab8bfdc78da
  languageName: node
  linkType: hard

"css-to-react-native@npm:^3.0.0":
  version: 3.2.0
  resolution: "css-to-react-native@npm:3.2.0"
  dependencies:
    camelize: "npm:^1.0.0"
    css-color-keywords: "npm:^1.0.0"
    postcss-value-parser: "npm:^4.0.2"
  checksum: 62ef744254e333abc696efdc945ecf13ad6ba7b726d0a39c0405b2fcb86542aa2f3fe7b7b6770f67ae9679d98b159b4d66353107bf7d6144a445eafcf5fa250a
  languageName: node
  linkType: hard

"css-tree@npm:^2.2.1":
  version: 2.3.1
  resolution: "css-tree@npm:2.3.1"
  dependencies:
    mdn-data: "npm:2.0.30"
    source-map-js: "npm:^1.0.1"
  checksum: e5e39b82eb4767c664fa5c2cd9968c8c7e6b7fd2c0079b52680a28466d851e2826d5e64699c449d933c0e8ca0554beca43c41a9fcb09fb6a46139d462dbdf0df
  languageName: node
  linkType: hard

"css-tree@npm:~2.2.0":
  version: 2.2.1
  resolution: "css-tree@npm:2.2.1"
  dependencies:
    mdn-data: "npm:2.0.28"
    source-map-js: "npm:^1.0.1"
  checksum: 1959c4b0e268bf8db1b3a1776a5ba9ae3a464ccd1226bfa62799cb0a3d0039006e21fb95cec4dec9d687a9a9b90f692dff2d230b631527ece700f4bfb419aaf3
  languageName: node
  linkType: hard

"css-vendor@npm:^2.0.8":
  version: 2.0.8
  resolution: "css-vendor@npm:2.0.8"
  dependencies:
    "@babel/runtime": "npm:^7.8.3"
    is-in-browser: "npm:^1.0.2"
  checksum: 3868a17c84b01f1ad9f6d8a44ca84a44453594ea63202e58b20aef63ead801cad021ab0b7816ff8815506c9cc6f971173433fe56b9d150fa76cab09da3041a50
  languageName: node
  linkType: hard

"css-what@npm:^6.1.0":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: c67a3a2d0d81843af87f8bf0a4d0845b0f952377714abbb2884e48942409d57a2110eabee003609d02ee487b054614bdfcfc59ee265728ff105bd5aa221c1d0e
  languageName: node
  linkType: hard

"cssjanus@npm:^2.0.1":
  version: 2.1.0
  resolution: "cssjanus@npm:2.1.0"
  checksum: 858ab0be770f9f898f3331dafbd8c377a8ca3c7ee88fd5b65b702c260d61ff70fe9d9c70bf343ece0594600904deead97d78ad0ede97ad55accfdc98ebe1dcf2
  languageName: node
  linkType: hard

"csso@npm:5.0.5":
  version: 5.0.5
  resolution: "csso@npm:5.0.5"
  dependencies:
    css-tree: "npm:~2.2.0"
  checksum: 4036fb2b9f8ed6b948349136b39e0b19ffb5edee934893a37b55e9a116186c4ae2a9d3ba66fbdbc07fa44a853fb478cd2d8733e4743473dcd364e7f21444ff34
  languageName: node
  linkType: hard

"csstype@npm:^2.5.2":
  version: 2.6.21
  resolution: "csstype@npm:2.6.21"
  checksum: bf9072344fac1b56dc390fbc410b411bbc2a03fa9c3d243a74ff5687f94777f9da03a5681ac01efc2e68b51055e2c7d6a489185a85a8f01c976a85f9eec3b75e
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2, csstype@npm:^3.1.2, csstype@npm:^3.1.3":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: f593cce41ff5ade23f44e77521e3a1bcc2c64107041e1bf6c3c32adc5187d0d60983292fda326154d20b01079e24931aa5b08e4467cc488b60bb1e7f6d478ade
  languageName: node
  linkType: hard

"damerau-levenshtein@npm:^1.0.8":
  version: 1.0.8
  resolution: "damerau-levenshtein@npm:1.0.8"
  checksum: f4eba1c90170f96be25d95fa3857141b5f81e254f7e4d530da929217b19990ea9a0390fc53d3c1cafac9152fda78e722ea4894f765cf6216be413b5af1fbf821
  languageName: node
  linkType: hard

"date-fns@npm:^2.30.0":
  version: 2.30.0
  resolution: "date-fns@npm:2.30.0"
  dependencies:
    "@babel/runtime": "npm:^7.21.0"
  checksum: 70b3e8ea7aaaaeaa2cd80bd889622a4bcb5d8028b4de9162cbcda359db06e16ff6e9309e54eead5341e71031818497f19aaf9839c87d1aba1e27bb4796e758a9
  languageName: node
  linkType: hard

"dayjs-plugin-utc@npm:^0.1.2":
  version: 0.1.2
  resolution: "dayjs-plugin-utc@npm:0.1.2"
  checksum: ccd4d8d342bd018b988e2134756122159bc98e9f2949fd76cc82eb54554b4440223d2ca550ab389ca6e598a96d66dfc6a0fcab3103782a2726db4e24f0018143
  languageName: node
  linkType: hard

"dayjs@npm:^1.11.10, dayjs@npm:^1.8.34":
  version: 1.11.10
  resolution: "dayjs@npm:1.11.10"
  checksum: 27e8f5bc01c0a76f36c656e62ab7f08c2e7b040b09e613cd4844abf03fb258e0350f0a83b02c887b84d771c1f11e092deda0beef8c6df2a1afbc3f6c1fade279
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: "npm:2.1.2"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 0073c3bcbd9cb7d71dd5f6b55be8701af42df3e56e911186dfa46fac3a5b9eb7ce7f377dd1d3be6db8977221f8eb333d945216f645cf56f6b688cd484837d255
  languageName: node
  linkType: hard

"debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: "npm:^2.1.1"
  checksum: d86fd7be2b85462297ea16f1934dc219335e802f629ca9a69b63ed8ed041dda492389bb2ee039217c02e5b54792b1c51aa96ae954cf28634d363a2360c7a1639
  languageName: node
  linkType: hard

"deep-equal@npm:^1.0.1":
  version: 1.1.2
  resolution: "deep-equal@npm:1.1.2"
  dependencies:
    is-arguments: "npm:^1.1.1"
    is-date-object: "npm:^1.0.5"
    is-regex: "npm:^1.1.4"
    object-is: "npm:^1.1.5"
    object-keys: "npm:^1.1.1"
    regexp.prototype.flags: "npm:^1.5.1"
  checksum: c9d2ed2a0d93a2ee286bdb320cd51c78cd4c310b2161d1ede6476b67ca1d73860e7ff63b10927830aa4b9eca2a48073cfa54c8c4a1b2246397bda618c2138e97
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: ec12d074aef5ae5e81fa470b9317c313142c9e8e2afe3f8efa124db309720db96d1d222b82b84c834e5f87e7a614b44a4684b6683583118b87c833b3be40d4d8
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2, deepmerge@npm:^4.3.1":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 058d9e1b0ff1a154468bf3837aea436abcfea1ba1d165ddaaf48ca93765fdd01a30d33c36173da8fbbed951dd0a267602bc782fe288b0fc4b7e1e7091afc4529
  languageName: node
  linkType: hard

"default-browser-id@npm:^3.0.0":
  version: 3.0.0
  resolution: "default-browser-id@npm:3.0.0"
  dependencies:
    bplist-parser: "npm:^0.2.0"
    untildify: "npm:^4.0.0"
  checksum: 279c7ad492542e5556336b6c254a4eaf31b2c63a5433265655ae6e47301197b6cfb15c595a6fdc6463b2ff8e1a1a1ed3cba56038a60e1527ba4ab1628c6b9941
  languageName: node
  linkType: hard

"default-browser@npm:^4.0.0":
  version: 4.0.0
  resolution: "default-browser@npm:4.0.0"
  dependencies:
    bundle-name: "npm:^3.0.0"
    default-browser-id: "npm:^3.0.0"
    execa: "npm:^7.1.1"
    titleize: "npm:^3.0.0"
  checksum: 40c5af984799042b140300be5639c9742599bda76dc9eba5ac9ad5943c83dd36cebc4471eafcfddf8e0ec817166d5ba89d56f08e66a126c7c7908a179cead1a7
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.1":
  version: 1.1.1
  resolution: "define-data-property@npm:1.1.1"
  dependencies:
    get-intrinsic: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
  checksum: 5573c8df96b5857408cad64d9b91b69152e305ce4b06218e5f49b59c6cafdbb90a8bd8a0bb83c7bc67a8d479c04aa697063c9bc28d849b7282f9327586d6bc7b
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^3.0.0":
  version: 3.0.0
  resolution: "define-lazy-prop@npm:3.0.0"
  checksum: f28421cf9ee86eecaf5f3b8fe875f13d7009c2625e97645bfff7a2a49aca678270b86c39f9c32939e5ca7ab96b551377ed4139558c795e076774287ad3af1aa4
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.2.0, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"dequal@npm:^2.0.3":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 6ff05a7561f33603df87c45e389c9ac0a95e3c056be3da1a0c4702149e3a7f6fe5ffbb294478687ba51a9e95f3a60e8b6b9005993acd79c292c7d15f71964b6b
  languageName: node
  linkType: hard

"detect-node@npm:^2.0.4, detect-node@npm:^2.1.0":
  version: 2.1.0
  resolution: "detect-node@npm:2.1.0"
  checksum: 832184ec458353e41533ac9c622f16c19f7c02d8b10c303dfd3a756f56be93e903616c0bb2d4226183c9351c15fc0b3dba41a17a2308262afabcfa3776e6ae6e
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: "npm:^4.0.0"
  checksum: fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"dnd-core@npm:^11.1.3":
  version: 11.1.3
  resolution: "dnd-core@npm:11.1.3"
  dependencies:
    "@react-dnd/asap": "npm:^4.0.0"
    "@react-dnd/invariant": "npm:^2.0.0"
    redux: "npm:^4.0.4"
  checksum: 05e518148c5b7f68cdd6631d60384b2aaca53089e58085f7abb450b99ca787943fb6e2ecf71af57a25d7430ff7f3812b72b05d6cb3d19c81b82e1adf3de6b989
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: 555684f77e791b17173ea86e2eea45ef26c22219cb64670669c4f4bebd26dbc95cd90ec1f4159e9349a6bb9eb892ce4dde8cd0139e77bedd8bf4518238618474
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: b4b28f1df5c563f7d876e7461254a4597b8cabe915abe94d7c5d1633fed263fcf9a85e8d3836591fc2d040108e822b0d32758e5ec1fe31c590dc7e08086e3e48
  languageName: node
  linkType: hard

"dom-helpers@npm:^5.0.1":
  version: 5.2.1
  resolution: "dom-helpers@npm:5.2.1"
  dependencies:
    "@babel/runtime": "npm:^7.8.7"
    csstype: "npm:^3.0.2"
  checksum: bed2341adf8864bf932b3289c24f35fdd99930af77df46688abf2d753ff291df49a15850c874d686d9be6ec4e1c6835673906e64dbd8b2839d227f117a11fd41
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.2"
    entities: "npm:^4.2.0"
  checksum: e3bf9027a64450bca0a72297ecdc1e3abb7a2912268a9f3f5d33a2e29c1e2c3502c6e9f860fc6625940bfe0cfb57a44953262b9e94df76872fdfb8151097eeb3
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: "npm:^2.3.0"
  checksum: 809b805a50a9c6884a29f38aec0a4e1b4537f40e1c861950ed47d10b049febe6b79ab72adaeeebb3cc8fc1cd33f34e97048a72a9265103426d93efafa78d3e96
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1":
  version: 3.1.0
  resolution: "domutils@npm:3.1.0"
  dependencies:
    dom-serializer: "npm:^2.0.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
  checksum: 9a169a6e57ac4c738269a73ab4caf785114ed70e46254139c1bbc8144ac3102aacb28a6149508395ae34aa5d6a40081f4fa5313855dc8319c6d8359866b6dfea
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: a65e3519414856df0228b9f645332f974f2bf5433370f544a681122eab59e66038fc3349b4be1cdc47152779dac71a5864f1ccda2f745e767c46e9c6543b1169
  languageName: node
  linkType: hard

"duplexer2@npm:~0.1.4":
  version: 0.1.4
  resolution: "duplexer2@npm:0.1.4"
  dependencies:
    readable-stream: "npm:^2.0.2"
  checksum: f60ff8b8955f992fd9524516e82faa5662d7aca5b99ee71c50bbbe1a3c970fafacb35d526d8b05cef8c08be56eed3663c096c50626c3c3651a52af36c408bf4d
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 9b1d3e1baefeaf7d70799db8774149cef33b97183a6addceeba0cf6b85ba23ee2686f302f14482006df32df75d32b17c509c143a3689627929e4a8efaf483952
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.4.601":
  version: 1.4.608
  resolution: "electron-to-chromium@npm:1.4.608"
  checksum: 521b5e15ca18b37b20c4382d18d37ff913ae27536c8e7373e063c115db84e9d8849f3f36c5dc030aab543e31591e0a282ba375c40da808cff6395fd911186bc6
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: c72d67a6821be15ec11997877c437491c313d924306b8da5d87d2a2bcc2cec9903cb5b04ee1a088460501d8e5b44f10df82fdc93c444101a7610b80c8b6938e1
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 915acf859cea7131dac1b2b5c9c8e35c4849e325a1d114c30adb8cd615970f6dca0e27f64f3a4949d7d6ed86ecd79a1c5c63f02e697513cddd7b5835c90948b8
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.4.1":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 530a5a5a1e517e962854a31693dbb5c0b2fc40b46dad2a56a2deec656ca040631124f4795823acc68238147805f8b021abbe221f4afed5ef3c8e8efc2024908b
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.12.0":
  version: 5.15.0
  resolution: "enhanced-resolve@npm:5.15.0"
  dependencies:
    graceful-fs: "npm:^4.2.4"
    tapable: "npm:^2.2.0"
  checksum: 180c3f2706f9117bf4dc7982e1df811dad83a8db075723f299245ef4488e0cad7e96859c5f0e410682d28a4ecd4da021ec7d06265f7e4eb6eed30c69ca5f7d3e
  languageName: node
  linkType: hard

"entities@npm:^4.2.0, entities@npm:^4.4.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: ede2a35c9bce1aeccd055a1b445d41c75a14a2bb1cd22e242f20cf04d236cdcd7f9c859eb83f76885327bfae0c25bf03303665ee1ce3d47c5927b98b0e3e3d48
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 1d20d825cdcce8d811bfbe86340f4755c02655a7feb2f13f8c880566d9d72a3f6c92c192a6867632e490d6da67b678271f46e01044996a6443e870331100dfdd
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: d547740aa29c34e753fb6fed2c5de81802438529c12b3673bd37b6bb1fe49b9b7abdc3c11e6062fe625d8a296b3cf769a80f878865e25e685f787763eede3ffb
  languageName: node
  linkType: hard

"es-abstract@npm:^1.22.1":
  version: 1.22.3
  resolution: "es-abstract@npm:1.22.3"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.0"
    arraybuffer.prototype.slice: "npm:^1.0.2"
    available-typed-arrays: "npm:^1.0.5"
    call-bind: "npm:^1.0.5"
    es-set-tostringtag: "npm:^2.0.1"
    es-to-primitive: "npm:^1.2.1"
    function.prototype.name: "npm:^1.1.6"
    get-intrinsic: "npm:^1.2.2"
    get-symbol-description: "npm:^1.0.0"
    globalthis: "npm:^1.0.3"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    has-proto: "npm:^1.0.1"
    has-symbols: "npm:^1.0.3"
    hasown: "npm:^2.0.0"
    internal-slot: "npm:^1.0.5"
    is-array-buffer: "npm:^3.0.2"
    is-callable: "npm:^1.2.7"
    is-negative-zero: "npm:^2.0.2"
    is-regex: "npm:^1.1.4"
    is-shared-array-buffer: "npm:^1.0.2"
    is-string: "npm:^1.0.7"
    is-typed-array: "npm:^1.1.12"
    is-weakref: "npm:^1.0.2"
    object-inspect: "npm:^1.13.1"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.4"
    regexp.prototype.flags: "npm:^1.5.1"
    safe-array-concat: "npm:^1.0.1"
    safe-regex-test: "npm:^1.0.0"
    string.prototype.trim: "npm:^1.2.8"
    string.prototype.trimend: "npm:^1.0.7"
    string.prototype.trimstart: "npm:^1.0.7"
    typed-array-buffer: "npm:^1.0.0"
    typed-array-byte-length: "npm:^1.0.0"
    typed-array-byte-offset: "npm:^1.0.0"
    typed-array-length: "npm:^1.0.4"
    unbox-primitive: "npm:^1.0.2"
    which-typed-array: "npm:^1.1.13"
  checksum: e1ea9738ece15f810733b7bd71d825b555e01bb8c860272560d7d901467a9db1265214d6cf44f3beeb5d73ae421a609b9ad93a39aa47bbcd8cde510d5e0aa875
  languageName: node
  linkType: hard

"es-iterator-helpers@npm:^1.0.12, es-iterator-helpers@npm:^1.0.15":
  version: 1.0.15
  resolution: "es-iterator-helpers@npm:1.0.15"
  dependencies:
    asynciterator.prototype: "npm:^1.0.0"
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.22.1"
    es-set-tostringtag: "npm:^2.0.1"
    function-bind: "npm:^1.1.1"
    get-intrinsic: "npm:^1.2.1"
    globalthis: "npm:^1.0.3"
    has-property-descriptors: "npm:^1.0.0"
    has-proto: "npm:^1.0.1"
    has-symbols: "npm:^1.0.3"
    internal-slot: "npm:^1.0.5"
    iterator.prototype: "npm:^1.1.2"
    safe-array-concat: "npm:^1.0.1"
  checksum: 78535c00c49d81df603e650886d3806f3cd8d288e2c07703cfb145725753a3d2df19bff9feeb14cd1baed02252d1f85c4bbc922c8db02841722ab3ec02e78339
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.1":
  version: 2.0.2
  resolution: "es-set-tostringtag@npm:2.0.2"
  dependencies:
    get-intrinsic: "npm:^1.2.2"
    has-tostringtag: "npm:^1.0.0"
    hasown: "npm:^2.0.0"
  checksum: afcec3a4c9890ae14d7ec606204858441c801ff84f312538e1d1ccf1e5493c8b17bd672235df785f803756472cb4f2d49b87bde5237aef33411e74c22f194e07
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.0":
  version: 1.0.2
  resolution: "es-shim-unscopables@npm:1.0.2"
  dependencies:
    hasown: "npm:^2.0.0"
  checksum: 6d3bf91f658a27cc7217cd32b407a0d714393a84d125ad576319b9e83a893bea165cf41270c29e9ceaa56d3cf41608945d7e2a2c31fd51c0009b0c31402b91c7
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-to-primitive@npm:1.2.1"
  dependencies:
    is-callable: "npm:^1.1.4"
    is-date-object: "npm:^1.0.1"
    is-symbol: "npm:^1.0.2"
  checksum: 74aeeefe2714cf99bb40cab7ce3012d74e1e2c1bd60d0a913b467b269edde6e176ca644b5ba03a5b865fb044a29bca05671cd445c85ca2cdc2de155d7fc8fe9b
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.1.1
  resolution: "escalade@npm:3.1.1"
  checksum: afa618e73362576b63f6ca83c975456621095a1ed42ff068174e3f5cea48afc422814dda548c96e6ebb5333e7265140c7292abcc81bbd6ccb1757d50d3a4e182
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-config-airbnb-base@npm:^15.0.0":
  version: 15.0.0
  resolution: "eslint-config-airbnb-base@npm:15.0.0"
  dependencies:
    confusing-browser-globals: "npm:^1.0.10"
    object.assign: "npm:^4.1.2"
    object.entries: "npm:^1.1.5"
    semver: "npm:^6.3.0"
  peerDependencies:
    eslint: ^7.32.0 || ^8.2.0
    eslint-plugin-import: ^2.25.2
  checksum: daa68a1dcb7bff338747a952723b5fa9d159980ec3554c395a4b52a7f7d4f00a45e7b465420eb6d4d87a82cef6361e4cfd6dbb38c2f3f52f2140b6cf13654803
  languageName: node
  linkType: hard

"eslint-config-airbnb-typescript@npm:^17.1.0":
  version: 17.1.0
  resolution: "eslint-config-airbnb-typescript@npm:17.1.0"
  dependencies:
    eslint-config-airbnb-base: "npm:^15.0.0"
  peerDependencies:
    "@typescript-eslint/eslint-plugin": ^5.13.0 || ^6.0.0
    "@typescript-eslint/parser": ^5.0.0 || ^6.0.0
    eslint: ^7.32.0 || ^8.2.0
    eslint-plugin-import: ^2.25.3
  checksum: a2238d820909ac005704e04d29ed495cebbe024869c488330273ea108e18cbf74b6b13e09d54d22a598fe793b9ed5ae593a7e8f9bdc6ea17614d5f2add340960
  languageName: node
  linkType: hard

"eslint-config-airbnb@npm:^19.0.4":
  version: 19.0.4
  resolution: "eslint-config-airbnb@npm:19.0.4"
  dependencies:
    eslint-config-airbnb-base: "npm:^15.0.0"
    object.assign: "npm:^4.1.2"
    object.entries: "npm:^1.1.5"
  peerDependencies:
    eslint: ^7.32.0 || ^8.2.0
    eslint-plugin-import: ^2.25.3
    eslint-plugin-jsx-a11y: ^6.5.1
    eslint-plugin-react: ^7.28.0
    eslint-plugin-react-hooks: ^4.3.0
  checksum: f2086523cfd20c42fd620c757281bd028aa8ce9dadc7293c5c23ea60947a2d3ca04404ede77b40f5a65250fe3c04502acafc4f2f6946819fe6c257d76d9644e5
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^9.0.0":
  version: 9.1.0
  resolution: "eslint-config-prettier@npm:9.1.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 411e3b3b1c7aa04e3e0f20d561271b3b909014956c4dba51c878bf1a23dbb8c800a3be235c46c4732c70827276e540b6eed4636d9b09b444fd0a8e07f0fcd830
  languageName: node
  linkType: hard

"eslint-import-resolver-node@npm:^0.3.9":
  version: 0.3.9
  resolution: "eslint-import-resolver-node@npm:0.3.9"
  dependencies:
    debug: "npm:^3.2.7"
    is-core-module: "npm:^2.13.0"
    resolve: "npm:^1.22.4"
  checksum: d52e08e1d96cf630957272e4f2644dcfb531e49dcfd1edd2e07e43369eb2ec7a7d4423d417beee613201206ff2efa4eb9a582b5825ee28802fc7c71fcd53ca83
  languageName: node
  linkType: hard

"eslint-import-resolver-typescript@npm:^3.6.1":
  version: 3.6.1
  resolution: "eslint-import-resolver-typescript@npm:3.6.1"
  dependencies:
    debug: "npm:^4.3.4"
    enhanced-resolve: "npm:^5.12.0"
    eslint-module-utils: "npm:^2.7.4"
    fast-glob: "npm:^3.3.1"
    get-tsconfig: "npm:^4.5.0"
    is-core-module: "npm:^2.11.0"
    is-glob: "npm:^4.0.3"
  peerDependencies:
    eslint: "*"
    eslint-plugin-import: "*"
  checksum: 261df24721a7c5e37ee598b63e7e12c54e3d20c9ae5de6dbc132cecced023cb967c481007eef73252da108ac7eabb2e859853ff2e2d5776699a2954466ca716f
  languageName: node
  linkType: hard

"eslint-module-utils@npm:^2.7.4, eslint-module-utils@npm:^2.8.0":
  version: 2.8.0
  resolution: "eslint-module-utils@npm:2.8.0"
  dependencies:
    debug: "npm:^3.2.7"
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: a9a7ed93eb858092e3cdc797357d4ead2b3ea06959b0eada31ab13862d46a59eb064b9cb82302214232e547980ce33618c2992f6821138a4934e65710ed9cc29
  languageName: node
  linkType: hard

"eslint-plugin-import@npm:^2.28.1":
  version: 2.29.0
  resolution: "eslint-plugin-import@npm:2.29.0"
  dependencies:
    array-includes: "npm:^3.1.7"
    array.prototype.findlastindex: "npm:^1.2.3"
    array.prototype.flat: "npm:^1.3.2"
    array.prototype.flatmap: "npm:^1.3.2"
    debug: "npm:^3.2.7"
    doctrine: "npm:^2.1.0"
    eslint-import-resolver-node: "npm:^0.3.9"
    eslint-module-utils: "npm:^2.8.0"
    hasown: "npm:^2.0.0"
    is-core-module: "npm:^2.13.1"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^3.1.2"
    object.fromentries: "npm:^2.0.7"
    object.groupby: "npm:^1.0.1"
    object.values: "npm:^1.1.7"
    semver: "npm:^6.3.1"
    tsconfig-paths: "npm:^3.14.2"
  peerDependencies:
    eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8
  checksum: d6e8d016f38369892c85b866f762c03dee2b337d4f12031756e30d7490879261d1192a3c2f682fd7c4d2b923465f7a1e3d22cfdad5da1b1391c3bd39ea87af1a
  languageName: node
  linkType: hard

"eslint-plugin-jsx-a11y@npm:^6.7.1":
  version: 6.8.0
  resolution: "eslint-plugin-jsx-a11y@npm:6.8.0"
  dependencies:
    "@babel/runtime": "npm:^7.23.2"
    aria-query: "npm:^5.3.0"
    array-includes: "npm:^3.1.7"
    array.prototype.flatmap: "npm:^1.3.2"
    ast-types-flow: "npm:^0.0.8"
    axe-core: "npm:=4.7.0"
    axobject-query: "npm:^3.2.1"
    damerau-levenshtein: "npm:^1.0.8"
    emoji-regex: "npm:^9.2.2"
    es-iterator-helpers: "npm:^1.0.15"
    hasown: "npm:^2.0.0"
    jsx-ast-utils: "npm:^3.3.5"
    language-tags: "npm:^1.0.9"
    minimatch: "npm:^3.1.2"
    object.entries: "npm:^1.1.7"
    object.fromentries: "npm:^2.0.7"
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8
  checksum: 7a8e4498531a43d988ce2f12502a3f5ce96eacfec13f956cf927f24bb041b724fb7fc0f0306ea19d143bfc79e138bf25e25acca0822847206ac6bf5ce095e846
  languageName: node
  linkType: hard

"eslint-plugin-perfectionist@npm:^2.1.0":
  version: 2.5.0
  resolution: "eslint-plugin-perfectionist@npm:2.5.0"
  dependencies:
    "@typescript-eslint/utils": "npm:^6.13.0"
    minimatch: "npm:^9.0.3"
    natural-compare-lite: "npm:^1.4.0"
  peerDependencies:
    astro-eslint-parser: ^0.16.0
    eslint: ">=8.0.0"
    svelte: ">=3.0.0"
    svelte-eslint-parser: ^0.33.0
    vue-eslint-parser: ">=9.0.0"
  peerDependenciesMeta:
    astro-eslint-parser:
      optional: true
    svelte:
      optional: true
    svelte-eslint-parser:
      optional: true
    vue-eslint-parser:
      optional: true
  checksum: 9b4116e345ba066d6cc1bf915a17b3e1f31c0b14909854a1ee96b95423ffa242fb2aa43ff122f13bc93b91891e0365615a46b4a2f6b42c2c3fe875c07988cac7
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^5.0.0":
  version: 5.0.1
  resolution: "eslint-plugin-prettier@npm:5.0.1"
  dependencies:
    prettier-linter-helpers: "npm:^1.0.0"
    synckit: "npm:^0.8.5"
  peerDependencies:
    "@types/eslint": ">=8.0.0"
    eslint: ">=8.0.0"
    prettier: ">=3.0.0"
  peerDependenciesMeta:
    "@types/eslint":
      optional: true
    eslint-config-prettier:
      optional: true
  checksum: 1a43dcca90f61fde0167347e0e870b579835ba6b9d697a862c29c76097a7bb0e8f07a7cf88be33517ca11203d9d4aa335d794c377640c2fe5acd06871db67d34
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^4.6.0":
  version: 4.6.0
  resolution: "eslint-plugin-react-hooks@npm:4.6.0"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0
  checksum: 3c63134e056a6d98d66e2c475c81f904169db817e89316d14e36269919e31f4876a2588aa0e466ec8ef160465169c627fe823bfdaae7e213946584e4a165a3ac
  languageName: node
  linkType: hard

"eslint-plugin-react@npm:^7.33.2":
  version: 7.33.2
  resolution: "eslint-plugin-react@npm:7.33.2"
  dependencies:
    array-includes: "npm:^3.1.6"
    array.prototype.flatmap: "npm:^1.3.1"
    array.prototype.tosorted: "npm:^1.1.1"
    doctrine: "npm:^2.1.0"
    es-iterator-helpers: "npm:^1.0.12"
    estraverse: "npm:^5.3.0"
    jsx-ast-utils: "npm:^2.4.1 || ^3.0.0"
    minimatch: "npm:^3.1.2"
    object.entries: "npm:^1.1.6"
    object.fromentries: "npm:^2.0.6"
    object.hasown: "npm:^1.1.2"
    object.values: "npm:^1.1.6"
    prop-types: "npm:^15.8.1"
    resolve: "npm:^2.0.0-next.4"
    semver: "npm:^6.3.1"
    string.prototype.matchall: "npm:^4.0.8"
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8
  checksum: cb8c5dd5859cace330e24b7d74b9c652c0d93ef1d87957261fe1ac2975c27c918d0d5dc607f25aba4972ce74d04456f4f93883a16ac10cd598680d047fc3495d
  languageName: node
  linkType: hard

"eslint-plugin-unused-imports@npm:^3.0.0":
  version: 3.0.0
  resolution: "eslint-plugin-unused-imports@npm:3.0.0"
  dependencies:
    eslint-rule-composer: "npm:^0.3.0"
  peerDependencies:
    "@typescript-eslint/eslint-plugin": ^6.0.0
    eslint: ^8.0.0
  peerDependenciesMeta:
    "@typescript-eslint/eslint-plugin":
      optional: true
  checksum: 9433b80d4efdf3f8e43a38a7662b279b310020f3a80ffd2bbc56a375804b367bedfbe5b611b1969963e2de3b392bf1f389e89d2af810594ea3ab913c7e219ba1
  languageName: node
  linkType: hard

"eslint-rule-composer@npm:^0.3.0":
  version: 0.3.0
  resolution: "eslint-rule-composer@npm:0.3.0"
  checksum: c751e71243c6750de553ca0f586a71c7e9d43864bcbd0536639f287332e3f1ed3337bb0db07020652fa90937ceb63b6cc14c0f71fb227e8fc20ca44ee67e837f
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.2.2":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 5c660fb905d5883ad018a6fea2b49f3cb5b1cbf2cd4bd08e98646e9864f9bc2c74c0839bed2d292e90a4a328833accc197c8f0baed89cbe8d605d6f918465491
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 3f357c554a9ea794b094a09bd4187e5eacd1bc0d0653c3adeb87962c548e6a1ab8f982b86963ae1337f5d976004146536dcee5d0e2806665b193fbfbf1a9231b
  languageName: node
  linkType: hard

"eslint@npm:^8.50.0":
  version: 8.55.0
  resolution: "eslint@npm:8.55.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.6.1"
    "@eslint/eslintrc": "npm:^2.1.4"
    "@eslint/js": "npm:8.55.0"
    "@humanwhocodes/config-array": "npm:^0.11.13"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@nodelib/fs.walk": "npm:^1.2.8"
    "@ungap/structured-clone": "npm:^1.2.0"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.2"
    debug: "npm:^4.3.2"
    doctrine: "npm:^3.0.0"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^7.2.2"
    eslint-visitor-keys: "npm:^3.4.3"
    espree: "npm:^9.6.1"
    esquery: "npm:^1.4.2"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^6.0.1"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    globals: "npm:^13.19.0"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    is-path-inside: "npm:^3.0.3"
    js-yaml: "npm:^4.1.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    levn: "npm:^0.4.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
    strip-ansi: "npm:^6.0.1"
    text-table: "npm:^0.2.0"
  bin:
    eslint: bin/eslint.js
  checksum: afd016cfbe9e9d667b3f98c14c681a7e518808f6c30856e56cbb02248900eac5bf6dc5e577a7eaec259539486db48ef7d16ef58fb14b1585ba7c84b35490c53c
  languageName: node
  linkType: hard

"espree@npm:^9.6.0, espree@npm:^9.6.1":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: "npm:^8.9.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^3.4.1"
  checksum: 255ab260f0d711a54096bdeda93adff0eadf02a6f9b92f02b323e83a2b7fc258797919437ad331efec3930475feb0142c5ecaaf3cdab4befebd336d47d3f3134
  languageName: node
  linkType: hard

"esquery@npm:^1.4.2":
  version: 1.5.0
  resolution: "esquery@npm:1.5.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: e65fcdfc1e0ff5effbf50fb4f31ea20143ae5df92bb2e4953653d8d40aa4bc148e0d06117a592ce4ea53eeab1dafdfded7ea7e22a5be87e82d73757329a1b01d
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 44ffcd89e714ea6b30143e7f119b104fc4d75e77ee913f34d59076b40ef2d21967f84e019f84e1fd0465b42cdbf725db449f232b5e47f29df29ed76194db8e16
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 37cbe6e9a68014d34dbdc039f90d0baf72436809d02edffcc06ba3c2a12eb298048f877511353b130153e532aac8d68ba78430c0dd2f44806ebc7c014b01585e
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: b23acd24791db11d8f65be5ea58fd9a6ce2df5120ae2da65c16cfc5331ff59d5ac4ef50af66cd4bde238881503ec839928a0135b99a036a9cdfa22d17fd56cdb
  languageName: node
  linkType: hard

"eventemitter3@npm:^2.0.3":
  version: 2.0.3
  resolution: "eventemitter3@npm:2.0.3"
  checksum: 828903ea7e5115f1870d3d28508c42c9a93f9bc7ce37a46764f9d055ab3172ec0321cba07948c1eea30c3a64008455de0f92057a404889ec3a2556bbf151d8ff
  languageName: node
  linkType: hard

"exact-trie@npm:^1.0.13":
  version: 1.0.13
  resolution: "exact-trie@npm:1.0.13"
  checksum: 3a543a52d5d993e8a19d577aaca70552d0c02c69525c72c5c1e7405ba42da0f0fd1eb8a33567944956ad475e667dfe8dd54c1eee7a8697cd7b67d2ca72f4e429
  languageName: node
  linkType: hard

"exceljs@npm:^4.4.0":
  version: 4.4.0
  resolution: "exceljs@npm:4.4.0"
  dependencies:
    archiver: "npm:^5.0.0"
    dayjs: "npm:^1.8.34"
    fast-csv: "npm:^4.3.1"
    jszip: "npm:^3.10.1"
    readable-stream: "npm:^3.6.0"
    saxes: "npm:^5.0.1"
    tmp: "npm:^0.2.0"
    unzipper: "npm:^0.10.11"
    uuid: "npm:^8.3.0"
  checksum: 2d310146130b2af25b9a553185062a4095f6fee9a931c18a22b8dccd8a244056f3d2766f44227b9a43c0f52d651d05c5c96291fd38b8ed0a0322afab683fd80c
  languageName: node
  linkType: hard

"execa@npm:^5.0.0":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.0"
    human-signals: "npm:^2.1.0"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.1"
    onetime: "npm:^5.1.2"
    signal-exit: "npm:^3.0.3"
    strip-final-newline: "npm:^2.0.0"
  checksum: 8ada91f2d70f7dff702c861c2c64f21dfdc1525628f3c0454fd6f02fce65f7b958616cbd2b99ca7fa4d474e461a3d363824e91b3eb881705231abbf387470597
  languageName: node
  linkType: hard

"execa@npm:^7.1.1":
  version: 7.2.0
  resolution: "execa@npm:7.2.0"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.1"
    human-signals: "npm:^4.3.0"
    is-stream: "npm:^3.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^5.1.0"
    onetime: "npm:^6.0.0"
    signal-exit: "npm:^3.0.7"
    strip-final-newline: "npm:^3.0.0"
  checksum: 473feff60f9d4dbe799225948de48b5158c1723021d19c4b982afe37bcd111ae84e1b4c9dfe967fae5101b0894b1a62e4dd564a286dfa3e46d7b0cfdbf7fe62b
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 2d9bbb6473de7051f96790d5f9a678f32e60ed0aa70741dc7fdc96fec8d631124ec3374ac144387604f05afff9500f31a1d45bd9eee4cdc2e4f9ad2d9b9d5dbd
  languageName: node
  linkType: hard

"extend@npm:^3.0.2":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 59e89e2dc798ec0f54b36d82f32a27d5f6472c53974f61ca098db5d4648430b725387b53449a34df38fd0392045434426b012f302b3cc049a6500ccf82877e4e
  languageName: node
  linkType: hard

"fast-csv@npm:^4.3.1":
  version: 4.3.6
  resolution: "fast-csv@npm:4.3.6"
  dependencies:
    "@fast-csv/format": "npm:4.3.5"
    "@fast-csv/parse": "npm:4.3.6"
  checksum: eaa7ae48b3c7087f01a4827c5e0ad630685d0fada2f93489b2da1dcecd56b758eeb445a245f48a43e18815a03e8b848ecbc3951a65e60fed381d9056d9aa6768
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-diff@npm:1.1.2":
  version: 1.1.2
  resolution: "fast-diff@npm:1.1.2"
  checksum: d212e75beac8f3f271f03c91068686a3a6bfa86af0c3f1348faf689c0fb2a31583a5234b0b4014918d2aff2076bbda900a8f3065ee498347709cc2528e970ebd
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: 9e57415bc69cd6efcc720b3b8fe9fdaf42dcfc06f86f0f45378b1fa512598a8aac48aa3928c8751d58e2f01bb4ba4f07e4f3d9bc0d57586d45f1bd1e872c6cde
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.9, fast-glob@npm:^3.3.0, fast-glob@npm:^3.3.1":
  version: 3.3.2
  resolution: "fast-glob@npm:3.3.2"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.4"
  checksum: 222512e9315a0efca1276af9adb2127f02105d7288fa746145bf45e2716383fb79eb983c89601a72a399a56b7c18d38ce70457c5466218c5f13fad957cee16df
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 2c20055c1fa43c922428f16ca8bb29f2807de63e5c851f665f7ac9790176c01c3b40335257736b299764a8d383388dabc73c8083b8e1bc3d99f0a941444ec60e
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: eb7e220ecf2bab5159d157350b81d01f75726a4382f5a9266f42b9150c4523b9795f7f5d9fbbbeaeac09a441b2369f05ee02db48ea938584205530fe5693cfe1
  languageName: node
  linkType: hard

"fast-sort@npm:^2.2.0":
  version: 2.2.0
  resolution: "fast-sort@npm:2.2.0"
  checksum: 609210b138df35fb0fa79c1ff1fccd28e4c07ef98ec1f62263cc99d7ffc8d0fabbcfbeeae25eb717c35215c546639a091669c998cccee6f4d03db5be095deaa7
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.15.0
  resolution: "fastq@npm:1.15.0"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 67c01b1c972e2d5b6fea197a1a39d5d582982aea69ff4c504badac71080d8396d4843b165a9686e907c233048f15a86bbccb0e7f83ba771f6fa24bcde059d0c3
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: "npm:^3.0.4"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"filesize@npm:^6.1.0":
  version: 6.4.0
  resolution: "filesize@npm:6.4.0"
  checksum: 20ddfd66059aa4cdd0db34e36adb368696811d083bbc31952f64f33f5f7792d8351ab33cd47416ef478acfd869cf77eee47235749090ad9b1cac26d2c18a05e5
  languageName: node
  linkType: hard

"fill-range@npm:^7.0.1":
  version: 7.0.1
  resolution: "fill-range@npm:7.0.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: e260f7592fd196b4421504d3597cc76f4a1ca7a9488260d533b611fc3cefd61e9a9be1417cb82d3b01ad9f9c0ff2dbf258e1026d2445e26b0cf5148ff4250429
  languageName: node
  linkType: hard

"find-root@npm:^1.1.0":
  version: 1.1.0
  resolution: "find-root@npm:1.1.0"
  checksum: caa799c976a14925ba7f31ca1a226fe73d3aa270f4f1b623fcfeb1c6e263111db4beb807d8acd31bd4d48d44c343b93688a9288dfbccca27463c36a0301b0bb9
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4":
  version: 3.2.0
  resolution: "flat-cache@npm:3.2.0"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.3"
    rimraf: "npm:^3.0.2"
  checksum: 02381c6ece5e9fa5b826c9bbea481d7fd77645d96e4b0b1395238124d581d10e56f17f723d897b6d133970f7a57f0fab9148cbbb67237a0a0ffe794ba60c0c70
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.2.9
  resolution: "flatted@npm:3.2.9"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.0":
  version: 1.15.3
  resolution: "follow-redirects@npm:1.15.3"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 60d98693f4976892f8c654b16ef6d1803887a951898857ab0cdc009570b1c06314ad499505b7a040ac5b98144939f8597766e5e6a6859c0945d157b473aa6f5f
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3":
  version: 0.3.3
  resolution: "for-each@npm:0.3.3"
  dependencies:
    is-callable: "npm:^1.1.3"
  checksum: fdac0cde1be35610bd635ae958422e8ce0cc1313e8d32ea6d34cfda7b60850940c1fd07c36456ad76bd9c24aef6ff5e03b02beb58c83af5ef6c968a64eada676
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.1.1
  resolution: "foreground-child@npm:3.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.0"
    signal-exit: "npm:^4.0.1"
  checksum: 087edd44857d258c4f73ad84cb8df980826569656f2550c341b27adf5335354393eec24ea2fabd43a253233fb27cee177ebe46bd0b7ea129c77e87cb1e9936fb
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.0
  resolution: "form-data@npm:4.0.0"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    mime-types: "npm:^2.1.12"
  checksum: 7264aa760a8cf09482816d8300f1b6e2423de1b02bba612a136857413fdc96d7178298ced106817655facc6b89036c6e12ae31c9eb5bdc16aabf502ae8a5d805
  languageName: node
  linkType: hard

"framer-motion@npm:^10.16.4":
  version: 10.16.15
  resolution: "framer-motion@npm:10.16.15"
  dependencies:
    "@emotion/is-prop-valid": "npm:^0.8.2"
    tslib: "npm:^2.4.0"
  peerDependencies:
    react: ^18.0.0
    react-dom: ^18.0.0
  dependenciesMeta:
    "@emotion/is-prop-valid":
      optional: true
  peerDependenciesMeta:
    react:
      optional: true
    react-dom:
      optional: true
  checksum: 2e125fe02ac65e9767fa603d4a97d9f23cb4ab808b300d1a27d392294a565f3b202c2c072aee743837acbb9d731515aa45b721f5e280602e3ae1374ed164b4ee
  languageName: node
  linkType: hard

"fs-constants@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs-constants@npm:1.0.0"
  checksum: 18f5b718371816155849475ac36c7d0b24d39a11d91348cfcb308b4494824413e03572c403c86d3a260e049465518c4f0d5bd00f0371cdfcad6d4f30a85b350d
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 03191781e94bc9a54bd376d3146f90fe8e082627c502185dbf7b9b3032f66b0b142c1115f3b2cc5936575fc1b44845ce903dd4c21bec2a8d69f3bd56f9cee9ec
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: af143246cf6884fe26fa281621d45cfe111d34b30535a475bfa38dafe343dadb466c047a924ffc7d6b7b18265df4110224ce3803806dbb07173bf2087b648d7f
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: e703107c28e362d8d7b910bbcbfd371e640a3bb45ae157a362b5952c0030c0b6d4981140ec319b347bce7adc025dd7813da1ff908a945ac214d64f5402a51b96
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 4c1ade961ded57cdbfbb5cac5106ec17bc8bccd62e16343c569a0ceeca83b9dfef87550b4dc5cbb89642da412b20c5071f304c8c464b80415446e8e155a038c0
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"fstream@npm:^1.0.12":
  version: 1.0.12
  resolution: "fstream@npm:1.0.12"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    inherits: "npm:~2.0.0"
    mkdirp: "npm:>=0.5 0"
    rimraf: "npm:2"
  checksum: eadba4375e952f3f7e9d34d822cfa1592134173033bafef42aa23d5f09bf373e4eb77e097883c0a9136ad7e7d3b49bb14f0e8dfaa489abd5139b5a3c961787b6
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.1, function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 185e20d20f10c8d661d59aac0f3b63b31132d492e1b11fcc2a93cb2c47257ebaee7407c38513efd2b35cafdf972d9beb2ea4593c1e0f3bf8f2744836928d7454
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.5, function.prototype.name@npm:^1.1.6":
  version: 1.1.6
  resolution: "function.prototype.name@npm:1.1.6"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
    functions-have-names: "npm:^1.2.3"
  checksum: 4d40be44d4609942e4e90c4fff77a811fa936f4985d92d2abfcf44f673ba344e2962bf223a33101f79c1a056465f36f09b072b9c289d7660ca554a12491cd5a2
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 0ddfd3ed1066a55984aaecebf5419fbd9344a5c38dd120ffb0739fac4496758dcf371297440528b115e4367fc46e3abc86a2cc0ff44612181b175ae967a11a05
  languageName: node
  linkType: hard

"fuzzy-search@npm:^3.2.1":
  version: 3.2.1
  resolution: "fuzzy-search@npm:3.2.1"
  checksum: 78ca6740a8c456588a42bc02036a4b2ec968b6f3855104417dc7cd35fc7276298b8d1b16789a5af0b5e360e603eccc5ffc687f723fb87e5ba3984de63ed6b0ee
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 17d8333460204fbf1f9160d067e1e77f908a5447febb49424b8ab043026049835c9ef3974445c57dbd39161f4d2b04356d7de12b2eecaa27a7a7ea7d871cbedd
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.0.2, get-intrinsic@npm:^1.1.1, get-intrinsic@npm:^1.1.3, get-intrinsic@npm:^1.2.0, get-intrinsic@npm:^1.2.1, get-intrinsic@npm:^1.2.2":
  version: 1.2.2
  resolution: "get-intrinsic@npm:1.2.2"
  dependencies:
    function-bind: "npm:^1.1.2"
    has-proto: "npm:^1.0.1"
    has-symbols: "npm:^1.0.3"
    hasown: "npm:^2.0.0"
  checksum: aa96db4f809734d26d49b59bc8669d73a0ae792da561514e987735573a1dfaede516cd102f217a078ea2b42d4c4fb1f83d487932cb15d49826b726cc9cd4470b
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0, get-stream@npm:^6.0.1":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 781266d29725f35c59f1d214aedc92b0ae855800a980800e2923b3fbc4e56b3cb6e462c42e09a1cf1a00c64e056a78fa407cbe06c7c92b7e5cd49b4b85c2a497
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.0.0":
  version: 1.0.0
  resolution: "get-symbol-description@npm:1.0.0"
  dependencies:
    call-bind: "npm:^1.0.2"
    get-intrinsic: "npm:^1.1.1"
  checksum: 7e5f298afe0f0872747dce4a949ce490ebc5d6dd6aefbbe5044543711c9b19a4dfaebdbc627aee99e1299d58a435b2fbfa083458c1d58be6dc03a3bada24d359
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.5.0":
  version: 4.7.2
  resolution: "get-tsconfig@npm:4.7.2"
  dependencies:
    resolve-pkg-maps: "npm:^1.0.0"
  checksum: f21135848fb5d16012269b7b34b186af7a41824830f8616aba17a15eb4d9e54fdc876833f1e21768395215a826c8145582f5acd594ae2b4de3284d10b38d20f8
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 32cd106ce8c0d83731966d31517adb766d02c3812de49c30cfe0675c7c0ae6630c11214c54a5ae67aca882cf738d27fd7768f21aa19118b9245950554be07247
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: 9009529195a955c40d7b9690794aeff5ba665cc38f1519e111c58bb54366fd0c106bde80acf97ba4e533208eb53422c83b136611a54c5fefb1edd8dc267cb62e
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10":
  version: 10.3.10
  resolution: "glob@npm:10.3.10"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^2.3.5"
    minimatch: "npm:^9.0.1"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
    path-scurry: "npm:^1.10.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 38bdb2c9ce75eb5ed168f309d4ed05b0798f640b637034800a6bf306f39d35409bf278b0eaaffaec07591085d3acb7184a201eae791468f0f617771c2486a6a8
  languageName: node
  linkType: hard

"glob@npm:^7.1.3, glob@npm:^7.1.4, glob@npm:^7.2.3":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 59452a9202c81d4508a43b8af7082ca5c76452b9fcc4a9ab17655822e6ce9b21d4f8fbadabe4fe3faef448294cec249af305e2cd824b7e9aaf689240e5e96a7b
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 9f054fa38ff8de8fa356502eb9d2dae0c928217b8b5c8de1f09f5c9b6c8a96d8b9bd3afc49acbcd384a98a81fea713c859e1b09e214c60509517bb8fc2bc13c2
  languageName: node
  linkType: hard

"globals@npm:^13.19.0":
  version: 13.23.0
  resolution: "globals@npm:13.23.0"
  dependencies:
    type-fest: "npm:^0.20.2"
  checksum: bf6a8616f4a64959c0b9a8eb4dc8a02e7dd0082385f7f06bc9694d9fceabe39f83f83789322cfe0470914dc8b273b7a29af5570b9e1a0507d3fb7348a64703a3
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.3":
  version: 1.0.3
  resolution: "globalthis@npm:1.0.3"
  dependencies:
    define-properties: "npm:^1.1.3"
  checksum: 45ae2f3b40a186600d0368f2a880ae257e8278b4c7704f0417d6024105ad7f7a393661c5c2fa1334669cd485ea44bc883a08fdd4516df2428aec40c99f52aa89
  languageName: node
  linkType: hard

"globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: "npm:^2.1.0"
    dir-glob: "npm:^3.0.1"
    fast-glob: "npm:^3.2.9"
    ignore: "npm:^5.2.0"
    merge2: "npm:^1.4.1"
    slash: "npm:^3.0.0"
  checksum: 288e95e310227bbe037076ea81b7c2598ccbc3122d87abc6dab39e1eec309aa14f0e366a98cdc45237ffcfcbad3db597778c0068217dcb1950fef6249104e1b1
  languageName: node
  linkType: hard

"goober@npm:^2.0.33":
  version: 2.1.13
  resolution: "goober@npm:2.1.13"
  peerDependencies:
    csstype: ^3.0.10
  checksum: 0f073d7271bf1b53ec0db3744a0596c40889b01cad2db0a73709d24e081d3f47585eb21e3f5dd873138c12c587469f9a73b25c94681d0a57533063e038bf00ed
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1":
  version: 1.0.1
  resolution: "gopd@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.1.3"
  checksum: 5fbc7ad57b368ae4cd2f41214bd947b045c1a4be2f194a7be1778d71f8af9dbf4004221f3b6f23e30820eb0d052b4f819fe6ebe8221e2a3c6f0ee4ef173421ca
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.11, graceful-fs@npm:^4.2.2, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: bf152d0ed1dc159239db1ba1f74fdbc40cb02f626770dcd5815c427ce0688c2635a06ed69af364396da4636d0408fcf7d4afdf7881724c3307e46aff30ca49e2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 6dd60dba97007b21e3a829fab3f771803cc1292977fe610e240ea72afd67e5690ac9eeaafc4a99710e78962e5936ab5a460787c2a1180f1cb0ccfac37d29f897
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.1, has-bigints@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-bigints@npm:1.0.2"
  checksum: 4e0426c900af034d12db14abfece02ce7dbf53f2022d28af1a97913ff4c07adb8799476d57dc44fbca0e07d1dbda2a042c2928b1f33d3f09c15de0640a7fb81b
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0":
  version: 1.0.1
  resolution: "has-property-descriptors@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.2"
  checksum: 21a47bb080a24e79594aef1ce71e1a18a1c5ab4120308e218088f67ebb7f6f408847541e2d96e5bd00e90eef5c5a49e4ebbdc8fc2d5b365a2c379aef071642f0
  languageName: node
  linkType: hard

"has-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "has-proto@npm:1.0.1"
  checksum: eab2ab0ed1eae6d058b9bbc4c1d99d2751b29717be80d02fd03ead8b62675488de0c7359bc1fdd4b87ef6fd11e796a9631ad4d7452d9324fdada70158c2e5be7
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.2, has-symbols@npm:^1.0.3":
  version: 1.0.3
  resolution: "has-symbols@npm:1.0.3"
  checksum: 464f97a8202a7690dadd026e6d73b1ceeddd60fe6acfd06151106f050303eaa75855aaa94969df8015c11ff7c505f196114d22f7386b4a471038da5874cf5e9b
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-tostringtag@npm:1.0.0"
  dependencies:
    has-symbols: "npm:^1.0.2"
  checksum: 95546e7132efc895a9ae64a8a7cf52588601fc3d52e0304ed228f336992cdf0baaba6f3519d2655e560467db35a1ed79f6420c286cc91a13aa0647a31ed92570
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0":
  version: 2.0.0
  resolution: "hasown@npm:2.0.0"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: c330f8d93f9d23fe632c719d4db3d698ef7d7c367d51548b836069e06a90fa9151e868c8e67353cfe98d67865bf7354855db28fa36eb1b18fa5d4a3f4e7f1c90
  languageName: node
  linkType: hard

"highlight-words@npm:1.2.2":
  version: 1.2.2
  resolution: "highlight-words@npm:1.2.2"
  checksum: a5431c550a6d523ecb54eba952075790ddbf0cab9db6ba9e2c6928407ff2a5b27f07169ef2634eb56aaae42b396291acc62381956d152392350c94432dd8d247
  languageName: node
  linkType: hard

"highlight.js@npm:^11.9.0":
  version: 11.9.0
  resolution: "highlight.js@npm:11.9.0"
  checksum: 44b3e42bc096a2e5207e514826a10ff7671de315a7216ebaeba76593d4f16dfe3f7078390cb5c2b08eae657694fef4fb65d775376db48480c829c83fbc4f157a
  languageName: node
  linkType: hard

"hoist-non-react-statics@npm:^3.0.0, hoist-non-react-statics@npm:^3.2.0, hoist-non-react-statics@npm:^3.3.0, hoist-non-react-statics@npm:^3.3.1, hoist-non-react-statics@npm:^3.3.2":
  version: 3.3.2
  resolution: "hoist-non-react-statics@npm:3.3.2"
  dependencies:
    react-is: "npm:^16.7.0"
  checksum: 1acbe85f33e5a39f90c822ad4d28b24daeb60f71c545279431dc98c312cd28a54f8d64788e477fe21dc502b0e3cf58589ebe5c1ad22af27245370391c2d24ea6
  languageName: node
  linkType: hard

"hotkeys-js@npm:^3.8.3":
  version: 3.13.3
  resolution: "hotkeys-js@npm:3.13.3"
  checksum: 35f86165730346eb9bdf353316f13a7df4ed5ba3887b4f61e8e690da6beea242547cc728c69a8b9e8304b28f02a4654635fd9327494474a812e595308571d030
  languageName: node
  linkType: hard

"html-parse-stringify@npm:^3.0.1":
  version: 3.0.1
  resolution: "html-parse-stringify@npm:3.0.1"
  dependencies:
    void-elements: "npm:3.1.0"
  checksum: 8743b76cc50e46d1956c1ad879d18eb9613b0d2d81e24686d633f9f69bb26b84676f64a926973de793cca479997017a63219278476d617b6c42d68246d7c07fe
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 362d5ed66b12ceb9c0a328fb31200b590ab1b02f4a254a697dc796850cc4385603e75f53ec59f768b2dad3bfa1464bd229f7de278d2899a0e3beffc634b6683f
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.0
  resolution: "http-proxy-agent@npm:7.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: dbaaf3d9f3fc4df4a5d7ec45d456ec50f575240b557160fa63427b447d1f812dd7fe4a4f17d2e1ba003d231f07edf5a856ea6d91cb32d533062ff20a7803ccac
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.2
  resolution: "https-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.0.2"
    debug: "npm:4"
  checksum: 9ec844f78fd643608239c9c3f6819918631df5cd3e17d104cc507226a39b5d4adda9d790fc9fd63ac0d2bb8a761b2f9f60faa80584a9bf9d7f2e8c5ed0acd330
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: df59be9e0af479036798a881d1f136c4a29e0b518d4abb863afbd11bf30efa3eeb1d0425fc65942dcc05ab3bf40205ea436b0ff389f2cd20b75b8643d539bf86
  languageName: node
  linkType: hard

"human-signals@npm:^4.3.0":
  version: 4.3.1
  resolution: "human-signals@npm:4.3.1"
  checksum: fa59894c358fe9f2b5549be2fb083661d5e1dff618d3ac70a49ca73495a72e873fbf6c0878561478e521e17d498292746ee391791db95ffe5747bfb5aef8765b
  languageName: node
  linkType: hard

"hyphenate-style-name@npm:^1.0.3":
  version: 1.0.4
  resolution: "hyphenate-style-name@npm:1.0.4"
  checksum: d37883e6b7e1be62e1ddae29cac83fa59fb93c068bc8eb1561585439adbad91dcf7e264ee2a82c4378fc58049f7bd853544a4a81bf00d4aff717f641052323e7
  languageName: node
  linkType: hard

"i18next-browser-languagedetector@npm:^7.2.0":
  version: 7.2.0
  resolution: "i18next-browser-languagedetector@npm:7.2.0"
  dependencies:
    "@babel/runtime": "npm:^7.23.2"
  checksum: 5117b4961e0f32818f0d4587e81767d38c3a8e27305f1734fff2b07fe8c256161e2cdbd453b766b3c097055813fe89c43bce68b1d8f765b5b7f694d9852fe703
  languageName: node
  linkType: hard

"i18next@npm:^23.7.7":
  version: 23.7.8
  resolution: "i18next@npm:23.7.8"
  dependencies:
    "@babel/runtime": "npm:^7.23.2"
  checksum: 7cf6a62074b5855725f77132400344f8df4913f097d586d3199675d85c46ab85e71b02e5c8b42787d7d4c15afe32f05ece789e377bf96b35b2068f796d60f252
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 24e3292dd3dadaa81d065c6f8c41b274a47098150d444b96e5f53b4638a9a71482921ea6a91a1f59bb71d9796de25e04afd05919fa64c360347ba65d3766f10f
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: d9f2557a59036f16c282aaeb107832dc957a93d73397d89bbad4eb1130560560eb695060145e8e6b3b498b15ab95510226649a0b8f52ae06583575419fe10fc4
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0, ignore@npm:^5.2.4":
  version: 5.3.0
  resolution: "ignore@npm:5.3.0"
  checksum: 51594355cea4c6ad6b28b3b85eb81afa7b988a1871feefd7062baf136c95aa06760ee934fa9590e43d967bd377ce84a4cf6135fbeb6063e063f1182a0e9a3bcd
  languageName: node
  linkType: hard

"immediate@npm:~3.0.5":
  version: 3.0.6
  resolution: "immediate@npm:3.0.6"
  checksum: f9b3486477555997657f70318cc8d3416159f208bec4cca3ff3442fd266bc23f50f0c9bd8547e1371a6b5e82b821ec9a7044a4f7b944798b25aa3cc6d5e63e62
  languageName: node
  linkType: hard

"immer@npm:^9.0.21":
  version: 9.0.21
  resolution: "immer@npm:9.0.21"
  checksum: 8455d6b4dc8abfe40f06eeec9bcc944d147c81279424c0f927a4d4905ae34e5af19ab6da60bcc700c14f51c452867d7089b3b9236f5a9a2248e39b4a09ee89de
  languageName: node
  linkType: hard

"immutable@npm:4.3.4, immutable@npm:^4.0.0":
  version: 4.3.4
  resolution: "immutable@npm:4.3.4"
  checksum: ea187acc1eec9dcfaa0823bae59e1ae0ea82e7a40d2ace9fb84d467875d5506ced684a79b68e70451f1e1761a387a958ba724171f93aa10330998b026fcb5d29
  languageName: node
  linkType: hard

"immutable@npm:^5.0.0-beta.4":
  version: 5.0.0-beta.4
  resolution: "immutable@npm:5.0.0-beta.4"
  checksum: d54049297965eb1763664f655f79ee8d9d6c3bd43a1cf71c9e5581b4f3656f188d5725d5ac36eaa7cf26286b8dc074c3a6aade021ebac121c47b86c055e95c9f
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1, import-fresh@npm:^3.3.0":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 2d30b157a91fe1c1d7c6f653cbf263f039be6c5bfa959245a16d4ee191fc0f2af86c08545b6e6beeb041c56b574d2d5b9f95343d378ab49c0f37394d541e7fc8
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: cd3f5cbc9ca2d624c6a1f53f12e6b341659aba0e2d3254ae2b4464aaea8b4294cdb09616abbc59458f980531f2429784ed6a420d48d245bcad0811980c9efae9
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: d2ebd65441a38c8336c223d1b80b921b9fa737e37ea466fd7e253cb000c64ae1f17fa59e68130ef5bda92cfd8d36b83d37dab0eb0a4558bcfec8e8cdfd2dcb67
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.0, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: cd45e923bee15186c07fa4c89db0aace24824c482fb887b528304694b2aa6ff8a898da8657046a5dcf3e46cd6db6c61629551f9215f208d7c3f157cf9b290521
  languageName: node
  linkType: hard

"internal-slot@npm:^1.0.5":
  version: 1.0.6
  resolution: "internal-slot@npm:1.0.6"
  dependencies:
    get-intrinsic: "npm:^1.2.2"
    hasown: "npm:^2.0.0"
    side-channel: "npm:^1.0.4"
  checksum: bc2022eb1f277f2fcb2a60e7ced451c7ffc7a769b12e63c7a3fb247af8b5a1bed06428ce724046a8bca39ed6eb5b6832501a42f2e9a5ec4a9a7dc4e634431616
  languageName: node
  linkType: hard

"intl-messageformat@npm:9.13.0":
  version: 9.13.0
  resolution: "intl-messageformat@npm:9.13.0"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:1.11.4"
    "@formatjs/fast-memoize": "npm:1.2.1"
    "@formatjs/icu-messageformat-parser": "npm:2.1.0"
    tslib: "npm:^2.1.0"
  checksum: 54ad97038cf1a31c140ed2557eb76cd0a43a69c9f4b6bea308cb29bd74e8650f5ed979c7e0eb2f468e0992944adbf4c17a4dbcda20ec2f3ace79f3aa58756893
  languageName: node
  linkType: hard

"ip@npm:^2.0.0":
  version: 2.0.0
  resolution: "ip@npm:2.0.0"
  checksum: 1270b11e534a466fb4cf4426cbcc3a907c429389f7f4e4e3b288b42823562e88d6a509ceda8141a507de147ca506141f745005c0aa144569d94cf24a54eb52bc
  languageName: node
  linkType: hard

"is-arguments@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-arguments@npm:1.1.1"
  dependencies:
    call-bind: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.0"
  checksum: a170c7e26082e10de9be6e96d32ae3db4d5906194051b792e85fae3393b53cf2cb5b3557863e5c8ccbab55e2fd8f2f75aa643d437613f72052cf0356615c34be
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.1, is-array-buffer@npm:^3.0.2":
  version: 3.0.2
  resolution: "is-array-buffer@npm:3.0.2"
  dependencies:
    call-bind: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.10"
  checksum: dcac9dda66ff17df9cabdc58214172bf41082f956eab30bb0d86bc0fab1e44b690fc8e1f855cf2481245caf4e8a5a006a982a71ddccec84032ed41f9d8da8c14
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 73ced84fa35e59e2c57da2d01e12cd01479f381d7f122ce41dcbb713f09dbfc651315832cd2bf8accba7681a69e4d6f1e03941d94dd10040d415086360e7005e
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-async-function@npm:2.0.0"
  dependencies:
    has-tostringtag: "npm:^1.0.0"
  checksum: 2cf336fbf8cba3badcf526aa3d10384c30bab32615ac4831b74492eb4e843ccb7d8439a119c27f84bcf217d72024e611b1373f870f433b48f3fa57d3d1b863f1
  languageName: node
  linkType: hard

"is-bigint@npm:^1.0.1":
  version: 1.0.4
  resolution: "is-bigint@npm:1.0.4"
  dependencies:
    has-bigints: "npm:^1.0.1"
  checksum: cc981cf0564c503aaccc1e5f39e994ae16ae2d1a8fcd14721f14ad431809071f39ec568cfceef901cff408045f1a6d6bac90d1b43eeb0b8e3bc34c8eb1bdb4c4
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 078e51b4f956c2c5fd2b26bb2672c3ccf7e1faff38e0ebdba45612265f4e3d9fc3127a1fa8370bbf09eab61339203c3d3b7af5662cbf8be4030f8fac37745b0e
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.1.0":
  version: 1.1.2
  resolution: "is-boolean-object@npm:1.1.2"
  dependencies:
    call-bind: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.0"
  checksum: ba794223b56a49a9f185e945eeeb6b7833b8ea52a335cec087d08196cf27b538940001615d3bb976511287cefe94e5907d55f00bb49580533f9ca9b4515fcc2e
  languageName: node
  linkType: hard

"is-callable@npm:^1.1.3, is-callable@npm:^1.1.4, is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 48a9297fb92c99e9df48706241a189da362bff3003354aea4048bd5f7b2eb0d823cd16d0a383cece3d76166ba16d85d9659165ac6fcce1ac12e6c649d66dbdb9
  languageName: node
  linkType: hard

"is-core-module@npm:^2.11.0, is-core-module@npm:^2.13.0, is-core-module@npm:^2.13.1":
  version: 2.13.1
  resolution: "is-core-module@npm:2.13.1"
  dependencies:
    hasown: "npm:^2.0.0"
  checksum: d53bd0cc24b0a0351fb4b206ee3908f71b9bbf1c47e9c9e14e5f06d292af1663704d2abd7e67700d6487b2b7864e0d0f6f10a1edf1892864bdffcb197d1845a2
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.1, is-date-object@npm:^1.0.5":
  version: 1.0.5
  resolution: "is-date-object@npm:1.0.5"
  dependencies:
    has-tostringtag: "npm:^1.0.0"
  checksum: cc80b3a4b42238fa0d358b9a6230dae40548b349e64a477cb7c5eff9b176ba194c11f8321daaf6dd157e44073e9b7fd01f87db1f14952a88d5657acdcd3a56e2
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 3fef7ddbf0be25958e8991ad941901bf5922ab2753c46980b60b05c1bf9c9c2402d35e6dc32e4380b980ef5e1970a5d9d5e5aa2e02d77727c3b6b5e918474c56
  languageName: node
  linkType: hard

"is-docker@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-docker@npm:3.0.0"
  bin:
    is-docker: cli.js
  checksum: b698118f04feb7eaf3338922bd79cba064ea54a1c3db6ec8c0c8d8ee7613e7e5854d802d3ef646812a8a3ace81182a085dfa0a71cc68b06f3fa794b9783b3c90
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-finalizationregistry@npm:1.0.2"
  dependencies:
    call-bind: "npm:^1.0.2"
  checksum: 1b8e9e1bf2075e862315ef9d38ce6d39c43ca9d81d46f73b34473506992f4b0fbaadb47ec9b420a5e76afe3f564d9f1f0d9b552ef272cc2395e0f21d743c9c29
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.0.10
  resolution: "is-generator-function@npm:1.0.10"
  dependencies:
    has-tostringtag: "npm:^1.0.0"
  checksum: 499a3ce6361064c3bd27fbff5c8000212d48506ebe1977842bbd7b3e708832d0deb1f4cc69186ece3640770e8c4f1287b24d99588a0b8058b2dbdd344bc1f47f
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 3ed74f2b0cdf4f401f38edb0442ddfde3092d79d7d35c9919c86641efdbcbb32e45aa3c0f70ce5eecc946896cd5a0f26e4188b9f2b881876f7cb6c505b82da11
  languageName: node
  linkType: hard

"is-in-browser@npm:^1.0.2, is-in-browser@npm:^1.1.3":
  version: 1.1.3
  resolution: "is-in-browser@npm:1.1.3"
  checksum: f7411dbea0875ac609a794e28a545d654331e2a51f42a8c1629abbedf21ecb642ca726a4a9c8be28f9854990a764693483652b65612ccf7b5bc68aa4657e9a26
  languageName: node
  linkType: hard

"is-inside-container@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-inside-container@npm:1.0.0"
  dependencies:
    is-docker: "npm:^3.0.0"
  bin:
    is-inside-container: cli.js
  checksum: c50b75a2ab66ab3e8b92b3bc534e1ea72ca25766832c0623ac22d134116a98bcf012197d1caabe1d1c4bd5f84363d4aa5c36bb4b585fbcaf57be172cd10a1a03
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 93a32f01940220532e5948538699ad610d5924ac86093fcee83022252b363eb0cc99ba53ab084a04e4fb62bf7b5731f55496257a4c38adf87af9c4d352c71c35
  languageName: node
  linkType: hard

"is-map@npm:^2.0.1":
  version: 2.0.2
  resolution: "is-map@npm:2.0.2"
  checksum: 60ba910f835f2eacb1fdf5b5a6c60fe1c702d012a7673e6546992bcc0c873f62ada6e13d327f9e48f1720d49c152d6cdecae1fa47a261ef3d247c3ce6f0e1d39
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-negative-zero@npm:2.0.2"
  checksum: edbec1a9e6454d68bf595a114c3a72343d2d0be7761d8173dae46c0b73d05bb8fe9398c85d121e7794a66467d2f40b4a610b0be84cd804262d234fc634c86131
  languageName: node
  linkType: hard

"is-number-object@npm:^1.0.4":
  version: 1.0.7
  resolution: "is-number-object@npm:1.0.7"
  dependencies:
    has-tostringtag: "npm:^1.0.0"
  checksum: 8700dcf7f602e0a9625830541345b8615d04953655acbf5c6d379c58eb1af1465e71227e95d501343346e1d49b6f2d53cbc166b1fc686a7ec19151272df582f9
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 6a6c3383f68afa1e05b286af866017c78f1226d43ac8cb064e115ff9ed85eb33f5c4f7216c96a71e4dfea289ef52c5da3aef5bbfade8ffe47a0465d70c0c8e86
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: abd50f06186a052b349c15e55b182326f1936c89a78bf6c8f2b707412517c097ce04bc49a0ca221787bc44e1049f51f09a2ffb63d22899051988d3a618ba13e9
  languageName: node
  linkType: hard

"is-regex@npm:^1.1.4":
  version: 1.1.4
  resolution: "is-regex@npm:1.1.4"
  dependencies:
    call-bind: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.0"
  checksum: 36d9174d16d520b489a5e9001d7d8d8624103b387be300c50f860d9414556d0485d74a612fdafc6ebbd5c89213d947dcc6b6bff6b2312093f71ea03cbb19e564
  languageName: node
  linkType: hard

"is-set@npm:^2.0.1":
  version: 2.0.2
  resolution: "is-set@npm:2.0.2"
  checksum: d89e82acdc7760993474f529e043f9c4a1d63ed4774d21cc2e331d0e401e5c91c27743cd7c889137028f6a742234759a4bd602368fbdbf0b0321994aefd5603f
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-shared-array-buffer@npm:1.0.2"
  dependencies:
    call-bind: "npm:^1.0.2"
  checksum: 23d82259d6cd6dbb7c4ff3e4efeff0c30dbc6b7f88698498c17f9821cb3278d17d2b6303a5341cbd638ab925a28f3f086a6c79b3df70ac986cc526c725d43b4f
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-stream@npm:3.0.0"
  checksum: 172093fe99119ffd07611ab6d1bcccfe8bc4aa80d864b15f43e63e54b7abc71e779acd69afdb854c4e2a67fdc16ae710e370eda40088d1cfc956a50ed82d8f16
  languageName: node
  linkType: hard

"is-string@npm:^1.0.5, is-string@npm:^1.0.7":
  version: 1.0.7
  resolution: "is-string@npm:1.0.7"
  dependencies:
    has-tostringtag: "npm:^1.0.0"
  checksum: 2bc292fe927493fb6dfc3338c099c3efdc41f635727c6ebccf704aeb2a27bca7acb9ce6fd34d103db78692b10b22111a8891de26e12bfa1c5e11e263c99d1fef
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.2, is-symbol@npm:^1.0.3":
  version: 1.0.4
  resolution: "is-symbol@npm:1.0.4"
  dependencies:
    has-symbols: "npm:^1.0.2"
  checksum: a47dd899a84322528b71318a89db25c7ecdec73197182dad291df15ffea501e17e3c92c8de0bfb50e63402747399981a687b31c519971b1fa1a27413612be929
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.10, is-typed-array@npm:^1.1.12, is-typed-array@npm:^1.1.9":
  version: 1.1.12
  resolution: "is-typed-array@npm:1.1.12"
  dependencies:
    which-typed-array: "npm:^1.1.11"
  checksum: d953adfd3c41618d5e01b2a10f21817e4cdc9572772fa17211100aebb3811b6e3c2e308a0558cc87d218a30504cb90154b833013437776551bfb70606fb088ca
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.1":
  version: 2.0.1
  resolution: "is-weakmap@npm:2.0.1"
  checksum: 289fa4e8ba1bdda40ca78481266f6925b7c46a85599e6a41a77010bf91e5a24dfb660db96863bbf655ecdbda0ab517204d6a4e0c151dbec9d022c556321f3776
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-weakref@npm:1.0.2"
  dependencies:
    call-bind: "npm:^1.0.2"
  checksum: 0023fd0e4bdf9c338438ffbe1eed7ebbbff7e7e18fb7cdc227caaf9d4bd024a2dcdf6a8c9f40c92192022eac8391243bb9e66cccebecbf6fe1d8a366108f8513
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.1":
  version: 2.0.2
  resolution: "is-weakset@npm:2.0.2"
  dependencies:
    call-bind: "npm:^1.0.2"
    get-intrinsic: "npm:^1.1.1"
  checksum: 8f2ddb9639716fd7936784e175ea1183c5c4c05274c34f34f6a53175313cb1c9c35a8b795623306995e2f7cc8f25aa46302f15a2113e51c5052d447be427195c
  languageName: node
  linkType: hard

"is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: "npm:^2.0.0"
  checksum: 20849846ae414997d290b75e16868e5261e86ff5047f104027026fd61d8b5a9b0b3ade16239f35e1a067b3c7cc02f70183cb661010ed16f4b6c7c93dad1b19d8
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 1d8bc7911e13bb9f105b1b3e0b396c787a9e63046af0b8fe0ab1414488ab06b2b099b87a2d8a9e31d21c9a6fad773c7fc8b257c4880f2d957274479d28ca3414
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 7c9f715c03aff08f35e98b1fadae1b9267b38f0615d501824f9743f3aab99ef10e303ce7db3f186763a0b70a19de5791ebfc854ff884d5a8c4d92211f642ec92
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"iterator.prototype@npm:^1.1.2":
  version: 1.1.2
  resolution: "iterator.prototype@npm:1.1.2"
  dependencies:
    define-properties: "npm:^1.2.1"
    get-intrinsic: "npm:^1.2.1"
    has-symbols: "npm:^1.0.3"
    reflect.getprototypeof: "npm:^1.0.4"
    set-function-name: "npm:^2.0.1"
  checksum: b5013967ad8f28c9ca1be8e159eb10f591b8e46deae87476fe39d668c04374fe9158c815e8b6d2f45885b0a3fd842a8ba13f497ec762b3a0eff49bec278670b1
  languageName: node
  linkType: hard

"jackspeak@npm:^2.3.5":
  version: 2.3.6
  resolution: "jackspeak@npm:2.3.6"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 6e6490d676af8c94a7b5b29b8fd5629f21346911ebe2e32931c2a54210134408171c24cee1a109df2ec19894ad04a429402a8438cbf5cc2794585d35428ace76
  languageName: node
  linkType: hard

"js-sha3@npm:0.8.0":
  version: 0.8.0
  resolution: "js-sha3@npm:0.8.0"
  checksum: a49ac6d3a6bfd7091472a28ab82a94c7fb8544cc584ee1906486536ba1cb4073a166f8c7bb2b0565eade23c5b3a7b8f7816231e0309ab5c549b737632377a20c
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: af37d0d913fb56aec6dc0074c163cc71cd23c0b8aad5c2350747b6721d37ba118af35abdd8b33c47ec2800de07dedb16a527ca9c530ee004093e04958bd0cbf2
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c138a34a3fd0d08ebaf71273ad4465569a483b8a639e0b118ff65698d257c2791d3199e3f303631f2cb98213fa7b5f5d6a4621fd0fff819421b990d30d967140
  languageName: node
  linkType: hard

"jsesc@npm:^2.5.1":
  version: 2.5.2
  resolution: "jsesc@npm:2.5.2"
  bin:
    jsesc: bin/jsesc
  checksum: d2096abdcdec56969764b40ffc91d4a23408aa2f351b4d1c13f736f25476643238c43fdbaf38a191c26b1b78fd856d965f5d4d0dde7b89459cd94025190cdf13
  languageName: node
  linkType: hard

"jsesc@npm:~0.5.0":
  version: 0.5.0
  resolution: "jsesc@npm:0.5.0"
  bin:
    jsesc: bin/jsesc
  checksum: fab949f585c71e169c5cbe00f049f20de74f067081bbd64a55443bad1c71e1b5a5b448f2359bf2fe06f5ed7c07e2e4a9101843b01c823c30b6afc11f5bfaf724
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 82876154521b7b68ba71c4f969b91572d1beabadd87bd3a6b236f85fbc7dc4695089191ed60bb59f9340993c51b33d479f45b6ba9f3548beb519705281c32c3c
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 5f3a99009ed5f2a5a67d06e2f298cc97bc86d462034173308156f15b43a6e850be8511dc204b9b94566305da2947f7d90289657237d210351a39059ff9d666cf
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 12786c2e2f22c27439e6db0532ba321f1d0617c27ad8cb1c352a0e9249a50182fd1ba8b52a18899291604b0c32eafa8afd09e51203f19109a0537f68db2b652d
  languageName: node
  linkType: hard

"json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: "npm:^1.2.0"
  bin:
    json5: lib/cli.js
  checksum: a78d812dbbd5642c4f637dd130954acfd231b074965871c3e28a5bbd571f099d623ecf9161f1960c4ddf68e0cc98dee8bebfdb94a71ad4551f85a1afc94b63f6
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 1db67b853ff0de3534085d630691d3247de53a2ed1390ba0ddff681ea43e9b3e30ecbdb65c5e9aab49435e44059c23dbd6fee8ee619419ba37465bb0dd7135da
  languageName: node
  linkType: hard

"jss-plugin-camel-case@npm:10.10.0, jss-plugin-camel-case@npm:^10.5.1":
  version: 10.10.0
  resolution: "jss-plugin-camel-case@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    hyphenate-style-name: "npm:^1.0.3"
    jss: "npm:10.10.0"
  checksum: b4d68391316d41200881d3923ba771740be091b2feca09440470e5a5dc0260ce9adff8996b11e1f1c860eb20eed2fc89ae0b5813478b40c9d46a4e9142ffed81
  languageName: node
  linkType: hard

"jss-plugin-compose@npm:10.10.0":
  version: 10.10.0
  resolution: "jss-plugin-compose@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    jss: "npm:10.10.0"
    tiny-warning: "npm:^1.0.2"
  checksum: ee89b6905147edc4758e6eb2670761eccb072a32a4d1874d8cfbe18af858f460035e0fbc04363f8b3f9676a7aa4b2f4fef5608e6865233ac62bf9df887f151c2
  languageName: node
  linkType: hard

"jss-plugin-default-unit@npm:10.10.0, jss-plugin-default-unit@npm:^10.5.1":
  version: 10.10.0
  resolution: "jss-plugin-default-unit@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    jss: "npm:10.10.0"
  checksum: 7784f8b3668a99e27480b7baa28ec974f721438c67fd11f2a12851f1b0965d7a5116b473f5d34114276b14548fcea673c41467f62e136a3c4018be0bc1582a9c
  languageName: node
  linkType: hard

"jss-plugin-expand@npm:10.10.0":
  version: 10.10.0
  resolution: "jss-plugin-expand@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    jss: "npm:10.10.0"
  checksum: 7a12dedb0fb826c391d848f22824419c6db84ade5c21f6e7f91b12c299f716668318cd3b7aa4dfba612aee1a83f03f6f32a0b60f9758c4a2e1fa494aab4c5e0f
  languageName: node
  linkType: hard

"jss-plugin-extend@npm:10.10.0":
  version: 10.10.0
  resolution: "jss-plugin-extend@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    jss: "npm:10.10.0"
    tiny-warning: "npm:^1.0.2"
  checksum: d411da9b5ad6c3ae815cd54bbc7d1cb632b25b31b8495486e9fa3703d2a53b64e373265c7fddde473a74213c230bcdc49b887883752acad1cb43ef16246147cf
  languageName: node
  linkType: hard

"jss-plugin-global@npm:10.10.0, jss-plugin-global@npm:^10.5.1":
  version: 10.10.0
  resolution: "jss-plugin-global@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    jss: "npm:10.10.0"
  checksum: c0c2653ea340cd4bc2eb38a7d427bab001e6dd0dde1cf934c70ce624bd3e928f63a1917bcea59da714267dee6e424255b9079f6ca4c5e8aa4ccd726f2a53f9ba
  languageName: node
  linkType: hard

"jss-plugin-nested@npm:10.10.0, jss-plugin-nested@npm:^10.5.1":
  version: 10.10.0
  resolution: "jss-plugin-nested@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    jss: "npm:10.10.0"
    tiny-warning: "npm:^1.0.2"
  checksum: 834b795330edf1c4bbddfad68703bc436c7678bad3cebe724395c07abc4d0bd9b6f665d4c90819cab122531363024c8f53d61ae210ae1a26de1222da01f200b3
  languageName: node
  linkType: hard

"jss-plugin-props-sort@npm:10.10.0, jss-plugin-props-sort@npm:^10.5.1":
  version: 10.10.0
  resolution: "jss-plugin-props-sort@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    jss: "npm:10.10.0"
  checksum: 89eebe2bc4f58e763c3e3e9f96a54df87cc762a3c027b638459100dc880bfd3461092b1893de0946f6938e21b9fd3ffa3357e94ec6dd7c52ea3eefe3a9fbcb65
  languageName: node
  linkType: hard

"jss-plugin-rule-value-function@npm:10.10.0, jss-plugin-rule-value-function@npm:^10.5.1":
  version: 10.10.0
  resolution: "jss-plugin-rule-value-function@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    jss: "npm:10.10.0"
    tiny-warning: "npm:^1.0.2"
  checksum: 71cc09090fb1bb2170c8db79aa8844bc7e348f97d0ac6b7937e668d7725c7088da0758bd701997c829e22a5ef291ddb9e5bd19f27ead7f86ecd85601f854bdd6
  languageName: node
  linkType: hard

"jss-plugin-rule-value-observable@npm:10.10.0":
  version: 10.10.0
  resolution: "jss-plugin-rule-value-observable@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    jss: "npm:10.10.0"
    symbol-observable: "npm:^1.2.0"
  checksum: c9f99a87d16f1d4fbc39dec29f4e9525c2c4f795a4c3fbd71c305b140042864c9f622d812ad733808b584dca3bbb6257438350911440b0f158a492eb279b6f5a
  languageName: node
  linkType: hard

"jss-plugin-template@npm:10.10.0":
  version: 10.10.0
  resolution: "jss-plugin-template@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    jss: "npm:10.10.0"
    tiny-warning: "npm:^1.0.2"
  checksum: 5c94406644b6c2fc342f0c719171c439d313133507dc1c291e09a02eb214802227c032e5c52ead66c29c2ff98cf0d11b9533ab78c91464680bce59668bf23981
  languageName: node
  linkType: hard

"jss-plugin-vendor-prefixer@npm:10.10.0, jss-plugin-vendor-prefixer@npm:^10.5.1":
  version: 10.10.0
  resolution: "jss-plugin-vendor-prefixer@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    css-vendor: "npm:^2.0.8"
    jss: "npm:10.10.0"
  checksum: c497a32b756652853319cf3f5509688512b01010164145b2748a3f6748291eec9bd414970ef1371a8a41991083f966f0d30f46be6e1283e12c2bedc3923b1755
  languageName: node
  linkType: hard

"jss-preset-default@npm:10.10.0, jss-preset-default@npm:^10.10.0":
  version: 10.10.0
  resolution: "jss-preset-default@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    jss: "npm:10.10.0"
    jss-plugin-camel-case: "npm:10.10.0"
    jss-plugin-compose: "npm:10.10.0"
    jss-plugin-default-unit: "npm:10.10.0"
    jss-plugin-expand: "npm:10.10.0"
    jss-plugin-extend: "npm:10.10.0"
    jss-plugin-global: "npm:10.10.0"
    jss-plugin-nested: "npm:10.10.0"
    jss-plugin-props-sort: "npm:10.10.0"
    jss-plugin-rule-value-function: "npm:10.10.0"
    jss-plugin-rule-value-observable: "npm:10.10.0"
    jss-plugin-template: "npm:10.10.0"
    jss-plugin-vendor-prefixer: "npm:10.10.0"
  checksum: c061b43e7444bbc572c7eb26b2231893b26b1ff09e83c9da17db78b2445bab6f63d48842273cba65a7049441cfcef4eefbf14c2f14b3d46be7151559b5c3a7dc
  languageName: node
  linkType: hard

"jss@npm:10.10.0, jss@npm:^10.10.0, jss@npm:^10.5.1":
  version: 10.10.0
  resolution: "jss@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    csstype: "npm:^3.0.2"
    is-in-browser: "npm:^1.1.3"
    tiny-warning: "npm:^1.0.2"
  checksum: a08605d8e023cf0e67a53d00c03c4e52419e314020fcd0b27728986c3bf5c148d85447797980ab2d9a36288d907909fc6939f91fef5341a51976bc5ff11d147b
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:^2.4.1 || ^3.0.0, jsx-ast-utils@npm:^3.3.5":
  version: 3.3.5
  resolution: "jsx-ast-utils@npm:3.3.5"
  dependencies:
    array-includes: "npm:^3.1.6"
    array.prototype.flat: "npm:^1.3.1"
    object.assign: "npm:^4.1.4"
    object.values: "npm:^1.1.6"
  checksum: b61d44613687dfe4cc8ad4b4fbf3711bf26c60b8d5ed1f494d723e0808415c59b24a7c0ed8ab10736a40ff84eef38cbbfb68b395e05d31117b44ffc59d31edfc
  languageName: node
  linkType: hard

"jszip@npm:^3.10.1":
  version: 3.10.1
  resolution: "jszip@npm:3.10.1"
  dependencies:
    lie: "npm:~3.3.0"
    pako: "npm:~1.0.2"
    readable-stream: "npm:~2.3.6"
    setimmediate: "npm:^1.0.5"
  checksum: bfbfbb9b0a27121330ac46ab9cdb3b4812433faa9ba4a54742c87ca441e31a6194ff70ae12acefa5fe25406c432290e68003900541d948a169b23d30c34dd984
  languageName: node
  linkType: hard

"keyv@npm:^4.5.3":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 167eb6ef64cc84b6fa0780ee50c9de456b422a1e18802209234f7c2cf7eae648c7741f32e50d7e24ccb22b24c13154070b01563d642755b156c357431a191e75
  languageName: node
  linkType: hard

"language-subtag-registry@npm:^0.3.20":
  version: 0.3.22
  resolution: "language-subtag-registry@npm:0.3.22"
  checksum: 5591f4abd775d1ab5945355a5ba894327d2d94c900607bdb69aac1bc5bb921dbeeeb5f616df95e8c0ae875501d19c1cfa0e852ece822121e95048deb34f2b4d2
  languageName: node
  linkType: hard

"language-tags@npm:^1.0.9":
  version: 1.0.9
  resolution: "language-tags@npm:1.0.9"
  dependencies:
    language-subtag-registry: "npm:^0.3.20"
  checksum: d3a7c14b694e67f519153d6df6cb200681648d38d623c3bfa9d6a66a5ec5493628acb88e9df5aceef3cf1902ab263a205e7d59ee4cf1d6bb67e707b83538bd6d
  languageName: node
  linkType: hard

"lazystream@npm:^1.0.0":
  version: 1.0.1
  resolution: "lazystream@npm:1.0.1"
  dependencies:
    readable-stream: "npm:^2.0.5"
  checksum: 35f8cf8b5799c76570b211b079d4d706a20cbf13a4936d44cc7dbdacab1de6b346ab339ed3e3805f4693155ee5bbebbda4050fa2b666d61956e89a573089e3d4
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 2e4720ff79f21ae08d42374b0a5c2f664c5be8b6c8f565bb4e1315c96ed3a8acaa9de788ffed82d7f2378cf36958573de07ef92336cb5255ed74d08b8318c9ee
  languageName: node
  linkType: hard

"lie@npm:~3.3.0":
  version: 3.3.0
  resolution: "lie@npm:3.3.0"
  dependencies:
    immediate: "npm:~3.0.5"
  checksum: f335ce67fe221af496185d7ce39c8321304adb701e122942c495f4f72dcee8803f9315ee572f5f8e8b08b9e8d7195da91b9fad776e8864746ba8b5e910adf76e
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"listenercount@npm:~1.0.1":
  version: 1.0.1
  resolution: "listenercount@npm:1.0.1"
  checksum: 208c6d2b57dc16c22cc71b58a7debb6f4612a79de211b76e251efee8eb03b9f6acd4651399016ef9c15ff6a3dedfd7acc96064acddce0dbe627e2d8478034d3d
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash-es@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash-es@npm:4.17.21"
  checksum: 03f39878ea1e42b3199bd3f478150ab723f93cc8730ad86fec1f2804f4a07c6e30deaac73cad53a88e9c3db33348bb8ceeb274552390e7a75d7849021c02df43
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: cd0b2819786e6e80cb9f5cda26b1a8fc073daaf04e48d4cb462fa4663ec9adb3a5387aa22d7129e48eed1afa05b482e2a6b79bfc99b86886364449500cbb00fd
  languageName: node
  linkType: hard

"lodash.defaults@npm:^4.2.0":
  version: 4.2.0
  resolution: "lodash.defaults@npm:4.2.0"
  checksum: 6a2a9ea5ad7585aff8d76836c9e1db4528e5f5fa50fc4ad81183152ba8717d83aef8aec4fa88bf3417ed946fd4b4358f145ee08fbc77fb82736788714d3e12db
  languageName: node
  linkType: hard

"lodash.difference@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.difference@npm:4.5.0"
  checksum: b22adb1be9c60e5997b8b483f8bab19878cb40eda65437907958e5d27990214716e1b00ebe312a97f47e63d8b891e4ae30947d08e1f0861ccdb9462f56ab9d77
  languageName: node
  linkType: hard

"lodash.escaperegexp@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.escaperegexp@npm:4.1.2"
  checksum: 6d99452b1cfd6073175a9b741a9b09ece159eac463f86f02ea3bee2e2092923fce812c8d2bf446309cc52d1d61bf9af51c8118b0d7421388e6cead7bd3798f0f
  languageName: node
  linkType: hard

"lodash.flatten@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.flatten@npm:4.4.0"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"lodash.groupby@npm:^4.6.0":
  version: 4.6.0
  resolution: "lodash.groupby@npm:4.6.0"
  checksum: 98bd04e58ce4cebb2273010352508b5ea12025e94fcfd70c84c8082ef3b0689178e8e6dd53bff919f525fae9bd67b4aba228d606b75a967f30e84ec9610b5de1
  languageName: node
  linkType: hard

"lodash.isboolean@npm:^3.0.3":
  version: 3.0.3
  resolution: "lodash.isboolean@npm:3.0.3"
  checksum: b70068b4a8b8837912b54052557b21fc4774174e3512ed3c5b94621e5aff5eb6c68089d0a386b7e801d679cd105d2e35417978a5e99071750aa2ed90bffd0250
  languageName: node
  linkType: hard

"lodash.isequal@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.isequal@npm:4.5.0"
  checksum: 82fc58a83a1555f8df34ca9a2cd300995ff94018ac12cc47c349655f0ae1d4d92ba346db4c19bbfc90510764e0c00ddcc985a358bdcd4b3b965abf8f2a48a214
  languageName: node
  linkType: hard

"lodash.isfunction@npm:^3.0.9":
  version: 3.0.9
  resolution: "lodash.isfunction@npm:3.0.9"
  checksum: 99e54c34b1e8a9ba75c034deb39cedbd2aca7af685815e67a2a8ec4f73ec9748cda6ebee5a07d7de4b938e90d421fd280e9c385cc190f903ac217ac8aff30314
  languageName: node
  linkType: hard

"lodash.isnil@npm:^4.0.0":
  version: 4.0.0
  resolution: "lodash.isnil@npm:4.0.0"
  checksum: ebf8df69879badd6ad99c4f64c54c470248df5cf92b208ca730861b1d8ac058da7b632ac811d18b0929d93cbac8d8fc866e781ee816b0142c56952e85edc682f
  languageName: node
  linkType: hard

"lodash.isplainobject@npm:^4.0.6":
  version: 4.0.6
  resolution: "lodash.isplainobject@npm:4.0.6"
  checksum: 29c6351f281e0d9a1d58f1a4c8f4400924b4c79f18dfc4613624d7d54784df07efaff97c1ff2659f3e085ecf4fff493300adc4837553104cef2634110b0d5337
  languageName: node
  linkType: hard

"lodash.isundefined@npm:^3.0.1":
  version: 3.0.1
  resolution: "lodash.isundefined@npm:3.0.1"
  checksum: 52b4d99a47bd41daa4e2860200258f56b1f2c99263c11a5f607fbbd91d6447fe674bdafc172735d099908a09136d4a0f98cf79715e38ca4b490fdda7162be289
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: d0ea2dd0097e6201be083865d50c3fb54fbfbdb247d9cc5950e086c991f448b7ab0cdab0d57eacccb43473d3f2acd21e134db39f22dac2d6c9ba6bf26978e3d6
  languageName: node
  linkType: hard

"lodash.throttle@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.throttle@npm:4.1.1"
  checksum: 9be9fb2ffd686c20543167883305542f4564062a5f712a40e8c6f2f0d9fd8254a6e9d801c2470b1b24e0cdf2ae83c1277b55aa0fb4799a2db6daf545f53820e1
  languageName: node
  linkType: hard

"lodash.union@npm:^4.6.0":
  version: 4.6.0
  resolution: "lodash.union@npm:4.6.0"
  checksum: 175f5786efc527238c1350ce561c28e5ba527b5957605f9e5b8a804fce78801d09ced7b72de0302325e5b14c711f94690b1a733c13ad3674cc1a76e1172db1f8
  languageName: node
  linkType: hard

"lodash.uniq@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.uniq@npm:4.5.0"
  checksum: 86246ca64ac0755c612e5df6d93cfe92f9ecac2e5ff054b965efbbb1d9a647b6310969e78545006f70f52760554b03233ad0103324121ae31474c20d5f7a2812
  languageName: node
  linkType: hard

"lodash@npm:^4.17.21, lodash@npm:^4.17.4":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: c08619c038846ea6ac754abd6dd29d2568aa705feb69339e836dfa8d8b09abbb2f859371e86863eda41848221f9af43714491467b5b0299122431e202bb0c532
  languageName: node
  linkType: hard

"loose-envify@npm:^1.1.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 83a0a5f159ad7614bee8bf976b96275f3954335a84fad2696927f609ddae902802c4f3312d86668722e668bef41400254807e1d3a7f2e8c3eede79691aa1f010
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^9.1.1 || ^10.0.0":
  version: 10.1.0
  resolution: "lru-cache@npm:10.1.0"
  checksum: 207278d6fa711fb1f94a0835d4d4737441d2475302482a14785b10515e4c906a57ebf9f35bf060740c9560e91c7c1ad5a04fd7ed030972a9ba18bce2a228e95b
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 951d2673dcc64a7fb888bf3d13bc2fdf923faca97d89cdb405ba3dfff77e2b26e5798d405e78fcd7094c9e7b8b4dab2ddc5a4f8a11928af24a207b7c738ca3f8
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: fc1fe2ee205f7c8855fa0f34c1ab0bcf14b6229e35579ec1fd1079f31d6fc8ef8eb6fd17f2f4d99788d7e339f50e047555551ebd5e434dda503696e7c6591825
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^13.0.0":
  version: 13.0.0
  resolution: "make-fetch-happen@npm:13.0.0"
  dependencies:
    "@npmcli/agent": "npm:^2.0.0"
    cacache: "npm:^18.0.0"
    http-cache-semantics: "npm:^4.1.1"
    is-lambda: "npm:^1.0.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^3.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^0.6.3"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^10.0.0"
  checksum: ded5a91a02b76381b06a4ec4d5c1d23ebbde15d402b3c3e4533b371dac7e2f7ca071ae71ae6dae72aa261182557b7b1b3fd3a705b39252dc17f74fa509d3e76f
  languageName: node
  linkType: hard

"match-sorter@npm:^6.0.2":
  version: 6.3.1
  resolution: "match-sorter@npm:6.3.1"
  dependencies:
    "@babel/runtime": "npm:^7.12.5"
    remove-accents: "npm:0.4.2"
  checksum: 917dd07c2562b345919156ef601dfbeaa44571e111e2956f876be8de6800325be97f878677199c50298672fbd3cfd17b2c4918d3fba15f0e9a5b32f328780fdc
  languageName: node
  linkType: hard

"material-react-table@npm:^2.0.4":
  version: 2.0.5
  resolution: "material-react-table@npm:2.0.5"
  dependencies:
    "@tanstack/match-sorter-utils": "npm:8.8.4"
    "@tanstack/react-table": "npm:8.10.7"
    "@tanstack/react-virtual": "npm:3.0.1"
    highlight-words: "npm:1.2.2"
  peerDependencies:
    "@emotion/react": ">=11.11"
    "@emotion/styled": ">=11.11"
    "@mui/icons-material": ">=5.11"
    "@mui/material": ">=5.13"
    "@mui/x-date-pickers": ">=6.15.0"
    react: ">=18.0"
    react-dom: ">=18.0"
  checksum: 88a6ccafc55a0e4c71ddfb2e67e50cdbd57dc5a74d487c570003ee1a61682986a702fa429e65315aa66a6fe7aa049a2208218df8a5e92b3913c1936248b9489b
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.28":
  version: 2.0.28
  resolution: "mdn-data@npm:2.0.28"
  checksum: aec475e0c078af00498ce2f9434d96a1fdebba9814d14b8f72cd6d5475293f4b3972d0538af2d5c5053d35e1b964af08b7d162b98e9846e9343990b75e4baef1
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.30":
  version: 2.0.30
  resolution: "mdn-data@npm:2.0.30"
  checksum: e4944322bf3e0461a2daa2aee7e14e208960a036289531e4ef009e53d32bd41528350c070c4a33be867980443fe4c0523518d99318423cffa7c825fe7b1154e2
  languageName: node
  linkType: hard

"memoize-one@npm:>=3.1.1 <6":
  version: 5.2.1
  resolution: "memoize-one@npm:5.2.1"
  checksum: b7141dc148b5c6fdd51e77ecf0421fd2581681eb8756e0b3dfbd4fe765b5e2b5a6bc90214bb6f19a96b6aed44de17eda3407142a7be9e24ccd0774bbd9874d1b
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4":
  version: 4.0.5
  resolution: "micromatch@npm:4.0.5"
  dependencies:
    braces: "npm:^3.0.2"
    picomatch: "npm:^2.3.1"
  checksum: a749888789fc15cac0e03273844dbd749f9f8e8d64e70c564bcf06a033129554c789bb9e30d7566d7ff6596611a08e58ac12cf2a05f6e3c9c47c50c4c7e12fa2
  languageName: node
  linkType: hard

"microseconds@npm:0.2.0":
  version: 0.2.0
  resolution: "microseconds@npm:0.2.0"
  checksum: 22bfa8553f92c7d95afff6de0aeb2aecf750680d41b8c72b02098ccc5bbbb0a384380ff539292dbd3788f5dfc298682f9d38a2b4c101f5ee2c9471d53934c5fa
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 54bb60bf39e6f8689f6622784e668a3d7f8bed6b0d886f5c3c446cb3284be28b30bf707ed05d0fe44a036f8469976b2629bbea182684977b084de9da274694d7
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 89aa9651b67644035de2784a6e665fc685d79aba61857e02b9c8758da874a754aed4a9aced9265f5ed1171fd934331e5516b84a7f0218031b6fa0270eca1e51a
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mimic-fn@npm:^4.0.0":
  version: 4.0.0
  resolution: "mimic-fn@npm:4.0.0"
  checksum: 995dcece15ee29aa16e188de6633d43a3db4611bcf93620e7e62109ec41c79c0f34277165b8ce5e361205049766e371851264c21ac64ca35499acb5421c2ba56
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.5, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: e0b25b04cd4ec6732830344e5739b13f8690f8a012d73445a4a19fbc623f5dd481ef7a5827fde25954cd6026fede7574cc54dc4643c99d6c6b653d6203f94634
  languageName: node
  linkType: hard

"minimatch@npm:^5.1.0":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 126b36485b821daf96d33b5c821dac600cc1ab36c87e7a532594f9b1652b1fa89a1eebcaad4dff17c764dce1a7ac1531327f190fed5f97d8f6e5f889c116c429
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.1, minimatch@npm:^9.0.3":
  version: 9.0.3
  resolution: "minimatch@npm:9.0.3"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: c81b47d28153e77521877649f4bab48348d10938df9e8147a58111fe00ef89559a2938de9f6632910c4f7bf7bb5cd81191a546167e58d357f0cfb1e18cecc1c5
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 908491b6cc15a6c440ba5b22780a0ba89b9810e1aea684e253e43c4e3b8d56ec1dcdd7ea96dde119c29df59c936cde16062159eae4225c691e19c70b432b6e6f
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^3.0.0":
  version: 3.0.4
  resolution: "minipass-fetch@npm:3.0.4"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^2.1.2"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 3edf72b900e30598567eafe96c30374432a8709e61bb06b87198fa3192d466777e2ec21c52985a0999044fa6567bd6f04651585983a1cbb27e2c1770a07ed2a2
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 40982d8d836a52b0f37049a0a7e5d0f089637298e6d9b45df9c115d4f0520682a78258905e5c8b180fb41b593b0a82cc1361d2c74b45f7ada66334f84d1ecfdd
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: a5c6ef069f70d9a524d3428af39f2b117ff8cd84172e19b754e7264a33df460873e6eb3d6e55758531580970de50ae950c496256bb4ad3691a2974cddff189f0
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 61682162d29f45d3152b78b08bab7fb32ca10899bc5991ffe98afc18c9e9543bd1e3be94f8b8373ba6262497db63607079dc242ea62e43e7b2270837b7347c93
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3":
  version: 7.0.4
  resolution: "minipass@npm:7.0.4"
  checksum: e864bd02ceb5e0707696d58f7ce3a0b89233f0d686ef0d447a66db705c0846a8dc6f34865cd85256c1472ff623665f616b90b8ff58058b2ad996c5de747d2d18
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1, minizlib@npm:^2.1.2":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: "npm:^3.0.0"
    yallist: "npm:^4.0.0"
  checksum: ae0f45436fb51344dcb87938446a32fbebb540d0e191d63b35e1c773d47512e17307bf54aa88326cc6d176594d00e4423563a091f7266c2f9a6872cdc1e234d1
  languageName: node
  linkType: hard

"mkdirp@npm:>=0.5 0":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: "npm:^1.2.6"
  bin:
    mkdirp: bin/cmd.js
  checksum: 0c91b721bb12c3f9af4b77ebf73604baf350e64d80df91754dc509491ae93bf238581e59c7188360cec7cb62fc4100959245a42cfe01834efedc5e9d068376c2
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: d71b8dcd4b5af2fe13ecf3bd24070263489404fe216488c5ba7e38ece1f54daf219e72a833a3a2dc404331e870e9f44963a33399589490956bff003a3404d3b2
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 673cdb2c3133eb050c745908d8ce632ed2c02d85640e2edb3ace856a2266a813b30c613569bf3354fdf4ea7d1a1494add3bfa95e2713baa27d0c2c71fc44f58f
  languageName: node
  linkType: hard

"ms@npm:^2.1.1":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"nano-time@npm:1.0.0":
  version: 1.0.0
  resolution: "nano-time@npm:1.0.0"
  dependencies:
    big-integer: "npm:^1.6.16"
  checksum: eef8548546cc1020625f8e44751a7263e9eddf0412a6a1a6c80a8d2be2ea7973622804a977cdfe796807b85b20ff6c8ba340e8dd20effcc7078193ed5edbb5d4
  languageName: node
  linkType: hard

"nanoid@npm:^2.1.0":
  version: 2.1.11
  resolution: "nanoid@npm:2.1.11"
  checksum: cf2a2eedcf9d8893a4687f11743ccf8381f047bc2b3d3887a23721bbef8fe64c5759b9cba6eb945e40efeb4a7e7379b3417e4dc5f6cc03050322d2c24a7ff69b
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.6":
  version: 3.3.7
  resolution: "nanoid@npm:3.3.7"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: ac1eb60f615b272bccb0e2b9cd933720dad30bf9708424f691b8113826bb91aca7e9d14ef5d9415a6ba15c266b37817256f58d8ce980c82b0ba3185352565679
  languageName: node
  linkType: hard

"natural-compare-lite@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare-lite@npm:1.4.0"
  checksum: 5222ac3986a2b78dd6069ac62cbb52a7bf8ffc90d972ab76dfe7b01892485d229530ed20d0c62e79a6b363a663b273db3bde195a1358ce9e5f779d4453887225
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:^0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 2723fb822a17ad55c93a588a4bc44d53b22855bf4be5499916ca0cab1e7165409d0b288ba2577d7b029f10ce18cf2ed8e703e5af31c984e1e2304277ef979837
  languageName: node
  linkType: hard

"next@npm:^14.0.2":
  version: 14.0.4
  resolution: "next@npm:14.0.4"
  dependencies:
    "@next/env": "npm:14.0.4"
    "@next/swc-darwin-arm64": "npm:14.0.4"
    "@next/swc-darwin-x64": "npm:14.0.4"
    "@next/swc-linux-arm64-gnu": "npm:14.0.4"
    "@next/swc-linux-arm64-musl": "npm:14.0.4"
    "@next/swc-linux-x64-gnu": "npm:14.0.4"
    "@next/swc-linux-x64-musl": "npm:14.0.4"
    "@next/swc-win32-arm64-msvc": "npm:14.0.4"
    "@next/swc-win32-ia32-msvc": "npm:14.0.4"
    "@next/swc-win32-x64-msvc": "npm:14.0.4"
    "@swc/helpers": "npm:0.5.2"
    busboy: "npm:1.6.0"
    caniuse-lite: "npm:^1.0.30001406"
    graceful-fs: "npm:^4.2.11"
    postcss: "npm:8.4.31"
    styled-jsx: "npm:5.1.1"
    watchpack: "npm:2.4.0"
  peerDependencies:
    "@opentelemetry/api": ^1.1.0
    react: ^18.2.0
    react-dom: ^18.2.0
    sass: ^1.3.0
  dependenciesMeta:
    "@next/swc-darwin-arm64":
      optional: true
    "@next/swc-darwin-x64":
      optional: true
    "@next/swc-linux-arm64-gnu":
      optional: true
    "@next/swc-linux-arm64-musl":
      optional: true
    "@next/swc-linux-x64-gnu":
      optional: true
    "@next/swc-linux-x64-musl":
      optional: true
    "@next/swc-win32-arm64-msvc":
      optional: true
    "@next/swc-win32-ia32-msvc":
      optional: true
    "@next/swc-win32-x64-msvc":
      optional: true
  peerDependenciesMeta:
    "@opentelemetry/api":
      optional: true
    sass:
      optional: true
  bin:
    next: dist/bin/next
  checksum: f119dfed59ba14972759bbc354fd2e99793c5a31689465a5e7cacd329977ed3d259eb756142bef31e96f28a80a00997e76314425faeda4c6fcf4e4ad6c5fa960
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: "npm:^2.0.2"
    tslib: "npm:^2.0.3"
  checksum: 0b2ebc113dfcf737d48dde49cfebf3ad2d82a8c3188e7100c6f375e30eafbef9e9124aadc3becef237b042fd5eb0aad2fd78669c20972d045bbe7fea8ba0be5c
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 10.0.1
  resolution: "node-gyp@npm:10.0.1"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^13.0.0"
    nopt: "npm:^7.0.0"
    proc-log: "npm:^3.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^6.1.2"
    which: "npm:^4.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 578cf0c821f258ce4b6ebce4461eca4c991a4df2dee163c0624f2fe09c7d6d37240be4942285a0048d307230248ee0b18382d6623b9a0136ce9533486deddfa8
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.14":
  version: 2.0.14
  resolution: "node-releases@npm:2.0.14"
  checksum: 0f7607ec7db5ef1dc616899a5f24ae90c869b6a54c2d4f36ff6d84a282ab9343c7ff3ca3670fe4669171bb1e8a9b3e286e1ef1c131f09a83d70554f855d54f24
  languageName: node
  linkType: hard

"nopt@npm:^7.0.0":
  version: 7.2.0
  resolution: "nopt@npm:7.2.0"
  dependencies:
    abbrev: "npm:^2.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 1e7489f17cbda452c8acaf596a8defb4ae477d2a9953b76eb96f4ec3f62c6b421cd5174eaa742f88279871fde9586d8a1d38fb3f53fa0c405585453be31dff4c
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"notistack@npm:^3.0.1":
  version: 3.0.1
  resolution: "notistack@npm:3.0.1"
  dependencies:
    clsx: "npm:^1.1.0"
    goober: "npm:^2.0.33"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: be6ca9e1c2f06118fe0545eaaf91f2f260efe5b1ff3b3f6575a346d5c9f88666e7f13ef4369a56037a2c89c030c191f034f6e6b5a28be704540a23442e2d19a9
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 5374c0cea4b0bbfdfae62da7bbdf1e1558d338335f4cacf2515c282ff358ff27b2ecb91ffa5330a8b14390ac66a1e146e10700440c1ab868208430f56b5f4d23
  languageName: node
  linkType: hard

"npm-run-path@npm:^5.1.0":
  version: 5.1.0
  resolution: "npm-run-path@npm:5.1.0"
  dependencies:
    path-key: "npm:^4.0.0"
  checksum: dc184eb5ec239d6a2b990b43236845332ef12f4e0beaa9701de724aa797fe40b6bbd0157fb7639d24d3ab13f5d5cf22d223a19c6300846b8126f335f788bee66
  languageName: node
  linkType: hard

"nprogress@npm:^0.2.0":
  version: 0.2.0
  resolution: "nprogress@npm:0.2.0"
  checksum: 1870a74c054c01899f89e85122d7548832d083b5fa5d3cc6aafc2d4f92901e15face402ef557be5b103aed7b6e1406c656b842dec32b553b4b052031ea1b0935
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"numeral@npm:^2.0.6":
  version: 2.0.6
  resolution: "numeral@npm:2.0.6"
  checksum: 5d19829b5b8d6b503d3c0144fcbaf0f515f8666c87e96e9484a3742d0f6e13a14f763cad313dfaa0d968b3e1a65fc1d8162f4f0c715c3d2e1859ed27ed0834c2
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.1, object-inspect@npm:^1.9.0":
  version: 1.13.1
  resolution: "object-inspect@npm:1.13.1"
  checksum: 92f4989ed83422d56431bc39656d4c780348eb15d397ce352ade6b7fec08f973b53744bd41b94af021901e61acaf78fcc19e65bf464ecc0df958586a672700f0
  languageName: node
  linkType: hard

"object-is@npm:^1.1.5":
  version: 1.1.5
  resolution: "object-is@npm:1.1.5"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.1.3"
  checksum: 75365aff5da4bebad5d20efd9f9a7a13597e603f5eb03d89da8f578c3f3937fe01c6cb5fce86c0611c48795c0841401fd37c943821db0de703c7b30a290576ad
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 3d81d02674115973df0b7117628ea4110d56042e5326413e4b4313f0bcdf7dd78d4a3acef2c831463fa3796a66762c49daef306f4a0ea1af44877d7086d73bde
  languageName: node
  linkType: hard

"object-path@npm:^0.11.5":
  version: 0.11.8
  resolution: "object-path@npm:0.11.8"
  checksum: cbc41515ff97aa7515bd93a3d93d5b7307c95413345d83c66c60b7618429cfc935ff4049192c96701eeeb33a78678b15ee97b5fe0857e9eca4fcd7507dfafd36
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.2, object.assign@npm:^4.1.4":
  version: 4.1.5
  resolution: "object.assign@npm:4.1.5"
  dependencies:
    call-bind: "npm:^1.0.5"
    define-properties: "npm:^1.2.1"
    has-symbols: "npm:^1.0.3"
    object-keys: "npm:^1.1.1"
  checksum: dbb22da4cda82e1658349ea62b80815f587b47131b3dd7a4ab7f84190ab31d206bbd8fe7e26ae3220c55b65725ac4529825f6142154211220302aa6b1518045d
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.5, object.entries@npm:^1.1.6, object.entries@npm:^1.1.7":
  version: 1.1.7
  resolution: "object.entries@npm:1.1.7"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
  checksum: 03f0bd0f23a8626c94429d15abf26ccda7723f08cd26be2c09c72d436765f8c7468605b5476ca58d4a7cec1ec7eca5be496dbd938fd4236b77ed6d05a8680048
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.6, object.fromentries@npm:^2.0.7":
  version: 2.0.7
  resolution: "object.fromentries@npm:2.0.7"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
  checksum: 1bfbe42a51f8d84e417d193fae78e4b8eebb134514cdd44406480f8e8a0e075071e0717635d8e3eccd50fec08c1d555fe505c38804cbac0808397187653edd59
  languageName: node
  linkType: hard

"object.groupby@npm:^1.0.1":
  version: 1.0.1
  resolution: "object.groupby@npm:1.0.1"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
    get-intrinsic: "npm:^1.2.1"
  checksum: b7123d91403f95d63978513b23a6079c30f503311f64035fafc863c291c787f287b58df3b21ef002ce1d0b820958c9009dd5a8ab696e0eca325639d345e41524
  languageName: node
  linkType: hard

"object.hasown@npm:^1.1.2":
  version: 1.1.3
  resolution: "object.hasown@npm:1.1.3"
  dependencies:
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
  checksum: 735679729c25a4e0d3713adf5df9861d862f0453e87ada4d991b75cd4225365dec61a08435e1127f42c9cc1adfc8e952fa5dca75364ebda6539dadf4721dc9c4
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6, object.values@npm:^1.1.7":
  version: 1.1.7
  resolution: "object.values@npm:1.1.7"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
  checksum: 20ab42c0bbf984405c80e060114b18cf5d629a40a132c7eac4fb79c5d06deb97496311c19297dcf9c61f45c2539cd4c7f7c5d6230e51db360ff297bbc9910162
  languageName: node
  linkType: hard

"oblivious-set@npm:1.0.0":
  version: 1.0.0
  resolution: "oblivious-set@npm:1.0.0"
  checksum: f31740ea9c3a8242ad2324e4ebb9a35359fbc2e6e7131731a0fc1c8b7b1238eb07e4c8c631a38535243a7b8e3042b7e89f7dc2a95d2989afd6f80bd5793b0aab
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: e9fd0695a01cf226652f0385bf16b7a24153dbbb2039f764c8ba6d2306a8506b0e4ce570de6ad99c7a6eb49520743afdb66edd95ee979c1a342554ed49a9aadd
  languageName: node
  linkType: hard

"onetime@npm:^6.0.0":
  version: 6.0.0
  resolution: "onetime@npm:6.0.0"
  dependencies:
    mimic-fn: "npm:^4.0.0"
  checksum: 0846ce78e440841335d4e9182ef69d5762e9f38aa7499b19f42ea1c4cd40f0b4446094c455c713f9adac3f4ae86f613bb5e30c99e52652764d06a89f709b3788
  languageName: node
  linkType: hard

"open@npm:^9.1.0":
  version: 9.1.0
  resolution: "open@npm:9.1.0"
  dependencies:
    default-browser: "npm:^4.0.0"
    define-lazy-prop: "npm:^3.0.0"
    is-inside-container: "npm:^1.0.0"
    is-wsl: "npm:^2.2.0"
  checksum: b45bcc7a6795804a2f560f0ca9f5e5344114bc40754d10c28a811c0c8f7027356979192931a6a7df2ab9e5bab3058988c99ae55f4fb71db2ce9fc77c40f619aa
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.3
  resolution: "optionator@npm:0.9.3"
  dependencies:
    "@aashutoshrathi/word-wrap": "npm:^1.2.3"
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
  checksum: fa28d3016395974f7fc087d6bbf0ac7f58ac3489f4f202a377e9c194969f329a7b88c75f8152b33fb08794a30dcd5c079db6bb465c28151357f113d80bbf67da
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: "npm:^3.0.0"
  checksum: 7ba4a2b1e24c05e1fc14bbaea0fc6d85cf005ae7e9c9425d4575550f37e2e584b1af97bcde78eacd7559208f20995988d52881334db16cf77bc1bcf68e48ed7c
  languageName: node
  linkType: hard

"pako@npm:~1.0.2":
  version: 1.0.11
  resolution: "pako@npm:1.0.11"
  checksum: 1ad07210e894472685564c4d39a08717e84c2a68a70d3c1d9e657d32394ef1670e22972a433cbfe48976cb98b154ba06855dcd3fcfba77f60f1777634bec48c0
  languageName: node
  linkType: hard

"parchment@npm:^1.1.2, parchment@npm:^1.1.4":
  version: 1.1.4
  resolution: "parchment@npm:1.1.4"
  checksum: fc655f2bc608fe9e818f664af5f8debc553f84be3b3fd1d5f59a91a21caeacfc1237c8a01cb0ce2aafff123740d67e3f33a12b29c804ac0a131fe97986ba52dd
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0, parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-key@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-key@npm:4.0.0"
  checksum: 8e6c314ae6d16b83e93032c61020129f6f4484590a777eed709c4a01b50e498822b00f76ceaf94bc64dbd90b327df56ceadce27da3d83393790f1219e07721d7
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.10.1":
  version: 1.10.1
  resolution: "path-scurry@npm:1.10.1"
  dependencies:
    lru-cache: "npm:^9.1.1 || ^10.0.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: eebfb8304fef1d4f7e1486df987e4fd77413de4fce16508dea69fcf8eb318c09a6b15a7a2f4c22877cec1cb7ecbd3071d18ca9de79eeece0df874a00f1f0bdc8
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.0.0
  resolution: "picocolors@npm:1.0.0"
  checksum: a2e8092dd86c8396bdba9f2b5481032848525b3dc295ce9b57896f931e63fc16f79805144321f72976383fc249584672a75cc18d6777c6b757603f372f745981
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 60c2595003b05e4535394d1da94850f5372c9427ca4413b71210f437f7b2ca091dbd611c45e8b37d10036fa8eade25c1b8951654f9d3973bfa66a2ff4d3b08bc
  languageName: node
  linkType: hard

"popper.js@npm:1.16.1-lts":
  version: 1.16.1-lts
  resolution: "popper.js@npm:1.16.1-lts"
  checksum: 932c453fce30bd00720413a89e87733b5074359834bd5b3fdc85c8a2e14d44558419ddaa0bf8e32d67899c7a57ab37ee1251588b7eb3a402e77c56edc8c405c8
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.2":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: e4e4486f33b3163a606a6ed94f9c196ab49a37a7a7163abfcd469e5f113210120d70b8dd5e33d64636f41ad52316a3725655421eb9a1094f1bcab1db2f555c62
  languageName: node
  linkType: hard

"postcss@npm:8.4.31":
  version: 8.4.31
  resolution: "postcss@npm:8.4.31"
  dependencies:
    nanoid: "npm:^3.3.6"
    picocolors: "npm:^1.0.0"
    source-map-js: "npm:^1.0.2"
  checksum: 1a6653e72105907377f9d4f2cd341d8d90e3fde823a5ddea1e2237aaa56933ea07853f0f2758c28892a1d70c53bbaca200eb8b80f8ed55f13093003dbec5afa0
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 0b9d2c76801ca652a7f64892dd37b7e3fab149a37d2424920099bf894acccc62abb4424af2155ab36dea8744843060a2d8ddc983518d0b1e22265a22324b72ed
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: "npm:^1.1.2"
  checksum: 00ce8011cf6430158d27f9c92cfea0a7699405633f7f1d4a45f07e21bf78e99895911cbcdc3853db3a824201a7c745bd49bfea8abd5fb9883e765a90f74f8392
  languageName: node
  linkType: hard

"prettier@npm:^3.0.3":
  version: 3.1.0
  resolution: "prettier@npm:3.1.0"
  bin:
    prettier: bin/prettier.cjs
  checksum: e95e8f93c6b9aea2ac1e86bebe329bee90c8c50d9a23d1f593eba8d7f39b33b3641eb28785001505b6723c47895a5322ad12a2fb855b289cb7bae450ffc34425
  languageName: node
  linkType: hard

"proc-log@npm:^3.0.0":
  version: 3.0.0
  resolution: "proc-log@npm:3.0.0"
  checksum: 02b64e1b3919e63df06f836b98d3af002b5cd92655cab18b5746e37374bfb73e03b84fe305454614b34c25b485cc687a9eebdccf0242cda8fda2475dd2c97e02
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 96e1a82453c6c96eef53a37a1d6134c9f2482f94068f98a59145d0986ca4e497bf110a410adf73857e588165eab3899f0ebcf7b3890c1b3ce802abc0d65967d4
  languageName: node
  linkType: hard

"prop-types@npm:^15.5.8, prop-types@npm:^15.6.0, prop-types@npm:^15.6.1, prop-types@npm:^15.6.2, prop-types@npm:^15.7.2, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: "npm:^1.4.0"
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.13.1"
  checksum: 7d959caec002bc964c86cdc461ec93108b27337dabe6192fb97d69e16a0c799a03462713868b40749bfc1caf5f57ef80ac3e4ffad3effa636ee667582a75e2c0
  languageName: node
  linkType: hard

"property-expr@npm:^2.0.5":
  version: 2.0.6
  resolution: "property-expr@npm:2.0.6"
  checksum: 89977f4bb230736c1876f460dd7ca9328034502fd92e738deb40516d16564b850c0bbc4e052c3df88b5b8cd58e51c93b46a94bea049a3f23f4a022c038864cab
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: f0bb4a87cfd18f77bc2fba23ae49c3b378fb35143af16cc478171c623eebe181678f09439707ad80081d340d1593cd54a33a0113f3ccb3f4bc9451488780ee23
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: febdc4362bead22f9e2608ff0171713230b57aff9dddc1c273aa2a651fbd366f94b7d6a71d78342a7c0819906750351ca7f2edd26ea41b626d87d6a13d1bd059
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 72900df0616e473e824202113c3df6abae59150dfb73ed13273503127235320e9c8ca4aaaaccfd58cf417c6ca92a6e68ee9a5c3182886ae949a768639b388a7b
  languageName: node
  linkType: hard

"quill-delta@npm:^3.6.2":
  version: 3.6.3
  resolution: "quill-delta@npm:3.6.3"
  dependencies:
    deep-equal: "npm:^1.0.1"
    extend: "npm:^3.0.2"
    fast-diff: "npm:1.1.2"
  checksum: 8aaa7bddab343f7ad613c57f50fda0406aed81f280130d7851a67e61e6614d8af29b9f341101480cf12477f81c73ee37d126dffc65fa9b3860ebfec855b03944
  languageName: node
  linkType: hard

"quill@npm:^1.3.7":
  version: 1.3.7
  resolution: "quill@npm:1.3.7"
  dependencies:
    clone: "npm:^2.1.1"
    deep-equal: "npm:^1.0.1"
    eventemitter3: "npm:^2.0.3"
    extend: "npm:^3.0.2"
    parchment: "npm:^1.1.4"
    quill-delta: "npm:^3.6.2"
  checksum: 17f5ac34d07a2b9a855867cc1d83b3d19732d7a7e5d14f7550b4e96eaaaab24ac055b306e59c3cc7ccff4b9c46d48b8f09d933f618d82a52aaf86264d224c6fc
  languageName: node
  linkType: hard

"react-animate-height@npm:^2.1.2":
  version: 2.1.2
  resolution: "react-animate-height@npm:2.1.2"
  dependencies:
    classnames: "npm:^2.2.5"
    prop-types: "npm:^15.6.1"
  peerDependencies:
    react: ">=15.6.2"
    react-dom: ">=15.6.2"
  checksum: ca888e3ec2ec760044b03dff494464c11d05ad3945adadaa7286418d872d79dc021d806c68f96087821b37f20c8eec99556597cdf618cf9f80851e9f9ecb9c5a
  languageName: node
  linkType: hard

"react-display-name@npm:^0.2.4":
  version: 0.2.5
  resolution: "react-display-name@npm:0.2.5"
  checksum: ba27778c975e09afea2bfb58c4052b9e12121329a5115391564085ec64293f2b54d3408b841ad04600142c37d40493442676bf1124d0cc0e68f2f1e02762812b
  languageName: node
  linkType: hard

"react-dnd-html5-backend@npm:11":
  version: 11.1.3
  resolution: "react-dnd-html5-backend@npm:11.1.3"
  dependencies:
    dnd-core: "npm:^11.1.3"
  checksum: 7ca36903c6633a9e5e31eea121561d6b3b56af33a971a5fd4d91bd5693ed19fc99bf077753d70a17ff2c2eabedfe81ec07469ae86a605c697a6cae0bd25c0792
  languageName: node
  linkType: hard

"react-dnd@npm:11":
  version: 11.1.3
  resolution: "react-dnd@npm:11.1.3"
  dependencies:
    "@react-dnd/shallowequal": "npm:^2.0.0"
    "@types/hoist-non-react-statics": "npm:^3.3.1"
    dnd-core: "npm:^11.1.3"
    hoist-non-react-statics: "npm:^3.3.0"
  peerDependencies:
    react: ">= 16.9.0"
    react-dom: ">= 16.9.0"
  checksum: 340bd12529115ed7d0ddaa426f5d2398f8f1774fb178ebe39969cee39bd24a92e51f403a64d5399858c970aee4002828d08b4d99128266efc29b62bb6d9b2743
  languageName: node
  linkType: hard

"react-dom@npm:^18.2.0":
  version: 18.2.0
  resolution: "react-dom@npm:18.2.0"
  dependencies:
    loose-envify: "npm:^1.1.0"
    scheduler: "npm:^0.23.0"
  peerDependencies:
    react: ^18.2.0
  checksum: ca5e7762ec8c17a472a3605b6f111895c9f87ac7d43a610ab7024f68cd833d08eda0625ce02ec7178cc1f3c957cf0b9273cdc17aa2cd02da87544331c43b1d21
  languageName: node
  linkType: hard

"react-hook-form@npm:^7.47.0":
  version: 7.48.2
  resolution: "react-hook-form@npm:7.48.2"
  peerDependencies:
    react: ^16.8.0 || ^17 || ^18
  checksum: 6cb494f359675e78c14c49a33d1c568df1a03db4c750eab18d6554e622fd64e04195c57efa138db03f8988587d132c57d2a7f5f3dd9157be996cc9acd4362170
  languageName: node
  linkType: hard

"react-i18next@npm:^13.5.0":
  version: 13.5.0
  resolution: "react-i18next@npm:13.5.0"
  dependencies:
    "@babel/runtime": "npm:^7.22.5"
    html-parse-stringify: "npm:^3.0.1"
  peerDependencies:
    i18next: ">= 23.2.3"
    react: ">= 16.8.0"
  peerDependenciesMeta:
    react-dom:
      optional: true
    react-native:
      optional: true
  checksum: 903b486d112cd0aa40bdc3afaefd0c32b91c0a1e3e3561367c8d91ddc0fbad9945f1d630c3ddcd4764795fc00e0887252e2d337256a825caf3a86de038f6b2db
  languageName: node
  linkType: hard

"react-intl@npm:^5.13.2":
  version: 5.25.1
  resolution: "react-intl@npm:5.25.1"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:1.11.4"
    "@formatjs/icu-messageformat-parser": "npm:2.1.0"
    "@formatjs/intl": "npm:2.2.1"
    "@formatjs/intl-displaynames": "npm:5.4.3"
    "@formatjs/intl-listformat": "npm:6.5.3"
    "@types/hoist-non-react-statics": "npm:^3.3.1"
    "@types/react": "npm:16 || 17 || 18"
    hoist-non-react-statics: "npm:^3.3.2"
    intl-messageformat: "npm:9.13.0"
    tslib: "npm:^2.1.0"
  peerDependencies:
    react: ^16.3.0 || 17 || 18
    typescript: ^4.5
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 0003ce3ea3c9603ba5cc3d9dc262351897cb07b7d0f6a2aef5b9f65b1cb5902f1359fb8b581c12bf2fb2f1e88841cf6cee968de595ff1c0a7241756a8fa22eb0
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1, react-is@npm:^16.7.0":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 5aa564a1cde7d391ac980bedee21202fc90bdea3b399952117f54fb71a932af1e5902020144fb354b4690b2414a0c7aafe798eb617b76a3d441d956db7726fdf
  languageName: node
  linkType: hard

"react-is@npm:^16.8.0 || ^17.0.0, react-is@npm:^17.0.2":
  version: 17.0.2
  resolution: "react-is@npm:17.0.2"
  checksum: 73b36281e58eeb27c9cc6031301b6ae19ecdc9f18ae2d518bdb39b0ac564e65c5779405d623f1df9abf378a13858b79442480244bd579968afc1faf9a2ce5e05
  languageName: node
  linkType: hard

"react-is@npm:^18.0.0, react-is@npm:^18.2.0":
  version: 18.2.0
  resolution: "react-is@npm:18.2.0"
  checksum: 200cd65bf2e0be7ba6055f647091b725a45dd2a6abef03bf2380ce701fd5edccee40b49b9d15edab7ac08a762bf83cb4081e31ec2673a5bfb549a36ba21570df
  languageName: node
  linkType: hard

"react-is@npm:^18.3.1":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: d5f60c87d285af24b1e1e7eaeb123ec256c3c8bdea7061ab3932e3e14685708221bf234ec50b21e10dd07f008f1b966a2730a0ce4ff67905b3872ff2042aec22
  languageName: node
  linkType: hard

"react-jss@npm:^10.5.1":
  version: 10.10.0
  resolution: "react-jss@npm:10.10.0"
  dependencies:
    "@babel/runtime": "npm:^7.3.1"
    "@emotion/is-prop-valid": "npm:^0.7.3"
    css-jss: "npm:10.10.0"
    hoist-non-react-statics: "npm:^3.2.0"
    is-in-browser: "npm:^1.1.3"
    jss: "npm:10.10.0"
    jss-preset-default: "npm:10.10.0"
    prop-types: "npm:^15.6.0"
    shallow-equal: "npm:^1.2.0"
    theming: "npm:^3.3.0"
    tiny-warning: "npm:^1.0.2"
  peerDependencies:
    react: ">=16.8.6"
  checksum: 7c5181c1e6c83465ffdd55c4ef2641d13929102b70b3d1e3feeebeeffb16ad4a7b87dfa46522caf98a5874f67d74aa132e55571365e339f4d677780ed42c0c3c
  languageName: node
  linkType: hard

"react-lazy-load-image-component@npm:^1.6.0":
  version: 1.6.0
  resolution: "react-lazy-load-image-component@npm:1.6.0"
  dependencies:
    lodash.debounce: "npm:^4.0.8"
    lodash.throttle: "npm:^4.1.1"
  peerDependencies:
    react: ^15.x.x || ^16.x.x || ^17.x.x || ^18.x.x
    react-dom: ^15.x.x || ^16.x.x || ^17.x.x || ^18.x.x
  checksum: 1da26caf885b93cdf6843c729444bd7451dee545d3505b836ecc3c67534982c3d1a942f8e60e04246580dee43dd68b43445636c7e93d2bfdb5942e70ebe5fe96
  languageName: node
  linkType: hard

"react-query@npm:^3.39.3":
  version: 3.39.3
  resolution: "react-query@npm:3.39.3"
  dependencies:
    "@babel/runtime": "npm:^7.5.5"
    broadcast-channel: "npm:^3.4.1"
    match-sorter: "npm:^6.0.2"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
  peerDependenciesMeta:
    react-dom:
      optional: true
    react-native:
      optional: true
  checksum: 17dc4eb75d2ebc262b685096dfaa203202a883ac43768d0faf80f3ad4bdf791dff0691569d6e8ec2ac81b9377c5477e0a1ebea9f51bae1482ca154f5dde50d2d
  languageName: node
  linkType: hard

"react-quill@npm:^2.0.0":
  version: 2.0.0
  resolution: "react-quill@npm:2.0.0"
  dependencies:
    "@types/quill": "npm:^1.3.10"
    lodash: "npm:^4.17.4"
    quill: "npm:^1.3.7"
  peerDependencies:
    react: ^16 || ^17 || ^18
    react-dom: ^16 || ^17 || ^18
  checksum: e9aa49815bf3f52aefcef067eb3e3d474a737464ba06e3ac81a37806bac486bfa440303e5f2bf95b719aedc8918bbe19948ada83a7b62d7211ae95b053cb00b1
  languageName: node
  linkType: hard

"react-redux@npm:^7.2.2":
  version: 7.2.9
  resolution: "react-redux@npm:7.2.9"
  dependencies:
    "@babel/runtime": "npm:^7.15.4"
    "@types/react-redux": "npm:^7.1.20"
    hoist-non-react-statics: "npm:^3.3.2"
    loose-envify: "npm:^1.4.0"
    prop-types: "npm:^15.7.2"
    react-is: "npm:^17.0.2"
  peerDependencies:
    react: ^16.8.3 || ^17 || ^18
  peerDependenciesMeta:
    react-dom:
      optional: true
    react-native:
      optional: true
  checksum: 1c3018bd2601e6d18339281867910b583dcbb3d8856403086e08c00abf0dfe467a94c0d1356bafa8cdf107bf1e2c9899a28486e4778e85c8bc4dfed2076b116f
  languageName: node
  linkType: hard

"react-redux@npm:^8.1.3":
  version: 8.1.3
  resolution: "react-redux@npm:8.1.3"
  dependencies:
    "@babel/runtime": "npm:^7.12.1"
    "@types/hoist-non-react-statics": "npm:^3.3.1"
    "@types/use-sync-external-store": "npm:^0.0.3"
    hoist-non-react-statics: "npm:^3.3.2"
    react-is: "npm:^18.0.0"
    use-sync-external-store: "npm:^1.0.0"
  peerDependencies:
    "@types/react": ^16.8 || ^17.0 || ^18.0
    "@types/react-dom": ^16.8 || ^17.0 || ^18.0
    react: ^16.8 || ^17.0 || ^18.0
    react-dom: ^16.8 || ^17.0 || ^18.0
    react-native: ">=0.59"
    redux: ^4 || ^5.0.0-beta.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
    react-dom:
      optional: true
    react-native:
      optional: true
    redux:
      optional: true
  checksum: c4c7586cff3abeb784e73598d330f5301116a4e9942fd36895f2bccd8990001709c6c3ea1817edb75ee477470d6c67c9113e05a7f86b2b68a3950c9c29fe20cb
  languageName: node
  linkType: hard

"react-transition-group@npm:^4.4.0, react-transition-group@npm:^4.4.5":
  version: 4.4.5
  resolution: "react-transition-group@npm:4.4.5"
  dependencies:
    "@babel/runtime": "npm:^7.5.5"
    dom-helpers: "npm:^5.0.1"
    loose-envify: "npm:^1.4.0"
    prop-types: "npm:^15.6.2"
  peerDependencies:
    react: ">=16.6.0"
    react-dom: ">=16.6.0"
  checksum: ca32d3fd2168c976c5d90a317f25d5f5cd723608b415fb3b9006f9d793c8965c619562d0884503a3e44e4b06efbca4fdd1520f30e58ca3e00a0890e637d55419
  languageName: node
  linkType: hard

"react-virtualized-auto-sizer@npm:^1.0.5":
  version: 1.0.20
  resolution: "react-virtualized-auto-sizer@npm:1.0.20"
  peerDependencies:
    react: ^15.3.0 || ^16.0.0-alpha || ^17.0.0 || ^18.0.0-rc
    react-dom: ^15.3.0 || ^16.0.0-alpha || ^17.0.0 || ^18.0.0-rc
  checksum: 3b7dcc4784906948f287c6342487e1431dda860ef2db4f8d4728fcb60b3ad2161f3bd07fc6499006497d7547652107f5766057357d69bb9552b4961fe98e1448
  languageName: node
  linkType: hard

"react-window@npm:^1.8.6":
  version: 1.8.10
  resolution: "react-window@npm:1.8.10"
  dependencies:
    "@babel/runtime": "npm:^7.0.0"
    memoize-one: "npm:>=3.1.1 <6"
  peerDependencies:
    react: ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0
    react-dom: ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0
  checksum: 6f4a713a2012d605370ef4c7026a45ddd6801e428faa4cad558b12b05ba54c00de72de9a360db109db9666f972a3d955b63af9e5a4cd5fbc52411a382273107b
  languageName: node
  linkType: hard

"react@npm:18.2.0, react@npm:^18.2.0":
  version: 18.2.0
  resolution: "react@npm:18.2.0"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: b9214a9bd79e99d08de55f8bef2b7fc8c39630be97c4e29d7be173d14a9a10670b5325e94485f74cd8bff4966ef3c78ee53c79a7b0b9b70cba20aa8973acc694
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.0, readable-stream@npm:^2.0.2, readable-stream@npm:^2.0.5, readable-stream@npm:~2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 8500dd3a90e391d6c5d889256d50ec6026c059fadee98ae9aa9b86757d60ac46fff24fafb7a39fa41d54cb39d8be56cc77be202ebd4cd8ffcf4cb226cbaa40d4
  languageName: node
  linkType: hard

"readable-stream@npm:^3.1.1, readable-stream@npm:^3.4.0, readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: d9e3e53193adcdb79d8f10f2a1f6989bd4389f5936c6f8b870e77570853561c362bee69feca2bbb7b32368ce96a85504aa4cedf7cf80f36e6a9de30d64244048
  languageName: node
  linkType: hard

"readdir-glob@npm:^1.1.2":
  version: 1.1.3
  resolution: "readdir-glob@npm:1.1.3"
  dependencies:
    minimatch: "npm:^5.1.0"
  checksum: ca3a20aa1e715d671302d4ec785a32bf08e59d6d0dd25d5fc03e9e5a39f8c612cdf809ab3e638a79973db7ad6868492edf38504701e313328e767693671447d6
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 196b30ef6ccf9b6e18c4e1724b7334f72a093d011a99f3b5920470f0b3406a51770867b3e1ae9711f227ef7a7065982f6ee2ce316746b2cb42c88efe44297fe7
  languageName: node
  linkType: hard

"redux-persist@npm:^6.0.0":
  version: 6.0.0
  resolution: "redux-persist@npm:6.0.0"
  peerDependencies:
    redux: ">4.0.0"
  checksum: d3bf03fe8fc90f08985b88419eb32a21f727db2cfc93b486a8267b3884a22c665916a3dce1fd80e9e7d8180a849e5dc614b53e09e0a6803855899d7b3e652242
  languageName: node
  linkType: hard

"redux-thunk@npm:^2.4.2":
  version: 2.4.2
  resolution: "redux-thunk@npm:2.4.2"
  peerDependencies:
    redux: ^4
  checksum: 9bcb1193835128ecebf1e1a1b1a37bc15e8dfbdf6b6ee1b5566dd4c8e4ca05a81175f0c6dda34ab47f87053cd13b74d9f881d59446691d7b192831852b5d7a72
  languageName: node
  linkType: hard

"redux-watch@npm:^1.2.0":
  version: 1.2.0
  resolution: "redux-watch@npm:1.2.0"
  dependencies:
    object-path: "npm:^0.11.5"
  checksum: b80bef16715faf2c20f06a69125fb7ff5faa97ef1fca6b96bf8556a5346de01d58762974526c687764cee68e9b10c31783ae44cbb28ed47e1265ccef0770b91b
  languageName: node
  linkType: hard

"redux@npm:^4.0.0, redux@npm:^4.0.4, redux@npm:^4.2.1":
  version: 4.2.1
  resolution: "redux@npm:4.2.1"
  dependencies:
    "@babel/runtime": "npm:^7.9.2"
  checksum: 371e4833b671193303a7dea7803c8fdc8e0d566740c78f580e0a3b77b4161da25037626900a2205a5d616117fa6ad09a4232e5a110bd437186b5c6355a041750
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.4":
  version: 1.0.4
  resolution: "reflect.getprototypeof@npm:1.0.4"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
    get-intrinsic: "npm:^1.2.1"
    globalthis: "npm:^1.0.3"
    which-builtin-type: "npm:^1.1.3"
  checksum: 52ff881f62a9cb4acdd7f9a8f4ac88234056c4a6b1ed570c249cc085de5c313249b90251d16eb8e58302b82ae697eec19dde16ff62949f6b87f035a3a26dc5df
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.1.0":
  version: 10.1.1
  resolution: "regenerate-unicode-properties@npm:10.1.1"
  dependencies:
    regenerate: "npm:^1.4.2"
  checksum: b855152efdcca0ecc37ceb0cb6647a544344555fc293af3b57191b918e1bc9c95ee404a9a64a1d692bf66d45850942c29d93f2740c0d1980d3a8ea2ca63b184e
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: dc6c95ae4b3ba6adbd7687cafac260eee4640318c7a95239d5ce847d9b9263979758389e862fe9c93d633b5792ea4ada5708df75885dc5aa05a309fa18140a87
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.0
  resolution: "regenerator-runtime@npm:0.14.0"
  checksum: 6c19495baefcf5fbb18a281b56a97f0197b5f219f42e571e80877f095320afac0bdb31dab8f8186858e6126950068c3f17a1226437881e3e70446ea66751897c
  languageName: node
  linkType: hard

"regenerator-transform@npm:^0.15.2":
  version: 0.15.2
  resolution: "regenerator-transform@npm:0.15.2"
  dependencies:
    "@babel/runtime": "npm:^7.8.4"
  checksum: c4fdcb46d11bbe32605b4b9ed76b21b8d3f241a45153e9dc6f5542fed4c7744fed459f42701f650d5d5956786bf7de57547329d1c05a9df2ed9e367b9d903302
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.0, regexp.prototype.flags@npm:^1.5.1":
  version: 1.5.1
  resolution: "regexp.prototype.flags@npm:1.5.1"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    set-function-name: "npm:^2.0.0"
  checksum: 3fa5610b8e411bbc3a43ddfd13162f3a817beb43155fbd8caa24d4fd0ce2f431a8197541808772a5a06e5946cebfb68464c827827115bde0d11720a92fe2981a
  languageName: node
  linkType: hard

"regexpu-core@npm:^5.3.1":
  version: 5.3.2
  resolution: "regexpu-core@npm:5.3.2"
  dependencies:
    "@babel/regjsgen": "npm:^0.8.0"
    regenerate: "npm:^1.4.2"
    regenerate-unicode-properties: "npm:^10.1.0"
    regjsparser: "npm:^0.9.1"
    unicode-match-property-ecmascript: "npm:^2.0.0"
    unicode-match-property-value-ecmascript: "npm:^2.1.0"
  checksum: ed0d7c66d84c633fbe8db4939d084c780190eca11f6920807dfb8ebac59e2676952cd8f2008d9c86ae8cf0463ea5fd12c5cff09ef2ce7d51ee6b420a5eb4d177
  languageName: node
  linkType: hard

"regjsparser@npm:^0.9.1":
  version: 0.9.1
  resolution: "regjsparser@npm:0.9.1"
  dependencies:
    jsesc: "npm:~0.5.0"
  bin:
    regjsparser: bin/parser
  checksum: be7757ef76e1db10bf6996001d1021048b5fb12f5cb470a99b8cf7f3ff943f0f0e2291c0dcdbb418b458ddc4ac10e48680a822b69ef487a0284c8b6b77beddc3
  languageName: node
  linkType: hard

"remove-accents@npm:0.4.2":
  version: 0.4.2
  resolution: "remove-accents@npm:0.4.2"
  checksum: 25d5c84f1355be60b42f8ad8a1ba29c2413dead138a9e2a88633e4fc94a1b953afe306dd27479125aef231d267482c54d292e558b04ec100fb9adf063ccbf75a
  languageName: node
  linkType: hard

"remove-accents@npm:^0.4.2":
  version: 0.4.4
  resolution: "remove-accents@npm:0.4.4"
  checksum: 13d1b4086d3926928117d3253ede78f1bb1084f8897c7a60043b392a47663d5b3abd008bc854dc225d6f762dd5a5845acf15add1b02826f1e11de37862bad024
  languageName: node
  linkType: hard

"reselect@npm:^4.1.8":
  version: 4.1.8
  resolution: "reselect@npm:4.1.8"
  checksum: 199984d9872f71cd207f4aa6e6fd2bd48d95154f7aa9b3aee3398335f39f5491059e732f28c12e9031d5d434adab2c458dc8af5afb6564d0ad37e1644445e09c
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 91eb76ce83621eea7bbdd9b55121a5c1c4a39e54a9ce04a9ad4517f102f8b5131c2cf07622c738a6683991bf54f2ce178f5a42803ecbd527ddc5105f362cc9e3
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 0763150adf303040c304009231314d1e84c6e5ebfa2d82b7d94e96a6e82bacd1dcc0b58ae257315f3c8adb89a91d8d0f12928241cba2df1680fbe6f60bf99b0e
  languageName: node
  linkType: hard

"resolve@npm:^1.14.2, resolve@npm:^1.19.0, resolve@npm:^1.22.4":
  version: 1.22.8
  resolution: "resolve@npm:1.22.8"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: c473506ee01eb45cbcfefb68652ae5759e092e6b0fb64547feadf9736a6394f258fbc6f88e00c5ca36d5477fbb65388b272432a3600fa223062e54333c156753
  languageName: node
  linkType: hard

"resolve@npm:^2.0.0-next.4":
  version: 2.0.0-next.5
  resolution: "resolve@npm:2.0.0-next.5"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 2d6fd28699f901744368e6f2032b4268b4c7b9185fd8beb64f68c93ac6b22e52ae13560ceefc96241a665b985edf9ffd393ae26d2946a7d3a07b7007b7d51e79
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.14.2#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.19.0#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.4#optional!builtin<compat/resolve>":
  version: 1.22.8
  resolution: "resolve@patch:resolve@npm%3A1.22.8#optional!builtin<compat/resolve>::version=1.22.8&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: f345cd37f56a2c0275e3fe062517c650bb673815d885e7507566df589375d165bbbf4bdb6aa95600a9bc55f4744b81f452b5a63f95b9f10a72787dba3c90890a
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^2.0.0-next.4#optional!builtin<compat/resolve>":
  version: 2.0.0-next.5
  resolution: "resolve@patch:resolve@npm%3A2.0.0-next.5#optional!builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 05fa778de9d0347c8b889eb7a18f1f06bf0f801b0eb4610b4871a4b2f22e220900cf0ad525e94f990bb8d8921c07754ab2122c0c225ab4cdcea98f36e64fa4c2
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 1f914879f97e7ee931ad05fe3afa629bd55270fc6cf1c1e589b6a99fab96d15daad0fa1a52a00c729ec0078045fe3e399bd4fd0c93bcc906957bdc17f89cb8e6
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: 14222c9e1d3f9ae01480c50d96057228a8524706db79cdeb5a2ce5bb7070dd9f409a6f84a02cbef8cdc80d39aef86f2dd03d155188a1300c599b05437dcd2ffb
  languageName: node
  linkType: hard

"rimraf@npm:2":
  version: 2.7.1
  resolution: "rimraf@npm:2.7.1"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: ./bin.js
  checksum: 4586c296c736483e297da7cffd19475e4a3e41d07b1ae124aad5d687c79e4ffa716bdac8732ed1db942caf65271cee9dd39f8b639611de161a2753e2112ffe1d
  languageName: node
  linkType: hard

"rimraf@npm:3.0.2, rimraf@npm:^3.0.0, rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: bin.js
  checksum: 063ffaccaaaca2cfd0ef3beafb12d6a03dd7ff1260d752d62a6077b5dfff6ae81bea571f655bb6b589d366930ec1bdd285d40d560c0dae9b12f125e54eb743d5
  languageName: node
  linkType: hard

"run-applescript@npm:^5.0.0":
  version: 5.0.0
  resolution: "run-applescript@npm:5.0.0"
  dependencies:
    execa: "npm:^5.0.0"
  checksum: d00c2dbfa5b2d774de7451194b8b125f40f65fc183de7d9dcae97f57f59433586d3c39b9001e111c38bfa24c3436c99df1bb4066a2a0c90d39a8c4cd6889af77
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"rxjs@npm:7.8.0":
  version: 7.8.0
  resolution: "rxjs@npm:7.8.0"
  dependencies:
    tslib: "npm:^2.1.0"
  checksum: ff9359cc7875edecc8fc487481366b876b488901178cca8f2bdad03e00d2b5a19b01d2b02d3b4ebd47e574264db8460c6c2386076c3189b359b5e8c70a6e51e3
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.0.1":
  version: 1.0.1
  resolution: "safe-array-concat@npm:1.0.1"
  dependencies:
    call-bind: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.1"
    has-symbols: "npm:^1.0.3"
    isarray: "npm:^2.0.5"
  checksum: 44f073d85ca12458138e6eff103ac63cec619c8261b6579bd2fa3ae7b6516cf153f02596d68e40c5bbe322a29c930017800efff652734ddcb8c0f33b2a71f89c
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 7eb5b48f2ed9a594a4795677d5a150faa7eb54483b2318b568dc0c4fc94092a6cce5be02c7288a0500a156282f5276d5688bce7259299568d1053b2150ef374a
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 32872cd0ff68a3ddade7a7617b8f4c2ae8764d8b7d884c651b74457967a9e0e886267d3ecc781220629c44a865167b61c375d2da6c720c840ecd73f45d5d9451
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-regex-test@npm:1.0.0"
  dependencies:
    call-bind: "npm:^1.0.2"
    get-intrinsic: "npm:^1.1.3"
    is-regex: "npm:^1.1.4"
  checksum: c7248dfa07891aa634c8b9c55da696e246f8589ca50e7fd14b22b154a106e83209ddf061baf2fa45ebfbd485b094dc7297325acfc50724de6afe7138451b42a9
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 7eaf7a0cf37cc27b42fb3ef6a9b1df6e93a1c6d98c6c6702b02fe262d5fcbd89db63320793b99b21cb5348097d0a53de81bd5f4e8b86e20cc9412e3f1cfb4e83
  languageName: node
  linkType: hard

"sass@npm:^1.69.5":
  version: 1.69.5
  resolution: "sass@npm:1.69.5"
  dependencies:
    chokidar: "npm:>=3.0.0 <4.0.0"
    immutable: "npm:^4.0.0"
    source-map-js: "npm:>=0.6.2 <2.0.0"
  bin:
    sass: sass.js
  checksum: b320ab22061b3c7fe8cee43b13329b281dd7d86691b8b7c55dec3e47d3ede988989dac56db4dff57ee847d10252a26b611be1b0a5f7c3a0f6a6405ef37a6d018
  languageName: node
  linkType: hard

"saxes@npm:^5.0.1":
  version: 5.0.1
  resolution: "saxes@npm:5.0.1"
  dependencies:
    xmlchars: "npm:^2.2.0"
  checksum: 148b5f98fdd45df25fa1abef35d72cdf6457ac5aef3b7d59d60f770af09d8cf6e7e3a074197071222441d68670fd3198590aba9985e37c4738af2df2f44d0686
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.0":
  version: 0.23.0
  resolution: "scheduler@npm:0.23.0"
  dependencies:
    loose-envify: "npm:^1.1.0"
  checksum: 0c4557aa37bafca44ff21dc0ea7c92e2dbcb298bc62eae92b29a39b029134f02fb23917d6ebc8b1fa536b4184934314c20d8864d156a9f6357f3398aaf7bfda8
  languageName: node
  linkType: hard

"semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 1ef3a85bd02a760c6ef76a45b8c1ce18226de40831e02a00bad78485390b98b6ccaa31046245fc63bba4a47a6a592b6c7eedc65cc47126e60489f9cc1ce3ed7e
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.5.4":
  version: 7.5.4
  resolution: "semver@npm:7.5.4"
  dependencies:
    lru-cache: "npm:^6.0.0"
  bin:
    semver: bin/semver.js
  checksum: 985dec0d372370229a262c737063860fabd4a1c730662c1ea3200a2f649117761a42184c96df62a0e885e76fbd5dace41087d6c1ac0351b13c0df5d6bcb1b5ac
  languageName: node
  linkType: hard

"set-function-length@npm:^1.1.1":
  version: 1.1.1
  resolution: "set-function-length@npm:1.1.1"
  dependencies:
    define-data-property: "npm:^1.1.1"
    get-intrinsic: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
  checksum: 745ed1d7dc69a6185e0820082fe73838ab3dfd01e75cce83a41e4c1d68bbf34bc5fb38f32ded542ae0b557536b5d2781594499b5dcd19e7db138e06292a76c7b
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.0, set-function-name@npm:^2.0.1":
  version: 2.0.1
  resolution: "set-function-name@npm:2.0.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.0"
  checksum: 4975d17d90c40168eee2c7c9c59d023429f0a1690a89d75656306481ece0c3c1fb1ebcc0150ea546d1913e35fbd037bace91372c69e543e51fc5d1f31a9fa126
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5, setimmediate@npm:~1.0.4":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 76e3f5d7f4b581b6100ff819761f04a984fa3f3990e72a6554b57188ded53efce2d3d6c0932c10f810b7c59414f85e2ab3c11521877d1dea1ce0b56dc906f485
  languageName: node
  linkType: hard

"shallow-equal@npm:^1.2.0":
  version: 1.2.1
  resolution: "shallow-equal@npm:1.2.1"
  checksum: 4f1645cc516e7754c4438db687e1da439a5f29a7dba2ba90c5f88e5708aeb17bc4355ba45cad805b0e95dc898e37d8bf6d77d854919c7512f89939986cff8cd1
  languageName: node
  linkType: hard

"shallowequal@npm:^1.1.0":
  version: 1.1.0
  resolution: "shallowequal@npm:1.1.0"
  checksum: f4c1de0837f106d2dbbfd5d0720a5d059d1c66b42b580965c8f06bb1db684be8783538b684092648c981294bf817869f743a066538771dbecb293df78f765e00
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"shortid@npm:^2.2.16":
  version: 2.2.16
  resolution: "shortid@npm:2.2.16"
  dependencies:
    nanoid: "npm:^2.1.0"
  checksum: 0a00ce19fd319d9f3a2e646adf985a99f246475624f2e5d0d1811354b77d342e84c590ef5a7b22ea3e866f5c5821eac4905308f295fa95de74afccc8fed6ea2e
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4":
  version: 1.0.4
  resolution: "side-channel@npm:1.0.4"
  dependencies:
    call-bind: "npm:^1.0.0"
    get-intrinsic: "npm:^1.0.2"
    object-inspect: "npm:^1.9.0"
  checksum: c4998d9fc530b0e75a7fd791ad868fdc42846f072734f9080ff55cc8dc7d3899abcda24fd896aa6648c3ab7021b4bb478073eb4f44dfd55bce9714bc1a7c5d45
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: c9fa63bbbd7431066174a48ba2dd9986dfd930c3a8b59de9c29d7b6854ec1c12a80d15310869ea5166d413b99f041bfa3dd80a7947bcd44ea8e6eb3ffeabfa1f
  languageName: node
  linkType: hard

"simplebar-core@npm:^1.2.4":
  version: 1.2.4
  resolution: "simplebar-core@npm:1.2.4"
  dependencies:
    "@types/lodash-es": "npm:^4.17.6"
    can-use-dom: "npm:^0.1.0"
    lodash: "npm:^4.17.21"
    lodash-es: "npm:^4.17.21"
  checksum: d3716a37c5e4279152bf79f034044d3e19221e819b337f8faf505fc40695292d62e1c2380b0e10b63bc5d626c05fd88d0d3fd22215c491a6888058cd72c033f2
  languageName: node
  linkType: hard

"simplebar-react@npm:^3.2.4":
  version: 3.2.4
  resolution: "simplebar-react@npm:3.2.4"
  dependencies:
    simplebar-core: "npm:^1.2.4"
  peerDependencies:
    react: ">=16.8.0"
  checksum: d31181ee9282b5b7588c21c7c5e648fcba4ec8243145a6d31906b63644507f1f0ac9f4e4ac7cfdf82d533b46bb5cdda2d0cf1c826b48eca0aee022a09c4ef049
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 927484aa0b1640fd9473cee3e0a0bcad6fce93fd7bbc18bac9ad0c33686f5d2e2c422fba24b5899c184524af01e11dd2bd051c2bf2b07e47aff8ca72cbfc60d2
  languageName: node
  linkType: hard

"snake-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "snake-case@npm:3.0.4"
  dependencies:
    dot-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 0a7a79900bbb36f8aaa922cf111702a3647ac6165736d5dc96d3ef367efc50465cac70c53cd172c382b022dac72ec91710608e5393de71f76d7142e6fd80e8a3
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.1":
  version: 8.0.2
  resolution: "socks-proxy-agent@npm:8.0.2"
  dependencies:
    agent-base: "npm:^7.0.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.7.1"
  checksum: ea727734bd5b2567597aa0eda14149b3b9674bb44df5937bbb9815280c1586994de734d965e61f1dd45661183d7b41f115fb9e432d631287c9063864cfcc2ecc
  languageName: node
  linkType: hard

"socks@npm:^2.7.1":
  version: 2.7.1
  resolution: "socks@npm:2.7.1"
  dependencies:
    ip: "npm:^2.0.0"
    smart-buffer: "npm:^4.2.0"
  checksum: 5074f7d6a13b3155fa655191df1c7e7a48ce3234b8ccf99afa2ccb56591c195e75e8bb78486f8e9ea8168e95a29573cbaad55b2b5e195160ae4d2ea6811ba833
  languageName: node
  linkType: hard

"source-map-js@npm:>=0.6.2 <2.0.0, source-map-js@npm:^1.0.1, source-map-js@npm:^1.0.2":
  version: 1.0.2
  resolution: "source-map-js@npm:1.0.2"
  checksum: 38e2d2dd18d2e331522001fc51b54127ef4a5d473f53b1349c5cca2123562400e0986648b52e9407e348eaaed53bce49248b6e2641e6d793ca57cb2c360d6d51
  languageName: node
  linkType: hard

"source-map@npm:^0.5.7":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 9b4ac749ec5b5831cad1f8cc4c19c4298ebc7474b24a0acf293e2f040f03f8eeccb3d01f12aa0f90cf46d555c887e03912b83a042c627f419bda5152d89c5269
  languageName: node
  linkType: hard

"ssri@npm:^10.0.0":
  version: 10.0.5
  resolution: "ssri@npm:10.0.5"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 453f9a1c241c13f5dfceca2ab7b4687bcff354c3ccbc932f35452687b9ef0ccf8983fd13b8a3baa5844c1a4882d6e3ddff48b0e7fd21d743809ef33b80616d79
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: 612c2b2a7dbcc859f74597112f80a42cbe4d448d03da790d5b7b39673c1197dd3789e91cd67210353e58857395d32c1e955a9041c4e6d5bae723436b3ed9ed14
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.8":
  version: 4.0.10
  resolution: "string.prototype.matchall@npm:4.0.10"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
    get-intrinsic: "npm:^1.2.1"
    has-symbols: "npm:^1.0.3"
    internal-slot: "npm:^1.0.5"
    regexp.prototype.flags: "npm:^1.5.0"
    set-function-name: "npm:^2.0.0"
    side-channel: "npm:^1.0.4"
  checksum: 0f7a1a7f91790cd45f804039a16bc6389c8f4f25903e648caa3eea080b019a5c7b0cac2ca83976646140c2332b159042140bf389f23675609d869dd52450cddc
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.8":
  version: 1.2.8
  resolution: "string.prototype.trim@npm:1.2.8"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
  checksum: 9301f6cb2b6c44f069adde1b50f4048915985170a20a1d64cf7cb2dc53c5cd6b9525b92431f1257f894f94892d6c4ae19b5aa7f577c3589e7e51772dffc9d5a4
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.7":
  version: 1.0.7
  resolution: "string.prototype.trimend@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
  checksum: 3f0d3397ab9bd95cd98ae2fe0943bd3e7b63d333c2ab88f1875cf2e7c958c75dc3355f6fe19ee7c8fca28de6f39f2475e955e103821feb41299a2764a7463ffa
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.7":
  version: 1.0.7
  resolution: "string.prototype.trimstart@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.2"
    define-properties: "npm:^1.2.0"
    es-abstract: "npm:^1.22.1"
  checksum: 6e594d3a61b127d243b8be1312e9f78683abe452cfe0bcafa3e0dc62ad6f030ccfb64d87ed3086fb7cb540fda62442c164d237cc5cc4d53c6e3eb659c29a0aeb
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 54d23f4a6acae0e93f999a585e673be9e561b65cd4cca37714af1e893ab8cd8dfa52a9e4f58f48f87b4a44918d3a9254326cb80ed194bf2e4c226e2b21767e56
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: 7c41c17ed4dea105231f6df208002ebddd732e8e9e2d619d133cecd8e0087ddfd9587d2feb3c8caf3213cbd841ada6d057f5142cae68a4e62d3540778d9819b4
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: ae3b5436d34fadeb6096367626ce987057713c566e1e7768818797e00ac5d62023d0f198c4e681eae9e20701721980b26a64a8f5b91238869592a9c6800719a2
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 475f53e9c44375d6e72807284024ac5d668ee1d06010740dec0b9744f2ddf47de8d7151f80e5f6190fc8f384e802fdf9504b76a7e9020c9faee7103623338be2
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 8d50ff27b7ebe5ecc78f1fe1e00fcdff7af014e73cf724b46fb81ef889eeb1015fc5184b64e81a2efe002180f3ba431bdd77e300da5c6685d702780fbf0c8d5b
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 69412b5e25731e1938184b5d489c32e340605bb611d6140344abc3421b7f3c6f9984b21dff296dfcf056681b82caa3bb4cc996a965ce37bcfad663e92eae9c64
  languageName: node
  linkType: hard

"strip-final-newline@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-final-newline@npm:3.0.0"
  checksum: 23ee263adfa2070cd0f23d1ac14e2ed2f000c9b44229aec9c799f1367ec001478469560abefd00c5c99ee6f0b31c137d53ec6029c53e9f32a93804e18c201050
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"styled-components@npm:^5.3.0":
  version: 5.3.11
  resolution: "styled-components@npm:5.3.11"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.0.0"
    "@babel/traverse": "npm:^7.4.5"
    "@emotion/is-prop-valid": "npm:^1.1.0"
    "@emotion/stylis": "npm:^0.8.4"
    "@emotion/unitless": "npm:^0.7.4"
    babel-plugin-styled-components: "npm:>= 1.12.0"
    css-to-react-native: "npm:^3.0.0"
    hoist-non-react-statics: "npm:^3.0.0"
    shallowequal: "npm:^1.1.0"
    supports-color: "npm:^5.5.0"
  peerDependencies:
    react: ">= 16.8.0"
    react-dom: ">= 16.8.0"
    react-is: ">= 16.8.0"
  checksum: 7e1baee0f7b4479fe1a4064e4ae87e40f1ba583030d04827cef73fa7b36d3a91ed552dc76164d319216039f906af42a5229648c023482280fa4b5f71f00eef2d
  languageName: node
  linkType: hard

"styled-jsx@npm:5.1.1":
  version: 5.1.1
  resolution: "styled-jsx@npm:5.1.1"
  dependencies:
    client-only: "npm:0.0.1"
  peerDependencies:
    react: ">= 16.8.0 || 17.x.x || ^18.0.0-0"
  peerDependenciesMeta:
    "@babel/core":
      optional: true
    babel-plugin-macros:
      optional: true
  checksum: 4f6a5d0010770fdeea1183d919d528fd46c484e23c0535ef3e1dd49488116f639c594f3bd4440e3bc8a8686c9f8d53c5761599870ff039ede11a5c3bfe08a4be
  languageName: node
  linkType: hard

"stylis-plugin-rtl@npm:^2.1.1":
  version: 2.1.1
  resolution: "stylis-plugin-rtl@npm:2.1.1"
  dependencies:
    cssjanus: "npm:^2.0.1"
  peerDependencies:
    stylis: 4.x
  checksum: 262d2c762216556ed3dc8218ea5929ae24b4b0fae020eb41c217911e6628374009d1e1b24256ed6a2f39cbbf302a877f2efe912b628c71dd15fde90866ca54ca
  languageName: node
  linkType: hard

"stylis@npm:4.2.0":
  version: 4.2.0
  resolution: "stylis@npm:4.2.0"
  checksum: 58359185275ef1f39c339ae94e598168aa6bb789f6cf0d52e726c1e7087a94e9c17f0385a28d34483dec1ffc2c75670ec714dc5603d99c3124ec83bc2b0a0f42
  languageName: node
  linkType: hard

"stylis@npm:^4.3.0":
  version: 4.3.0
  resolution: "stylis@npm:4.3.0"
  checksum: 54eb1a13a9ec394a01a2e1a5ca8f856b96ecd8b85b8c04a24c0ff0aa8416798a6a1e9555f6a4345b6f088d03424f5a4376ea093d0ec73e419698415a3c8b59d0
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0, supports-color@npm:^5.5.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: 5f505c6fa3c6e05873b43af096ddeb22159831597649881aeb8572d6fe3b81e798cc10840d0c9735e0026b250368851b7f77b65e84f4e4daa820a4f69947f55b
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: c8bb7afd564e3b26b50ca6ee47572c217526a1389fe018d00345856d4a9b08ffbd61fadaf283a87368d94c3dcdb8f5ffe2650a5a65863e21ad2730ca0f05210a
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: a9dc19ae2220c952bd2231d08ddeecb1b0328b61e72071ff4000c8384e145cc07c1c0bdb3b5a1cb06e186a7b2790f1dee793418b332f6ddf320de25d9125be7e
  languageName: node
  linkType: hard

"svg-parser@npm:^2.0.4":
  version: 2.0.4
  resolution: "svg-parser@npm:2.0.4"
  checksum: ec196da6ea21481868ab26911970e35488361c39ead1c6cdd977ba16c885c21a91ddcbfd113bfb01f79a822e2a751ef85b2f7f95e2cb9245558ebce12c34af1f
  languageName: node
  linkType: hard

"svgo@npm:^3.0.2":
  version: 3.0.5
  resolution: "svgo@npm:3.0.5"
  dependencies:
    "@trysound/sax": "npm:0.2.0"
    commander: "npm:^7.2.0"
    css-select: "npm:^5.1.0"
    css-tree: "npm:^2.2.1"
    css-what: "npm:^6.1.0"
    csso: "npm:5.0.5"
    picocolors: "npm:^1.0.0"
  bin:
    svgo: ./bin/svgo
  checksum: 9bd9ff340b3870e0e45b274a74dd9fadbddd09e268d03a3c14d74f96a23b446e2e6a4b2e300d577474c7a3249e7d31b09bb498415b9ffd7cfe88e8e1e145299f
  languageName: node
  linkType: hard

"swr@npm:^1.3.0":
  version: 1.3.0
  resolution: "swr@npm:1.3.0"
  peerDependencies:
    react: ^16.11.0 || ^17.0.0 || ^18.0.0
  checksum: 37077ffba20d33ff5caa12fcff9dbcedafb6a50a5f88d8e20908bc354fb0d40b619ecce1c7239a231080189ffc97c870a294c07a3b1383d2ad78b547f85f9f3b
  languageName: node
  linkType: hard

"symbol-observable@npm:^1.2.0":
  version: 1.2.0
  resolution: "symbol-observable@npm:1.2.0"
  checksum: 4684327a2fef2453dcd4238b5bd8f69c460a4708fb8c024a824c6a707ca644b2b2a586e36e5197d0d1162ff48e288299a48844a8c46274ffcfd9260e03df7692
  languageName: node
  linkType: hard

"synckit@npm:^0.8.5":
  version: 0.8.6
  resolution: "synckit@npm:0.8.6"
  dependencies:
    "@pkgr/utils": "npm:^2.4.2"
    tslib: "npm:^2.6.2"
  checksum: 565c659b5c935905e3774f8a53b013aeb1db03b69cb26cfea742021a274fba792e6ec22f1f918bfb6a7fe16dc9ab6e32a94b4289a8d5d9039b695cd9d524953d
  languageName: node
  linkType: hard

"tapable@npm:^2.2.0":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 1769336dd21481ae6347611ca5fca47add0962fd8e80466515032125eca0084a4f0ede11e65341b9c0018ef4e1cf1ad820adbb0fba7cc99865c6005734000b0a
  languageName: node
  linkType: hard

"tar-stream@npm:^2.2.0":
  version: 2.2.0
  resolution: "tar-stream@npm:2.2.0"
  dependencies:
    bl: "npm:^4.0.3"
    end-of-stream: "npm:^1.4.1"
    fs-constants: "npm:^1.0.0"
    inherits: "npm:^2.0.3"
    readable-stream: "npm:^3.1.1"
  checksum: 1a52a51d240c118cbcd30f7368ea5e5baef1eac3e6b793fb1a41e6cd7319296c79c0264ccc5859f5294aa80f8f00b9239d519e627b9aade80038de6f966fec6a
  languageName: node
  linkType: hard

"tar@npm:^6.1.11, tar@npm:^6.1.2":
  version: 6.2.0
  resolution: "tar@npm:6.2.0"
  dependencies:
    chownr: "npm:^2.0.0"
    fs-minipass: "npm:^2.0.0"
    minipass: "npm:^5.0.0"
    minizlib: "npm:^2.1.1"
    mkdirp: "npm:^1.0.3"
    yallist: "npm:^4.0.0"
  checksum: 2042bbb14830b5cd0d584007db0eb0a7e933e66d1397e72a4293768d2332449bc3e312c266a0887ec20156dea388d8965e53b4fc5097f42d78593549016da089
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: 4383b5baaeffa9bb4cda2ac33a4aa2e6d1f8aaf811848bf73513a9b88fd76372dc461f6fd6d2e9cb5100f48b473be32c6f95bd983509b7d92bb4d92c10747452
  languageName: node
  linkType: hard

"theming@npm:^3.3.0":
  version: 3.3.0
  resolution: "theming@npm:3.3.0"
  dependencies:
    hoist-non-react-statics: "npm:^3.3.0"
    prop-types: "npm:^15.5.8"
    react-display-name: "npm:^0.2.4"
    tiny-warning: "npm:^1.0.2"
  peerDependencies:
    react: ">=16.3"
  checksum: 904cc25891b8630b30b91356f2728b826d9dd1234ea3260e3734aff1c63c58f67639c8591a00096ff2c131c9159e01f6763f3a2512995ac7ddd6240cdd4edff0
  languageName: node
  linkType: hard

"tiny-case@npm:^1.0.3":
  version: 1.0.3
  resolution: "tiny-case@npm:1.0.3"
  checksum: 3f7a30c39d5b0e1bc097b0b271bec14eb5b836093db034f35a0de26c14422380b50dc12bfd37498cf35b192f5df06f28a710712c87ead68872a9e37ad6f6049d
  languageName: node
  linkType: hard

"tiny-warning@npm:^1.0.2":
  version: 1.0.3
  resolution: "tiny-warning@npm:1.0.3"
  checksum: da62c4acac565902f0624b123eed6dd3509bc9a8d30c06e017104bedcf5d35810da8ff72864400ad19c5c7806fc0a8323c68baf3e326af7cb7d969f846100d71
  languageName: node
  linkType: hard

"titleize@npm:^3.0.0":
  version: 3.0.0
  resolution: "titleize@npm:3.0.0"
  checksum: 71fbbeabbfb36ccd840559f67f21e356e1d03da2915b32d2ae1a60ddcc13a124be2739f696d2feb884983441d159a18649e8d956648d591bdad35c430a6b6d28
  languageName: node
  linkType: hard

"tmp@npm:^0.2.0":
  version: 0.2.1
  resolution: "tmp@npm:0.2.1"
  dependencies:
    rimraf: "npm:^3.0.0"
  checksum: 445148d72df3ce99356bc89a7857a0c5c3b32958697a14e50952c6f7cf0a8016e746ababe9a74c1aa52f04c526661992f14659eba34d3c6701d49ba2f3cf781b
  languageName: node
  linkType: hard

"to-fast-properties@npm:^2.0.0":
  version: 2.0.0
  resolution: "to-fast-properties@npm:2.0.0"
  checksum: be2de62fe58ead94e3e592680052683b1ec986c72d589e7b21e5697f8744cdbf48c266fa72f6c15932894c10187b5f54573a3bcf7da0bfd964d5caf23d436168
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10dda13571e1f5ad37546827e9b6d4252d2e0bc176c24a101252153ef435d83696e2557fe128c4678e4e78f5f01e83711c703eef9814eb12dab028580d45980a
  languageName: node
  linkType: hard

"toposort@npm:^2.0.2":
  version: 2.0.2
  resolution: "toposort@npm:2.0.2"
  checksum: 6f128353e4ed9739e49a28fb756b0a00f3752b29fc9b862ff781446598ee3b486cd229697feebc4eabd916eac5de219f3dae450c585bf13673f6b133a7226e06
  languageName: node
  linkType: hard

"traverse@npm:>=0.3.0 <0.4":
  version: 0.3.9
  resolution: "traverse@npm:0.3.9"
  checksum: ffbb8460a934f271b7b7ae654e676f740d81037d6c20ab9fd05781cfdf644929f494399b5cb3aa3db4ab69cbfef06ff8f885560d523ca49b7da33763f6c4c9f1
  languageName: node
  linkType: hard

"ts-api-utils@npm:^1.0.1":
  version: 1.0.3
  resolution: "ts-api-utils@npm:1.0.3"
  peerDependencies:
    typescript: ">=4.2.0"
  checksum: 1350a5110eb1e534e9a6178f4081fb8a4fcc439749e19f4ad699baec9090fcb90fe532d5e191d91a062dc6e454a14a8d7eb2ad202f57135a30c4a44a3024f039
  languageName: node
  linkType: hard

"ts-custom-error@npm:^3.2.1":
  version: 3.3.1
  resolution: "ts-custom-error@npm:3.3.1"
  checksum: 92e3a2c426bf6049579aeb889b6f9787e0cfb6bb715a1457e2571708be7fe739662ca9eb2a8c61b72a2d32189645f4fbcf1a370087e030d922e9e2a7b7c1c994
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.14.2":
  version: 3.14.2
  resolution: "tsconfig-paths@npm:3.14.2"
  dependencies:
    "@types/json5": "npm:^0.0.29"
    json5: "npm:^1.0.2"
    minimist: "npm:^1.2.6"
    strip-bom: "npm:^3.0.0"
  checksum: 17f23e98612a60cf23b80dc1d3b7b840879e41fcf603868fc3618a30f061ac7b463ef98cad8c28b68733b9bfe0cc40ffa2bcf29e94cf0d26e4f6addf7ac8527d
  languageName: node
  linkType: hard

"tsdef@npm:^0.0.14":
  version: 0.0.14
  resolution: "tsdef@npm:0.0.14"
  checksum: 426db840b57d0d0f2ee0ab5af82e5357e31cdec7fa95530c151699f3e2ee9db40aedb6a15f755071fb786de785c154748df01a125b56efaa99ea19227bf89df2
  languageName: node
  linkType: hard

"tslib@npm:^2.0.3, tslib@npm:^2.1.0, tslib@npm:^2.4.0, tslib@npm:^2.6.0, tslib@npm:^2.6.2":
  version: 2.6.2
  resolution: "tslib@npm:2.6.2"
  checksum: bd26c22d36736513980091a1e356378e8b662ded04204453d353a7f34a4c21ed0afc59b5f90719d4ba756e581a162ecbf93118dc9c6be5acf70aa309188166ca
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 14687776479d048e3c1dbfe58a2409e00367810d6960c0f619b33793271ff2a27f81b52461f14a162f1f89a9b1d8da1b237fc7c99b0e1fdcec28ec63a86b1fec
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 8907e16284b2d6cfa4f4817e93520121941baba36b39219ea36acfe64c86b9dbc10c9941af450bd60832c8f43464974d51c0957f9858bc66b952b66b6914cbb9
  languageName: node
  linkType: hard

"type-fest@npm:^2.19.0":
  version: 2.19.0
  resolution: "type-fest@npm:2.19.0"
  checksum: 7bf9e8fdf34f92c8bb364c0af14ca875fac7e0183f2985498b77be129dc1b3b1ad0a6b3281580f19e48c6105c037fb966ad9934520c69c6434d17fd0af4eed78
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.0":
  version: 1.0.0
  resolution: "typed-array-buffer@npm:1.0.0"
  dependencies:
    call-bind: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.1"
    is-typed-array: "npm:^1.1.10"
  checksum: 3e0281c79b2a40cd97fe715db803884301993f4e8c18e8d79d75fd18f796e8cd203310fec8c7fdb5e6c09bedf0af4f6ab8b75eb3d3a85da69328f28a80456bd3
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.0":
  version: 1.0.0
  resolution: "typed-array-byte-length@npm:1.0.0"
  dependencies:
    call-bind: "npm:^1.0.2"
    for-each: "npm:^0.3.3"
    has-proto: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.10"
  checksum: 6f376bf5d988f00f98ccee41fd551cafc389095a2a307c18fab30f29da7d1464fc3697139cf254cda98b4128bbcb114f4b557bbabdc6d9c2e5039c515b31decf
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.0":
  version: 1.0.0
  resolution: "typed-array-byte-offset@npm:1.0.0"
  dependencies:
    available-typed-arrays: "npm:^1.0.5"
    call-bind: "npm:^1.0.2"
    for-each: "npm:^0.3.3"
    has-proto: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.10"
  checksum: 2d81747faae31ca79f6c597dc18e15ae3d5b7e97f7aaebce3b31f46feeb2a6c1d6c92b9a634d901c83731ffb7ec0b74d05c6ff56076f5ae39db0cd19b16a3f92
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-length@npm:1.0.4"
  dependencies:
    call-bind: "npm:^1.0.2"
    for-each: "npm:^0.3.3"
    is-typed-array: "npm:^1.1.9"
  checksum: 0444658acc110b233176cb0b7689dcb828b0cfa099ab1d377da430e8553b6fdcdce882360b7ffe9ae085b6330e1d39383d7b2c61574d6cd8eef651d3e4a87822
  languageName: node
  linkType: hard

"typescript@npm:^5.2.2":
  version: 5.3.3
  resolution: "typescript@npm:5.3.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 6e4e6a14a50c222b3d14d4ea2f729e79f972fa536ac1522b91202a9a65af3605c2928c4a790a4a50aa13694d461c479ba92cedaeb1e7b190aadaa4e4b96b8e18
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A^5.2.2#optional!builtin<compat/typescript>":
  version: 5.3.3
  resolution: "typescript@patch:typescript@npm%3A5.3.3#optional!builtin<compat/typescript>::version=5.3.3&hash=e012d7"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: c93786fcc9a70718ba1e3819bab56064ead5817004d1b8186f8ca66165f3a2d0100fee91fa64c840dcd45f994ca5d615d8e1f566d39a7470fc1e014dbb4cf15d
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.0.2":
  version: 1.0.2
  resolution: "unbox-primitive@npm:1.0.2"
  dependencies:
    call-bind: "npm:^1.0.2"
    has-bigints: "npm:^1.0.2"
    has-symbols: "npm:^1.0.3"
    which-boxed-primitive: "npm:^1.0.2"
  checksum: 06e1ee41c1095e37281cb71a975cb3350f7cb470a0665d2576f02cc9564f623bd90cfc0183693b8a7fdf2d242963dcc3010b509fa3ac683f540c765c0f3e7e43
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 0097779d94bc0fd26f0418b3a05472410408877279141ded2bd449167be1aed7ea5b76f756562cb3586a07f251b90799bab22d9019ceba49c037c76445f7cddd
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.0"
  checksum: 39be078afd014c14dcd957a7a46a60061bc37c4508ba146517f85f60361acf4c7539552645ece25de840e17e293baa5556268d091ca6762747fdd0c705001a45
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: "npm:^2.0.0"
    unicode-property-aliases-ecmascript: "npm:^2.0.0"
  checksum: 1f34a7434a23df4885b5890ac36c5b2161a809887000be560f56ad4b11126d433c0c1c39baf1016bdabed4ec54829a6190ee37aa24919aa116dc1a5a8a62965a
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.1.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.1.0"
  checksum: 06661bc8aba2a60c7733a7044f3e13085808939ad17924ffd4f5222a650f88009eb7c09481dc9c15cfc593d4ad99bd1cde8d54042733b335672591a81c52601c
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 243524431893649b62cc674d877bd64ef292d6071dd2fd01ab4d5ad26efbc104ffcd064f93f8a06b7e4ec54c172bf03f6417921a0d8c3a9994161fe1f88f815b
  languageName: node
  linkType: hard

"unique-filename@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-filename@npm:3.0.0"
  dependencies:
    unique-slug: "npm:^4.0.0"
  checksum: 8e2f59b356cb2e54aab14ff98a51ac6c45781d15ceaab6d4f1c2228b780193dc70fae4463ce9e1df4479cb9d3304d7c2043a3fb905bdeca71cc7e8ce27e063df
  languageName: node
  linkType: hard

"unique-slug@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-slug@npm:4.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 40912a8963fc02fb8b600cf50197df4a275c602c60de4cac4f75879d3c48558cfac48de08a25cc10df8112161f7180b3bbb4d662aadb711568602f9eddee54f0
  languageName: node
  linkType: hard

"unload@npm:2.2.0":
  version: 2.2.0
  resolution: "unload@npm:2.2.0"
  dependencies:
    "@babel/runtime": "npm:^7.6.2"
    detect-node: "npm:^2.0.4"
  checksum: 382f676f24b774dc84beaf424326a227929ecad0ea0f319d4fd0812376b3306ea6d7ccf7ea85c6663ed7be552e9e004f429146bad8faf976b43084e29e265d10
  languageName: node
  linkType: hard

"untildify@npm:^4.0.0":
  version: 4.0.0
  resolution: "untildify@npm:4.0.0"
  checksum: 39ced9c418a74f73f0a56e1ba4634b4d959422dff61f4c72a8e39f60b99380c1b45ed776fbaa0a4101b157e4310d873ad7d114e8534ca02609b4916bb4187fb9
  languageName: node
  linkType: hard

"unzipper@npm:^0.10.11":
  version: 0.10.14
  resolution: "unzipper@npm:0.10.14"
  dependencies:
    big-integer: "npm:^1.6.17"
    binary: "npm:~0.3.0"
    bluebird: "npm:~3.4.1"
    buffer-indexof-polyfill: "npm:~1.0.0"
    duplexer2: "npm:~0.1.4"
    fstream: "npm:^1.0.12"
    graceful-fs: "npm:^4.2.2"
    listenercount: "npm:~1.0.1"
    readable-stream: "npm:~2.3.6"
    setimmediate: "npm:~1.0.4"
  checksum: 3f7b44f3c7253bc08da2988baf559f00b261c5340625e6e5206c5d73b4dea409b89caae4048346cf9f215d3cdf930e3bdee98edac5e0abc843eed765c52b398d
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.0.13":
  version: 1.0.13
  resolution: "update-browserslist-db@npm:1.0.13"
  dependencies:
    escalade: "npm:^3.1.1"
    picocolors: "npm:^1.0.0"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 9074b4ef34d2ed931f27d390aafdd391ee7c45ad83c508e8fed6aaae1eb68f81999a768ed8525c6f88d4001a4fbf1b8c0268f099d0e8e72088ec5945ac796acf
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: b271ca7e3d46b7160222e3afa3e531505161c9a4e097febae9664e4b59912f4cbe94861361a4175edac3a03fee99d91e44b6a58c17a634bc5a664b19fc76fbcb
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.0.0":
  version: 1.2.0
  resolution: "use-sync-external-store@npm:1.2.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: a676216affc203876bd47981103f201f28c2731361bb186367e12d287a7566763213a8816910c6eb88265eccd4c230426eb783d64c373c4a180905be8820ed8e
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"uuid@npm:^8.3.0":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 9a5f7aa1d6f56dd1e8d5f2478f855f25c645e64e26e347a98e98d95781d5ed20062d6cca2eecb58ba7c84bc3910be95c0451ef4161906abaab44f9cb68ffbdd1
  languageName: node
  linkType: hard

"void-elements@npm:3.1.0":
  version: 3.1.0
  resolution: "void-elements@npm:3.1.0"
  checksum: 0390f818107fa8fce55bb0a5c3f661056001c1d5a2a48c28d582d4d847347c2ab5b7f8272314cac58acf62345126b6b09bea623a185935f6b1c3bbce0dfd7f7f
  languageName: node
  linkType: hard

"watchpack@npm:2.4.0":
  version: 2.4.0
  resolution: "watchpack@npm:2.4.0"
  dependencies:
    glob-to-regexp: "npm:^0.4.1"
    graceful-fs: "npm:^4.1.2"
  checksum: 4280b45bc4b5d45d5579113f2a4af93b67ae1b9607cc3d86ae41cdd53ead10db5d9dc3237f24256d05ef88b28c69a02712f78e434cb7ecc8edaca134a56e8cab
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-boxed-primitive@npm:1.0.2"
  dependencies:
    is-bigint: "npm:^1.0.1"
    is-boolean-object: "npm:^1.1.0"
    is-number-object: "npm:^1.0.4"
    is-string: "npm:^1.0.5"
    is-symbol: "npm:^1.0.3"
  checksum: 9c7ca7855255f25ac47f4ce8b59c4cc33629e713fd7a165c9d77a2bb47bf3d9655a5664660c70337a3221cf96742f3589fae15a3a33639908d33e29aa2941efb
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.1.3":
  version: 1.1.3
  resolution: "which-builtin-type@npm:1.1.3"
  dependencies:
    function.prototype.name: "npm:^1.1.5"
    has-tostringtag: "npm:^1.0.0"
    is-async-function: "npm:^2.0.0"
    is-date-object: "npm:^1.0.5"
    is-finalizationregistry: "npm:^1.0.2"
    is-generator-function: "npm:^1.0.10"
    is-regex: "npm:^1.1.4"
    is-weakref: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    which-boxed-primitive: "npm:^1.0.2"
    which-collection: "npm:^1.0.1"
    which-typed-array: "npm:^1.1.9"
  checksum: d7823c4a6aa4fc8183eb572edd9f9ee2751e5f3ba2ccd5b298cc163f720df0f02ee1a5291d18ca8a41d48144ef40007ff6a64e6f5e7c506527086c7513a5f673
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.1":
  version: 1.0.1
  resolution: "which-collection@npm:1.0.1"
  dependencies:
    is-map: "npm:^2.0.1"
    is-set: "npm:^2.0.1"
    is-weakmap: "npm:^2.0.1"
    is-weakset: "npm:^2.0.1"
  checksum: 85c95fcf92df7972ce66bed879e53d9dc752a30ef08e1ca4696df56bcf1c302e3b9965a39b04a20fa280a997fad6c170eb0b4d62435569b7f6c0bc7be910572b
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.11, which-typed-array@npm:^1.1.13, which-typed-array@npm:^1.1.9":
  version: 1.1.13
  resolution: "which-typed-array@npm:1.1.13"
  dependencies:
    available-typed-arrays: "npm:^1.0.5"
    call-bind: "npm:^1.0.4"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.0"
  checksum: 605e3e10b7118af904a0e79d0d50b95275102f06ec902734024989cd71354929f7acee50de43529d3baf5858e2e4eb32c75e6ebd226c888ad976d8140e4a3e71
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 4782f8a1d6b8fc12c65e968fea49f59752bf6302dc43036c3bf87da718a80710f61a062516e9764c70008b487929a73546125570acea95c5b5dcc8ac3052c70f
  languageName: node
  linkType: hard

"which@npm:^4.0.0":
  version: 4.0.0
  resolution: "which@npm:4.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: f17e84c042592c21e23c8195108cff18c64050b9efb8459589116999ea9da6dd1509e6a1bac3aeebefd137be00fabbb61b5c2bc0aa0f8526f32b58ee2f545651
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: cebdaeca3a6880da410f75209e68cd05428580de5ad24535f22696d7d9cab134d1f8498599f344c3cf0fb37c1715807a183778d8c648d6cc0cb5ff2bb4236540
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 7b1e4b35e9bb2312d2ee9ee7dc95b8cb5f8b4b5a89f7dde5543fe66c1e3715663094defa50d75454ac900bd210f702d575f15f3f17fa9ec0291806d2578d1ddf
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"xmlchars@npm:^2.2.0":
  version: 2.2.0
  resolution: "xmlchars@npm:2.2.0"
  checksum: 4ad5924974efd004a47cce6acf5c0269aee0e62f9a805a426db3337af7bcbd331099df174b024ace4fb18971b8a56de386d2e73a1c4b020e3abd63a4a9b917f1
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 9af0a4329c3c6b779ac4736c69fae4190ac03029fa27c1aef4e6bcc92119b73dea6fe5db5fe881fb0ce2a0e9539a42cdf60c7c21eda04d1a0b8c082e38509efb
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 4cb02b42b8a93b5cf50caf5d8e9beb409400a8a4d85e83bb0685c1457e9ac0b7a00819e9f5991ac25ffabb56a78e2f017c1acc010b3a1babfe6de690ba531abd
  languageName: node
  linkType: hard

"yaml@npm:^1.10.0":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: e088b37b4d4885b70b50c9fa1b7e54bd2e27f5c87205f9deaffd1fb293ab263d9c964feadb9817a7b129a5bf30a06582cb08750f810568ecc14f3cdbabb79cb3
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"yup@npm:^1.3.2":
  version: 1.3.2
  resolution: "yup@npm:1.3.2"
  dependencies:
    property-expr: "npm:^2.0.5"
    tiny-case: "npm:^1.0.3"
    toposort: "npm:^2.0.2"
    type-fest: "npm:^2.19.0"
  checksum: 68ea930cbbb159ecbfd9e6d256d237a9f0f24ca05ee8e92c0a1058eee73988f3fabcd39a05cf2eded4d394d4813206e8deb90caef610feae88abab9801d1d6af
  languageName: node
  linkType: hard

"zip-stream@npm:^4.1.0":
  version: 4.1.1
  resolution: "zip-stream@npm:4.1.1"
  dependencies:
    archiver-utils: "npm:^3.0.4"
    compress-commons: "npm:^4.1.2"
    readable-stream: "npm:^3.6.0"
  checksum: 33bd5ee7017656c2ad728b5d4ba510e15bd65ce1ec180c5bbdc7a5f063256353ec482e6a2bc74de7515219d8494147924b9aae16e63fdaaf37cdf7d1ee8df125
  languageName: node
  linkType: hard
