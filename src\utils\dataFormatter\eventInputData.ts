import dayjs from "dayjs";
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

export type EventInputProps = {
    eventId: string;
    eventName: string;
    eventCategory: string;
    eventDescription: string,
    eventStartDateTime: number;
    eventEndDateTime: number;
    saleStartDateTimeForDisplay: number;
    saleEndDateTimeForDisplay: number;
    isPrivate: string;
    publish: string;
    publishDateTime: number;
    publishDetailsDateTime: number;
    isListDetails: string,
    alertThreshold: number;
    totalSupply: number;
    acceptCurrency: string;
    eventTimeZone: string;
    eventLocation: string;
};

export type EventLocalizableFieldProps = {
    eventId: string;
    eventName: string;
    eventDescription: string;
};

export default (event: EventInputProps) => ({
    fields: {
        eventId: event.eventId,
        isPrivate: (event.isPrivate.toLowerCase() === 'yes') ? 'true' : 'false',
        publish: (event.publish.toLowerCase() === 'yes') ? 'true' : 'false',
        isListDetails: (event.isListDetails.toLowerCase() === 'yes') ? 'true' : 'false',
        eventStartDateTime: dayjs(event.eventStartDateTime).tz(event.eventTimeZone, true).valueOf(),
        eventEndDateTime: dayjs(event.eventEndDateTime).tz(event.eventTimeZone, true).valueOf(),
        saleStartDateTimeForDisplay: dayjs(event.saleStartDateTimeForDisplay).tz(event.eventTimeZone, true).valueOf(),
        saleEndDateTimeForDisplay: dayjs(event.saleEndDateTimeForDisplay).tz(event.eventTimeZone, true).valueOf(),
        publishDateTime: dayjs(event.publishDateTime).tz(event.eventTimeZone, true).valueOf(),
        publishDetailsDateTime: dayjs(event.publishDetailsDateTime).tz(event.eventTimeZone, true).valueOf(),
        alertThreshold: event.alertThreshold,
        totalSupply: event.totalSupply,
        acceptCurrency: event.acceptCurrency,
        eventTimeZone: event.eventTimeZone,
        eventLocation: event.eventLocation,
    },
    localizableFields: {
        eventId: event.eventId,
        eventName: event.eventName,
        eventDescription: event.eventDescription,
    }
}); 