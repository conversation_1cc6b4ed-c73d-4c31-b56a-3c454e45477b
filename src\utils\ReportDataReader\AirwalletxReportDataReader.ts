import { NumberRegex } from "@/constants/common";
import { Buffer } from "buffer";
import ExcelJS from "exceljs";

import ReportDataReader from "./index";

type AirwalletxRawRecord = {
    "Transaction Id": string;
    "Transaction Type": string;
    "Source Id": string;
    "Source Entity": string;
    "Time Zone": string;
    "Created At": string;
    "Settled At": string;
    "Posted At": string;
    "Status": string;
    "Transaction Currency": string;
    "Transaction Amount": number;
    "Exchange Rate": number;
    "Settlement Currency": string;
    "Settlement Amount": number;
    "Net Amount": number;
    "Payment Method Fee": number;
    "Interchange Fee": number;
    "Scheme Fee": number;
    "Payment Attempt Id": string;
    "Payment Id": string;
    "Order Id": string;
    "Request Id": string;
    "Batch Id": string;
    "Payment Method": string;
    "Connected Account Id": string;
    "Account Id": string;
    "Card Brand": string;
    "Tax Amount": string;
}

type AirwalletxRawRecordNewVersion = {
    "Legal entity name": string;
    "Account name": string;
    "Account ID": string;
    "Settlement batch ID": string;
    "Transaction ID": string;
    "Transaction type": string;
    "Source entity": string;
    "Source ID": string;
    "Payment ID": string;
    "Time zone": string;
    "Created at": string;
    "Settled at": string;
    "Transaction currency": string;
    "Transaction amount": number;
    "Exchange rate": number;
    "Settlement currency": string;
    "Gross amount": number;
    "Fees": number;
    "Taxes on fees": number;
    "Net amount": number;
    "Payment attempt ID": string;
    "Payment created time": string;
    "Order ID": string;
    "Request ID": string;
    "Descriptor": string;
    "Payment method": string;
    "Payment method type": string;
    "Card issuing or shopper country": string;
    "Issuing or originating bank": string;
    "Card funding": string;
    "Card category": string;
    "Card transaction region": string;
    "Dispute reason code": string;
    "Dispute reason": string;
    "Dispute status": string;
    "Acquirer reference number": string;
    "Subscription ID": string;
    "Customer ID": string;
    "Merchant customer ID": string;
    "Customer name": string;
    "Customer phone": string;
    "Customer email": string;
    "Payment link reference": string;
    "Shipping name": string;
    "Shipping address": string;
    "Shipping city": string;
    "Shipping state": string;
    "Shipping postal code": string;
    "Shipping country": string;
    "Connected account id": string;
    "Metadata 1": string;
    "Metadata 2": string;
}

const PaymentOrderIdRegex = /[0-9]+_(IC_[\w]+)/g;
class AirwalletxReportDataReader extends ReportDataReader<AirwalletxRawRecordNewVersion> {
    // eslint-disable-next-line class-methods-use-this
    async ExtractDatasetFromContent(dataUriContent: string): Promise<AirwalletxRawRecordNewVersion[]> {
        const base64Data = dataUriContent.split(';base64,').pop();
        if (!base64Data) {
            throw new Error("Unable to find Transaction Details (Format invalid)");
        }
        const buffer = Buffer.from(base64Data, 'base64');
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(buffer);
        const worksheet = workbook.worksheets[0];

        let headerRowNumber: number = -1; // -1
        let datasetRowNumber: number = -1; // -1
        let datasetColumnCount: number = 0; // 0
        for (let i: number = 1; i <= worksheet.rowCount; i += 1) {
            const cellValue = worksheet.getCell(`A${i}`).value;
            // if (cellValue === "Transaction Details") {
            if (cellValue === "Legal entity name") {
                headerRowNumber = i;
                datasetRowNumber = i + 1;
                datasetColumnCount = worksheet.getRow(headerRowNumber).cellCount;
                break;
            }
            if (i === worksheet.rowCount) {
                throw new Error("Unable to find Transaction Details (Format invalid)");
            }
        }
        const rawRecords: AirwalletxRawRecordNewVersion[] = [];
        for (let i = datasetRowNumber; i <= worksheet.rowCount; i++) {
            const rawRecord: { [key in string]: unknown } = {};
            for (let j = 1; j <= datasetColumnCount; j++) {
                const columnName = worksheet.getRow(headerRowNumber!).getCell(j).value!.toString() as keyof AirwalletxRawRecordNewVersion;
                const cellValue = worksheet.getRow(i).getCell(j).value?.toString();
                if (cellValue) {
                    rawRecord[columnName] = NumberRegex.test(cellValue) ? parseFloat(cellValue) : cellValue;
                }
            }
            rawRecords.push(rawRecord as AirwalletxRawRecordNewVersion);
        }
        this.rawRecords = rawRecords;
        return rawRecords;
    }

    GetIds(): string[] {
        return this.rawRecords
            .map(item => new RegExp(PaymentOrderIdRegex).exec(item["Order ID"]))
            .filter(item => item !== null)
            .map(item => item![1]);
    }

    GetRecordById(id: string): AirwalletxRawRecordNewVersion | null | undefined {
        return this.rawRecords
            .find(item => {
                const PaymentOrderId = new RegExp(PaymentOrderIdRegex).exec(item["Order ID"])?.[1];
                if (!PaymentOrderId)
                    return false;

                return id === PaymentOrderId;
            });
    }
}
export default AirwalletxReportDataReader;
