import { useSnackbar } from '@/components/snackbar';
import { EnumPaymentOrderState } from '@/enum/EnumPaymentOrderState';
import EnumRequestHeader from '@/enum/EnumRequestHeader';
import ADMIN_ENDPOINTS from '@/models/api/admin-endpoints';
import API from '@/utils/api';
import UpgradeIcon from '@mui/icons-material/Upgrade';
import Button from '@mui/material/Button';
import { Dayjs } from 'dayjs';
import { useCallback } from 'react';

interface SalesReportQuery {
    event: string,
    from_date: string,
    to_date: string,
    payment_state?: string,
    page?: string,
    page_size?: string,
}

interface ExportButtonProps {
    event: string,
    firstDay?: Dayjs | null,
    lastDay?: Dayjs | null,
    onFinished: Function,
}

const reportDownload = async (query: SalesReportQuery) => {
    const jwt = API.GetUserJWT();
    if (!jwt) {
        throw new Error("EMPTY_JWT");
    }
    const { event, payment_state, ...restQuery } = query;
    const paymentStateArray = payment_state?.trim().replaceAll(" ", "").split(",") ?? [];
    const qs = new URLSearchParams({
        ...(event !== "all" && paymentStateArray.length <= 0 ? query : { ...restQuery })
    });

    if (payment_state) {
        if (event !== "all") qs.append("event", event);
        
        if (paymentStateArray.length > 1) {
            paymentStateArray.map((state, index) => qs.append(`payment_state[${index}]`, state));
        } else if (paymentStateArray.length === 1) {
            qs.append("payment_state", paymentStateArray[0]);
        }
    }
    
    const request = new Request(`${ADMIN_ENDPOINTS.GetSalesReportSummary()}?${qs.toString()}`, {
        method: 'GET',
        headers: new Headers({
            [EnumRequestHeader.AUTHORIZATION]: jwt
        }),
    });
    try {
        const res = await fetch(request);
        const blob = await res.blob();
        return blob;
    } catch {
        throw new Error('Network error');
    }
};

const ExportButton = (props: ExportButtonProps) => {
    const { event, firstDay, lastDay, onFinished } = props;

    const { enqueueSnackbar } = useSnackbar();

    const onExport = useCallback(() => {
        (async () => {
            try {
                const fileBlob = await reportDownload({
                    event,
                    from_date: firstDay!.startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                    to_date: lastDay!.endOf('day').format('YYYY-MM-DD HH:mm:ss'),
                    payment_state: `${EnumPaymentOrderState.PAID}, ${EnumPaymentOrderState.COMPLETED}`,
                    page: "1",
                    page_size: "999999"
                });
                const url = window.URL.createObjectURL(new Blob([fileBlob]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', `Incutix_Sales_Summary_Report_${Date.now()}.xlsx`);
                document.body.appendChild(link);
                link.click();
                enqueueSnackbar("Start downloading", { variant: "success" });
                onFinished();
            } catch (error: unknown) {
                console.warn(error);
                enqueueSnackbar("Download Failed, please ensure your credential is valid.", { variant: "error" });
            }
        })();
    }, [event, firstDay, lastDay, enqueueSnackbar, onFinished]);

    return (
        <Button variant="contained" endIcon={<UpgradeIcon />} onClick={onExport}>Export</Button>
    )
};

export default ExportButton;