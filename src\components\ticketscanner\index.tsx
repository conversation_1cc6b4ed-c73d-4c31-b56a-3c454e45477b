import useGetTicketInfo from '@/hooks/api/use-get-ticket-info';
import { useBoolean } from '@/hooks/utils/use-boolean';
import { TicketInfo } from '@/models/api/entity/TicketInfo';
import CardGiftcardIcon from '@mui/icons-material/CardGiftcard';
import CloseIcon from '@mui/icons-material/Close';
import DangerousIcon from '@mui/icons-material/Dangerous';
import DoneIcon from '@mui/icons-material/Done';
import DoorSlidingIcon from '@mui/icons-material/DoorSliding';
import MeetingRoomIcon from '@mui/icons-material/MeetingRoom';
import NextPlanIcon from '@mui/icons-material/NextPlan';
import NoMeetingRoomIcon from '@mui/icons-material/NoMeetingRoom';
import QrCodeScannerIcon from '@mui/icons-material/QrCodeScanner';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import { Box, Button, Card, CardActions, CardContent, CardHeader, Chip, Stack, styled, TextField } from '@mui/material';
import { QrScanner } from '@yudiel/react-qr-scanner';
import * as dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { useSnackbar } from 'src/components/snackbar';
import { EnumTicketAction } from 'src/enum/EnumTicketAction';
import { EnumTicketActionPermission } from 'src/enum/EnumTicketActionPermission';

import { MotionContainer } from '../animate';
import ManualInputDialog from './manual-input-dialog';

dayjs.extend(utc);
dayjs.extend(timezone);

// Constants outside component to avoid recreation on every render
const SCAN_THROTTLE_MS = 330; // 0.33 seconds gap between scans

interface TicketScannerProps {
    Actions?: React.JSX.ElementType;
    permission: EnumTicketActionPermission;
}

const ManualInputBox = styled(MotionContainer)(({ theme }) => ({
    position: "absolute",
    bottom: theme.spacing(3),
    left: 0,
    right: 0,
    display: "flex",
    justifyContent: 'center',
    alignItems: "center",
    zIndex: 1,
}));
export const TicketScanner = (props: TicketScannerProps) => {
    const [showScanner, setShowScanner] = useState(false);
    const { enqueueSnackbar } = useSnackbar();

    const { Actions, permission } = props;
    const [qrCodeToken, setQrCodeToken] = useState("");
    const [scanSuccess, setScanSuccess] = useState(false);
    const [ticketInfo, setTicketInfo] = useState<TicketInfo>();
    const snackbarDuration = 1000 * 2;

    // Add ref to track last scan time for throttling
    const lastScanTimeRef = useRef<number>(0);
    const scanThrottleMs = 330; // 0.33 seconds gap between scans

    const manualInputDialog = useBoolean();

    const ticketPermissions = useMemo(() => [
        'ticket:admitted',
        'ticket:reward'
    ], []);

    const fetchTicketInfoAsync = useGetTicketInfo();

    const resetScanner = useCallback(() => {
        setScanSuccess(false);
        setTicketInfo(undefined);
        lastScanTimeRef.current = 0; // Reset throttle timer
    }, []);

    const onScannerDecode = useCallback(async (result: string) => {
        // Throttle scanning to prevent high frequency API requests
        const currentTime = Date.now();
        if (currentTime - lastScanTimeRef.current < scanThrottleMs) {
            return; // Skip this scan if within throttle period
        }
        lastScanTimeRef.current = currentTime;

        setQrCodeToken(result);
        try {
            const ticket = (await fetchTicketInfoAsync({ qrCodeToken: result })).data!;
            if (ticket.ticketEventDisplayInfo != null) {
                setTicketInfo(ticket.ticketEventDisplayInfo);
            }
            setScanSuccess(true);
            enqueueSnackbar("Valid Ticket", { variant: "success", preventDuplicate: true, autoHideDuration: snackbarDuration });
        } catch (error) {
            enqueueSnackbar("Invalid Ticket", { variant: "error", preventDuplicate: true, autoHideDuration: snackbarDuration });
            resetScanner();
        }
    }, [enqueueSnackbar, fetchTicketInfoAsync, resetScanner, snackbarDuration]);

    const onScannerError = useCallback((error: any) => {
        enqueueSnackbar(error.message, { variant: 'error', preventDuplicate: true, autoHideDuration: snackbarDuration });
        setScanSuccess(false);
    }, [enqueueSnackbar, snackbarDuration]);


    const onManualInputConfirmed = useCallback((token?: string) => {
        onScannerDecode(token ?? "");
    }, [onScannerDecode]);

    const eventStartDateTime = useMemo(() => {
        if (ticketInfo?.eventEndDateTime !== '') return dayjs.tz(ticketInfo?.eventStartDateTime, ticketInfo?.timeZone).format('YYYY-MM-DD HH:mm:ss');
        return '';
    }, [ticketInfo]);

    const eventEndDateTime = useMemo(() => {
        if (ticketInfo?.eventEndDateTime !== '') return dayjs.tz(ticketInfo?.eventEndDateTime, ticketInfo?.timeZone).format('YYYY-MM-DD HH:mm:ss');
        return '';
    }, [ticketInfo]);

    const sessionStartDateTime = useMemo(() => {
        if (ticketInfo?.sessionStartDateTime !== '') return dayjs.tz(ticketInfo?.sessionStartDateTime, ticketInfo?.timeZone).format('YYYY-MM-DD HH:mm:ss');
        return '';
    }, [ticketInfo]);

    const sessionEndDateTime = useMemo(() => {
        if (ticketInfo?.sessionEndDateTime !== '') return dayjs.tz(ticketInfo?.sessionEndDateTime, ticketInfo?.timeZone).format('YYYY-MM-DD HH:mm:ss');
        return '';
    }, [ticketInfo]);

    const getActionIcon = useCallback((actionName?: string, permissionName?: string) => {
        const defaultSx = { width: "2rem", height: "2rem" };
        if (actionName === EnumTicketAction.ALLOW) {
            const sx = { ...defaultSx, color: "green" };
            switch (permissionName) {
                case EnumTicketActionPermission.ADMITTED:
                    return <MeetingRoomIcon sx={sx} />;
                case EnumTicketActionPermission.REWARD:
                    return <VerifiedUserIcon sx={sx} />;
                default:
                    return <DoneIcon sx={sx} />;
            }
        }
        if (actionName === EnumTicketAction.DENY) {
            const sx = { ...defaultSx, color: "red" };
            switch (permissionName) {
                case EnumTicketActionPermission.ADMITTED:
                    return <NoMeetingRoomIcon sx={sx} />;
                case EnumTicketActionPermission.REWARD:
                    return <DangerousIcon sx={sx} />;
                default:
                    return <CloseIcon sx={sx} />;
            }
        }
        switch (permissionName) {
            case EnumTicketActionPermission.ADMITTED:
                return <DoorSlidingIcon sx={defaultSx} />;
            case EnumTicketActionPermission.REWARD:
                return <CardGiftcardIcon sx={defaultSx} />;
            default:
                return null;
        }
    }, []);
    const scannedTicketPermissionStatus = useMemo(() => ticketPermissions.map(ticketPermission => {
        // hotfix: 2024-01-20 : nick: manually modifity '_' -> ':'
        const activity = ticketInfo?.itemActivities.find(t => t.itemPermissionName.replaceAll("_", ":") === ticketPermission);
        const ActionIcon = getActionIcon(activity?.itemActionName, activity?.itemPermissionName);
        return (
            <Box key={ticketPermission}>
                <Stack direction="row" gap={0} sx={{ fontSize: "1.5rem", fontWeight: 600 }}>
                    <div style={{ width: "80%" }}>{ticketPermission}</div>
                    <div style={{ width: "10%" }}>{activity?.execuateTimes ?? '-'}</div>
                    <div style={{ width: "10%" }}>
                        {ActionIcon}
                    </div>
                </Stack>
            </Box>
        )
    }), [getActionIcon, ticketInfo?.itemActivities, ticketPermissions]);

    const onClickManualInput = useCallback(() => {
        manualInputDialog.onTrue();
    }, [manualInputDialog]);
    const Content = useMemo(() => {
        if (!showScanner) {
            return null;
        }
        if (scanSuccess) {
            return (
                <Card>
                    <CardHeader
                        title={ticketInfo?.eventBundleName}
                        subheader={ticketInfo?.eventName}
                    />
                    <CardContent>
                        <Stack gap={2}>
                            <TextField
                                disabled
                                label="Event Start DateTime (Local Time)"
                                defaultValue={eventStartDateTime}
                                variant="outlined"
                            />
                            <TextField
                                disabled
                                label="Event End DateTime (Local Time)"
                                defaultValue={eventEndDateTime}
                                variant="outlined"
                            />
                            <TextField
                                disabled
                                label="Timeslot Start DateTime (Local Time)"
                                defaultValue={sessionStartDateTime}
                                variant="outlined"
                            />
                            <TextField
                                disabled
                                label="Timeslot End DateTime (Local Time)"
                                defaultValue={sessionEndDateTime}
                                variant="outlined"
                            />
                            <Stack direction="column" spacing={0}>
                                {scannedTicketPermissionStatus}
                            </Stack>
                        </Stack>
                    </CardContent>
                    <CardActions sx={{
                        justifyContent: "center",
                        justifyItems: "center"
                    }}>
                        <Stack spacing={{ xs: 1, sm: 2 }} direction="column" justifyContent="center" justifyItems="center">
                            {Actions && <Actions permission={permission} qrCodeToken={qrCodeToken} onAfterAction={resetScanner} />}
                            { }
                            <Button size="large" color="primary" variant="contained" sx={{ height: '20vh', }} endIcon={<NextPlanIcon />} onClick={resetScanner}>Scan next one</Button>
                        </Stack>
                    </CardActions>
                </Card>
            );
        }
        if (ticketInfo) {
            return null;
        }
        return (
            <Card sx={{
                minWidth: 300,
                width: "100%",
                maxWidth: "70vh"
            }}>
                <QrScanner
                    onError={onScannerError}
                    onDecode={onScannerDecode}
                />
                <ManualInputBox>
                    <Chip label="Manual Input" onClick={onClickManualInput} />
                </ManualInputBox>
            </Card>
        );
    }, [Actions, eventEndDateTime, eventStartDateTime, onClickManualInput, onScannerDecode, onScannerError, permission, qrCodeToken, resetScanner, scanSuccess, scannedTicketPermissionStatus, sessionEndDateTime, sessionStartDateTime, showScanner, ticketInfo]);

    const startScanner = useCallback(() => {
        setShowScanner(true);
    }, []);
    const stopScanner = useCallback(() => {
        resetScanner();
        setShowScanner(false);
    }, [resetScanner]);
    if (!showScanner) {
        return (
            <Button variant="outlined" size="large" sx={{ height: '80svh', width: '100%' }} startIcon={<QrCodeScannerIcon />} onClick={startScanner}>
                Start Scan
            </Button>
        );
    }
    return (
        <>
            <Box display="flex" alignItems="center" flexDirection="column" gap={2}>
                {Content}
                <Button variant="outlined" size="large" sx={{ height: '10vh', width: '100%', maxWidth: '70vh' }} onClick={stopScanner}>
                    Stop Scan
                </Button>
            </Box>
            <ManualInputDialog state={manualInputDialog} onConfirm={onManualInputConfirmed} />
        </>
    )
};

