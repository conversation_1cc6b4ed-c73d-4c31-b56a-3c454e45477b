'use client';


import { useState, useCallback } from 'react';
import { Container, Typography, Tab, Tabs } from '@mui/material';
import { useSettingsContext } from 'src/components/settings';
import { useSearchParams } from 'next/navigation'

import SessionTableView from './table/session'


type Props = {
    id: string;
};

export default function EventView({ id }: Props) {
    const settings = useSettingsContext();
    const [currentTab, setCurrentTab] = useState('session');
    const searchParams = useSearchParams()
    const name = searchParams.get('name')
    const TABS = [
        {
            value: 'session',
            label: 'Session',
        },
        {
            value: 'payment',
            label: 'Payment Method',
        },
        {
            value: 'tickettype',
            label: 'Ticket Type',
        },
        {
            value: 'media',
            label: 'Media',
        }
    ];
    const handleChangeTab = useCallback((event: React.SyntheticEvent, newValue: string) => {
        setCurrentTab(newValue);
    }, []);

    const renderTabContent = () => {
        switch (currentTab) {
            case 'session':
                return <SessionTableView id={id} eventName={name} />;
            case 'payment':
                return <SessionTableView id='CFDA53ADDE1A4999975F0594251CAF0A' eventName={name} />;
            // case 'tickettype':
            //     return <TicketTypeView />;
            // case 'media':
            //     return <MediaView />;
            default:
                return null;
        }
    };

    return (
        <Container maxWidth={settings.themeStretch ? false : 'xl'}>
            <Typography variant="h4"> Events : {name} </Typography>
            <Tabs value={currentTab} onChange={handleChangeTab}>
                {TABS.map((tab) => (
                    <Tab key={tab.value} value={tab.value} label={tab.label} />
                ))}
            </Tabs>
            {renderTabContent()}
        </Container>
    );
}
