import DateRangeIcon from '@mui/icons-material/DateRange';
import { Box, Button, Card, CardActions, CardContent, IconButton, InputAdornment, MenuItem, MenuList, Paper, Stack, TextField } from '@mui/material';
import { DateCalendar } from "@mui/x-date-pickers";
import { PickersDay, PickersDayProps } from '@mui/x-date-pickers/PickersDay';
import dayjs, { Dayjs } from 'dayjs';
import React, { useCallback, useMemo, useState } from 'react';

import DateRangePickerDay from './date-range-picker-day';

const Day = (props: PickersDayProps<Dayjs> & { selectedFirstDay?: Dayjs | null, selectedLastDay?: Dayjs | null }) => {
    const { day, selectedFirstDay, selectedLastDay, ...other } = props;

    if (selectedFirstDay == null) {
        return <PickersDay day={day} {...other} />;
    }

    const start = selectedFirstDay;
    const end = selectedLastDay ?? selectedFirstDay;

    const dayIsBetween = day.isBetween(start, end, null, '[]');
    const isFirstDay = day.isSame(start, 'day');
    const isLastDay = day.isSame(end, 'day');

    return (
        <DateRangePickerDay
            {...other}
            day={day}
            sx={dayIsBetween ? { px: 2.5, mx: 0 } : {}}
            dayIsBetween={dayIsBetween}
            isFirstDay={isFirstDay}
            isLastDay={isLastDay}
            selected={false}
        />
    );
};

interface DateRangePickerProps {
    alwaysDisplay?: boolean,
    hiddenTextField?: boolean,
    exportComponent?: React.JSX.Element | null,
    onChange?: Function,
}

const DateRangePicker = (props: DateRangePickerProps) => {
    const { alwaysDisplay, hiddenTextField, exportComponent, onChange: onChangeHandler } = props;

    const [displayCalendar, setDisplayCalendar] = useState<boolean>(alwaysDisplay ?? false);
    const [value, setValue] = useState<{ firstDay?: Dayjs | null, lastDay?: Dayjs | null }>();

    const pickerLabelDisplay = useMemo(() => {
        if (!value?.firstDay && !value?.lastDay) {
            return "Select Date";
        }
        if (value?.firstDay && !value?.lastDay) {
            return `${value?.firstDay.format("YYYY/MM/DD")} - `;
        }
        return `${value?.firstDay?.format("YYYY/MM/DD")} - ${value?.lastDay?.format("YYYY/MM/DD")}`;
    }, [value]);

    const childrenWithProps = exportComponent ? React.cloneElement(exportComponent as React.ReactElement<any>, {
        ...exportComponent?.props,
        ...value
    }) : null;

    const onDateRangeAdornmentClick = useCallback(() => setDisplayCalendar(!displayCalendar), [displayCalendar]);

    const onChangeDateRangePicker = useCallback((pickedDate: dayjs.Dayjs | null) => {
        setValue((prevState) => {
            let _state = {
                ...prevState
            };
            if (
                (!value?.firstDay && !value?.lastDay) ||
                (value?.firstDay! && value?.lastDay!)
            ) {
                _state = { firstDay: pickedDate, lastDay: null };
            } else if (value.firstDay! && !value?.lastDay) {
                if (value.firstDay.isAfter(pickedDate)) {
                    _state = { ...prevState, firstDay: pickedDate, lastDay: prevState?.firstDay };
                }
                else {
                    _state = { ...prevState, lastDay: pickedDate };
                }
            }
            if (typeof onChangeHandler === 'function') onChangeHandler(_state);
            return _state;
        });
    }, [onChangeHandler, value]);

    const resetDateRangePicker = useCallback(() => {
        const _state = { firstDay: null, lastDay: null };
        if (typeof onChangeHandler === 'function') onChangeHandler(_state);
        setValue(_state);
    }, [onChangeHandler]);

    const quickSelectForYesterday = useCallback(() => {
        const today = dayjs().endOf('day');
        const yesterday = today.subtract(1, 'day').startOf('day');
        const _state = { firstDay: yesterday, lastDay: today };
        if (typeof onChangeHandler === 'function') onChangeHandler(_state);
        setValue(_state);
    }, [onChangeHandler]);

    const quickSelectForToday = useCallback(() => {
        const today = dayjs();
        const _state = { firstDay: today.startOf('day'), lastDay: today.endOf('day') };
        if (typeof onChangeHandler === 'function') onChangeHandler(_state);
        setValue(_state);
    }, [onChangeHandler]);

    const quickSelectForThisWeek = useCallback(() => {
        const today = dayjs();
        const _state = { firstDay: today.startOf('week'), lastDay: today.endOf('week') };
        if (typeof onChangeHandler === 'function') onChangeHandler(_state);
        setValue(_state);
    }, [onChangeHandler]);

    const quickSelectForLastWeek = useCallback(() => {
        const today = dayjs().subtract(1, 'week');
        const _state = { firstDay: today.startOf('week'), lastDay: today.endOf('week') };
        if (typeof onChangeHandler === 'function') onChangeHandler(_state);
        setValue(_state);
    }, [onChangeHandler]);

    const quickSelectForThisMonth = useCallback(() => {
        const today = dayjs();
        const _state = { firstDay: today.startOf('month'), lastDay: today.endOf('month') };
        if (typeof onChangeHandler === 'function') onChangeHandler(_state);
        setValue(_state);
    }, [onChangeHandler]);

    const quickSelectForLastMonth = useCallback(() => {
        const today = dayjs().subtract(1, 'month');
        const _state = { firstDay: today.startOf('month'), lastDay: today.endOf('month') };
        if (typeof onChangeHandler === 'function') onChangeHandler(_state);
        setValue(_state);
    }, [onChangeHandler]);

    return (
        <Box>
            <Stack
                direction="column"
                spacing={2}
            >
                {!hiddenTextField &&
                    <TextField
                        label={pickerLabelDisplay}
                        placeholder="YYYY/MM/DD"
                        InputProps={{
                            endAdornment: (
                                <InputAdornment position="end">
                                    <IconButton onClick={onDateRangeAdornmentClick}>
                                        <DateRangeIcon />
                                    </IconButton>
                                </InputAdornment>
                            ),
                        }}
                        disabled
                    />
                }
                {displayCalendar &&
                    <Stack
                        direction="row"
                        spacing={{ xs: 0, md: 2 }}
                        flexWrap="wrap"
                    >
                        <Card
                            sx={{
                                width: {
                                    xs: '100%',
                                    md: '50vw'
                                }
                            }}
                        >
                            <DateCalendar
                                onChange={onChangeDateRangePicker}
                                slots={{ day: Day }}
                                slotProps={{
                                    day: {
                                        selectedFirstDay: value?.firstDay,
                                        selectedLastDay: value?.lastDay
                                    } as any,
                                }}
                            />
                            {(value?.firstDay || value?.lastDay) &&
                                <CardContent>
                                    <>
                                        {value?.firstDay ? value.firstDay.format("YYYY-MM-DD") : ""}
                                        {value?.firstDay && value?.lastDay && " ~ "}
                                        {value?.lastDay ? value.lastDay.format("YYYY-MM-DD") : ""}
                                    </>
                                </CardContent>
                            }
                            <CardActions>
                                <Button color="secondary" onClick={resetDateRangePicker}>Reset</Button>
                            </CardActions>
                        </Card>
                        <Paper
                            sx={{
                                width: {
                                    xs: '100%',
                                    md: '20vw'
                                }
                            }}
                        >
                            <MenuList>
                                <MenuItem disabled divider>Quick Selection</MenuItem>
                                <MenuItem onClick={quickSelectForToday}>Today</MenuItem>
                                <MenuItem onClick={quickSelectForYesterday}>Yesterday</MenuItem>
                                <MenuItem onClick={quickSelectForThisWeek}>This week</MenuItem>
                                <MenuItem onClick={quickSelectForLastWeek}>Last week</MenuItem>
                                <MenuItem onClick={quickSelectForThisMonth}>This month</MenuItem>
                                <MenuItem onClick={quickSelectForLastMonth}>Last month</MenuItem>
                            </MenuList>
                        </Paper>
                        <Box>{childrenWithProps}</Box>
                    </Stack>
                }
            </Stack>
        </Box>
    )
};

export default DateRangePicker;