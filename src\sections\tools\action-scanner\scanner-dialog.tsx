import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import NextPlanIcon from '@mui/icons-material/NextPlan';

import { useBoolean } from 'src/hooks/utils/use-boolean';

type ScannerDialogProps = {
    state: ReturnType<typeof useBoolean>
    message: string;
    onScanNext: () => void;
}
export default function ScannerDialog(props: ScannerDialogProps) {
    const { state, message, onScanNext } = props;
    return (
        <Dialog open={state.value} onClose={state.onFalse}>
            <DialogTitle>Insert Ticket Action Result</DialogTitle>
            <DialogContent sx={{ color: 'text.secondary' }}>
                {message}
            </DialogContent>

            <DialogActions>
                <Button startIcon={<NextPlanIcon />} variant="outlined" onClick={onScanNext}>
                    Scan Next
                </Button>
            </DialogActions>
        </Dialog>
    );
}
