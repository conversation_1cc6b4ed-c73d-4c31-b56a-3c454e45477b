'use client';

import API from '@/utils/api';
import BA<PERSON><PERSON><PERSON> from '@/constants/config/backend';
import EnumCookieKey from '@/enum/EnumCookieKey';
import { OptionsType } from 'cookies-next/lib/types';
import { setCookie, deleteCookie } from 'cookies-next';
import EnumRequestHeader from '@/enum/EnumRequestHeader';
import UserInfoAPIResult from '@/models/api/result/user/info';
import LogoutAPIResult from '@/models/api/result/user/logout';
import UserLoginAPIResult from '@/models/api/result/user/login';
import END_USER_ENDPOINTS from '@/models/api/end-user-endpoints';
import { useMemo, useEffect, useReducer, useCallback } from 'react';

import axios, { endpoints } from 'src/utils/axios';

import { isValidToken } from './utils';
import { AuthContext } from './auth-context';
import { AuthUserType, ActionMapType, AuthStateType } from '../../types';

// ----------------------------------------------------------------------

// NOTE:
// We only build demo at basic level.
// Customer will need to do some extra handling yourself if you want to extend the logic and other features...

// ----------------------------------------------------------------------

enum Types {
    INITIAL = 'INITIAL',
    LOGIN = 'LOGIN',
    REGISTER = 'REGISTER',
    LOGOUT = 'LOGOUT',
}

type Payload = {
    [Types.INITIAL]: {
        user: AuthUserType;
    };
    [Types.LOGIN]: {
        user: AuthUserType;
    };
    [Types.REGISTER]: {
        user: AuthUserType;
    };
    [Types.LOGOUT]: undefined;
};

type ActionsType = ActionMapType<Payload>[keyof ActionMapType<Payload>];

// ----------------------------------------------------------------------

const initialState: AuthStateType = {
    user: null,
    loading: true,
};

const reducer = (state: AuthStateType, action: ActionsType) => {
    if (action.type === Types.INITIAL) {
        return {
            loading: false,
            user: action.payload.user,
        };
    }
    if (action.type === Types.LOGIN) {
        return {
            ...state,
            user: action.payload.user,
        };
    }
    if (action.type === Types.REGISTER) {
        return {
            ...state,
            user: action.payload.user,
        };
    }
    if (action.type === Types.LOGOUT) {
        return {
            ...state,
            user: null,
        };
    }
    return state;
};

// ----------------------------------------------------------------------

const STORAGE_KEY = EnumCookieKey.USER_JWT;

type Props = {
    children: React.ReactNode;
};

export function AuthProvider({ children }: Props) {
    const [state, dispatch] = useReducer(reducer, initialState);

    const getUserInfo = useCallback(() =>
        BACKEND.Gateway.fetchQuery<UserInfoAPIResult>({
            url: END_USER_ENDPOINTS.GetUserInfo(),
            params: {
                queryKey: "profile",
            },
        })
        , []);
    const initialize = useCallback(async () => {
        try {
            const accessToken = API.GetUserJWT();

            if (accessToken && isValidToken(accessToken)) {
                const user = ((await getUserInfo()).data!);
                dispatch({
                    type: Types.INITIAL,
                    payload: {
                        user: {
                            ...user ?? {},
                            accessToken,
                        },
                    },
                });
            } else {
                dispatch({
                    type: Types.INITIAL,
                    payload: {
                        user: null,
                    },
                });
            }
        } catch (error) {
            console.error(error);
            dispatch({
                type: Types.INITIAL,
                payload: {
                    user: null,
                },
            });
        }
    }, [getUserInfo]);

    useEffect(() => {
        initialize();
    }, [initialize]);

    // LOGIN
    const login = useCallback(async (email: string, password: string) => {
        const fetchAsync = BACKEND.Gateway.useSubmit();
        const formData = new FormData();
        formData.append("email", email);
        formData.append("recaptcha", "123");
        formData.append("password", password);

        const res = await fetchAsync<UserLoginAPIResult>(
            END_USER_ENDPOINTS.Login(),
            {
                method: "POST",
                data: formData
            }
        );
        const { userJWT, tokenExpiryDateTime } = res.data!;
        const cookieOptions: OptionsType = {
            maxAge: Math.floor((Number(tokenExpiryDateTime) - Date.now()) / 1000),
            sameSite: "lax",
        };
        setCookie(EnumCookieKey.USER_JWT, userJWT, cookieOptions);

        const user = (await getUserInfo()).data!;
        dispatch({
            type: Types.LOGIN,
            payload: {
                user: {
                    ...user
                }
            },
        });
    }, [getUserInfo]);

    // REGISTER
    const register = useCallback(
        async (email: string, password: string, firstName: string, lastName: string) => {
            const data = {
                email,
                password,
                firstName,
                lastName,
            };

            const res = await axios.post(endpoints.auth.register, data);

            const { accessToken, user } = res.data;

            sessionStorage.setItem(STORAGE_KEY, accessToken);

            dispatch({
                type: Types.REGISTER,
                payload: {
                    user: {
                        ...user,
                        accessToken,
                    },
                },
            });
        },
        []
    );
    // LOGOUT
    const logout = useCallback(async () => {
        const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
        await fetchAsync<LogoutAPIResult>(
            END_USER_ENDPOINTS.Logout(),
            {
                method: "POST"
            }
        );
        deleteCookie(STORAGE_KEY);
        dispatch({
            type: Types.LOGOUT,
        });
    }, []);

    // ----------------------------------------------------------------------

    const checkAuthenticated = state.user ? 'authenticated' : 'unauthenticated';

    const status = state.loading ? 'loading' : checkAuthenticated;

    const memoizedValue = useMemo(
        () => ({
            user: state.user,
            method: 'jwt',
            loading: status === 'loading',
            authenticated: status === 'authenticated',
            unauthenticated: status === 'unauthenticated',
            //
            login,
            register,
            logout,
        }),
        [login, logout, register, state.user, status]
    );

    return <AuthContext.Provider value={memoizedValue}>{children}</AuthContext.Provider>;
}
