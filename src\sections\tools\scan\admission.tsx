'use client';

import { TicketScanner } from '@/components/ticketscanner';
import { EnumTicketActionPermission } from '@/enum/EnumTicketActionPermission';

import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';

import { useSettingsContext } from 'src/components/settings';

import ScannerActions from '../action-scanner/scanner-actions';

export default function AdmissionScannerView() {
    const settings = useSettingsContext();
    return (
        <Container maxWidth={settings.themeStretch ? false : 'xl'}>
            <Typography variant="h4"> Admission QR Code Scanner </Typography>
            <TicketScanner permission={EnumTicketActionPermission.TICKET_ADMITTED} Actions={ScannerActions} />
        </Container>
    );
}
