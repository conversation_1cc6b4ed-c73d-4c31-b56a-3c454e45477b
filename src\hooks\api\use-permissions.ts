import BACKEND from "@/constants/config/backend";
import END_USER_ENDPOINTS from "@/models/api/end-user-endpoints";
import PermissionsAPIResult from "@/models/api/result/user/permission";

const usePermissions = () => BACKEND.Gateway.useQuery<PermissionsAPIResult>({
    url: END_USER_ENDPOINTS.GetUserInfo(),
    params: {
        queryKey: `userinfo`
    },
    refetchInterval: 5000,
    // cacheTime: 0,
    staleTime: 0
});
export default usePermissions;