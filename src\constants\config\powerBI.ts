import { Map } from "immutable";
import EnumReportType from "@/enum/EnumReportType";
import { AppConfig, IAppConfig, PowerBIReport } from "@/constants/config";

class PowerBI {
    static get WorkspaceId() {
        return AppConfig.Get<IAppConfig["powerbi"]["workspaceId"]>("powerbi.workspaceId");
    }

    static get Reports() {
        return AppConfig.Get<Map<EnumReportType, PowerBIReport>>("powerbi.reports").toJS();
    }
}

export default PowerBI;