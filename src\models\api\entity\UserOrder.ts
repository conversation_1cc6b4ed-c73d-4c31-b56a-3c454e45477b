interface UserOrderItem {
    eventId: string;
    eventName: string;
    eventBundleId: string;
    quantity: number;
    pricePerUnit: number;
    totalFee: number;
    eventBundleName: string;
}

interface UserOrder {
    paymentInfo: {
        paymentOrderId: string;
        paymentGatewayId: string;
        paymentGatewayOrderId: string;
        paymentType: string;
        internalPaymentOrderState: string;
        userId: string;
        currency: string;
        orderTotalFee: number;
        createdDateTime: number;
        lastModifiedDateTime: number;
    };
    itemList: UserOrderItem[];
}
export default UserOrder;