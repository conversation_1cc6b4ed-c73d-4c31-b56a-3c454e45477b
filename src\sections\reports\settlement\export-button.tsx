import { useSnackbar } from '@/components/snackbar';
import ExcelUtils from '@/utils/excel';
import AirwalletxReportDataReader from '@/utils/ReportDataReader/AirwalletxReportDataReader';
import IncutixSalesDetailReportDataReader from '@/utils/ReportDataReader/IncutixSalesDetailReportDataReader';
import LabPayReportDataReader from '@/utils/ReportDataReader/LabPayReportNormalizer';
import UpgradeIcon from '@mui/icons-material/Upgrade';
import Button from '@mui/material/Button';
import dayjs from 'dayjs';
import ExcelJS from 'exceljs';
import { useCallback } from 'react';

interface ExportButtonProps {
    airwalletxDataReader: AirwalletxReportDataReader,
    labpayDataReader: LabPayReportDataReader,
    incutixSalesDetailDataReader: IncutixSalesDetailReportDataReader
    onFinished: Function,
}

const ExportButton = (props: ExportButtonProps) => {
    const { airwalletxDataReader, labpayDataReader, incutixSalesDetailDataReader, onFinished } = props;
    const { enqueueSnackbar } = useSnackbar();
    const onExport = useCallback(() => {
        (async () => {

            // Create Overview
            const workbook = new ExcelJS.Workbook();
            const worksheet_overview = workbook.addWorksheet('Overview');

            // Create Label
            const BasicColumn = ["PaymentOrderId", "Found at Airwalletx ?", "Found at LabPay ?", "Found at Incutix ?"];
            const BasicColumnIndex = { start: "A", end: "D" };
            worksheet_overview.mergeCells(`${BasicColumnIndex.start}1:${BasicColumnIndex.end}1`);
            const BasicLabelColumn = worksheet_overview.getCell(`${BasicColumnIndex.start}1`);
            BasicLabelColumn.value = "General Data";
            BasicLabelColumn.font = { bold: true, size: 16, underline: true }
            BasicLabelColumn.alignment = { horizontal: "center" };

            // const AirwalletxColumn = ["Transaction Id", "Transaction Type", "Source Id", "Source Entity", "Time Zone", "Created At", "Settled At", "Posted At", "Status", "Transaction Currency", "Transaction Amount", "Exchange Rate", "Settlement Currency", "Settlement Amount", "Net Amount", "Payment Method Fee", "Interchange Fee", "Scheme Fee", "Payment Attempt Id", "Payment Id", "Order Id", "Request Id", "Batch Id", "Payment Method", "Connected Account Id", "Account Id", "Card Brand", "Tax Amount"];
            const AirwalletxColumn = ["Transaction ID", "Transaction type", "Source ID", "Source entity", "Time zone", "Created at", "Settled at", "Transaction currency", "Transaction amount", "Exchange rate", "Settlement currency", "Gross amount", "Net amount", "Payment attempt ID", "Payment ID", "Order ID", "Request ID", "Settlement batch ID", "Payment method", "Connected account id", "Account ID", "Taxes on fees"];
            const AirwalletxColumnIndex = { start: "E", end: "AA" };
            worksheet_overview.mergeCells(`${AirwalletxColumnIndex.start}1:${AirwalletxColumnIndex.end}1`);
            const AirwalletxDataLabelColumn = worksheet_overview.getCell(`${AirwalletxColumnIndex.start}1`);
            AirwalletxDataLabelColumn.value = "Airwalletx Data";
            AirwalletxDataLabelColumn.font = { bold: true, size: 16, underline: true }
            AirwalletxDataLabelColumn.alignment = { horizontal: "center" };

            const LabpayColumn = ["Platform Txn Ref", "Outlet", "Device Sequence #", "Currency", "Cash Amount", "Amount", "Refunded Amount", "Settlement Amount", "Settlement Date", "Platform", "Payment Type", "Status", "Txn (DD/MM/YYYY)"];
            const LabpayColumnIndex = { start: "AB", end: "AN" };
            worksheet_overview.mergeCells(`${LabpayColumnIndex.start}1:${LabpayColumnIndex.end}1`);
            const LabpayDataLabelColumn = worksheet_overview.getCell(`${LabpayColumnIndex.start}1`);
            LabpayDataLabelColumn.value = "LabPay Data";
            LabpayDataLabelColumn.font = { bold: true, size: 16, underline: true }
            LabpayDataLabelColumn.alignment = { horizontal: "center" };

            const IncutixColumn = ["PaymentGatewayOrderId", "DateTime", "Email", "Nickname", "Currency", "Items", "EventName", "PaymentState", "PromotionCode"];
            const IncutixColumnIndex = { start: "AO", end: "AW" };
            worksheet_overview.mergeCells(`${IncutixColumnIndex.start}1:${IncutixColumnIndex.end}1`);
            const IncutixDataLabelColumn = worksheet_overview.getCell(`${IncutixColumnIndex.start}1`);
            IncutixDataLabelColumn.value = "Incutix Data";
            IncutixDataLabelColumn.font = { bold: true, size: 16, underline: true }
            IncutixDataLabelColumn.alignment = { horizontal: "center" };

            const InputColumn = ["EasyLive Profit Sharing Rate"];
            const InputColumnIndex = { start: "AX", end: "AX" };
            const InputLabelColumn = worksheet_overview.getCell(`${InputColumnIndex.start}1`);
            InputLabelColumn.value = "Input Data";
            InputLabelColumn.font = { bold: true, size: 16, underline: true }
            InputLabelColumn.alignment = { horizontal: "center" };

            const ComputedColumn = ["EasyLive Profit", "Incutix Profit", "Total Profit"];
            const ComputedColumnIndex = { start: "AY", end: "BA" };
            worksheet_overview.mergeCells(`${ComputedColumnIndex.start}1:${ComputedColumnIndex.end}1`);
            const ComputedLabelColumn = worksheet_overview.getCell(`${ComputedColumnIndex.start}1`);
            ComputedLabelColumn.value = "Computed Data";
            ComputedLabelColumn.font = { bold: true, size: 16, underline: true }
            ComputedLabelColumn.alignment = { horizontal: "center" };

            // Create Header
            const worksheet_overview_headerRow = worksheet_overview.addRow([...BasicColumn, ...AirwalletxColumn, ...LabpayColumn, ...IncutixColumn, ...InputColumn, ...ComputedColumn]);
            worksheet_overview_headerRow.font = { bold: true };
            worksheet_overview_headerRow.alignment = { horizontal: 'center' };
            worksheet_overview.getColumn(2).alignment = { horizontal: "center" };
            worksheet_overview.getColumn(3).alignment = { horizontal: "center" };
            worksheet_overview.getColumn(4).alignment = { horizontal: "center" };
            worksheet_overview.getColumn(5).alignment = { horizontal: "center" };
            worksheet_overview.getColumn(6).alignment = { horizontal: "center" };

            // Create Content
            const AmountColumnIndex = "P";
            const EasyLiveProfitSharingRateColumnIndex = "AX";

            const tempIdSet = new Set<string>();
            const lists = [
                airwalletxDataReader.GetIds(),
                labpayDataReader.GetIds(),
                incutixSalesDetailDataReader.GetIds()
            ]
            lists.forEach((list) => {
                list.forEach((item) => {
                    if (item) {
                        tempIdSet.add(item);
                    }
                });
            });
            const uniqueIds = Array.from(tempIdSet);
            uniqueIds.forEach((id) => {
                const airwalletxRecord = airwalletxDataReader.GetRecordById(id);
                const labpayRecord = labpayDataReader.GetRecordById(id);
                const incutixRecord = incutixSalesDetailDataReader.GetRecordById(id);
                const foundAtAirwalletX = !!airwalletxRecord;
                const foundAtLabPay = !!labpayRecord;
                const foundAtIncutix = !!incutixRecord;
                const currentRowNumber = worksheet_overview.lastRow ? worksheet_overview.lastRow.number + 1 : undefined;
                const dataRow = worksheet_overview.addRow(
                    [
                        id,
                        foundAtAirwalletX ? "Yes" : "No",
                        foundAtLabPay ? "Yes" : "No",
                        foundAtIncutix ? "Yes" : "No",
                        airwalletxRecord?.['Transaction ID'] ?? "-",
                        airwalletxRecord?.['Transaction type'] ?? "-",
                        airwalletxRecord?.['Source ID'] ?? "-",
                        airwalletxRecord?.['Source entity'] ?? "-",
                        airwalletxRecord?.['Time zone'] ?? "-",
                        airwalletxRecord?.['Created at'] ?? "-",
                        airwalletxRecord?.['Settled at'] ?? "-",
                        airwalletxRecord?.['Transaction currency'] ?? "-",
                        airwalletxRecord?.['Transaction amount'] ?? "-",
                        airwalletxRecord?.['Exchange rate'] ?? "-",
                        airwalletxRecord?.['Settlement currency'] ?? "-",
                        airwalletxRecord?.['Gross amount'] ?? "-",
                        airwalletxRecord?.['Net amount'] ?? "-",
                        airwalletxRecord?.['Payment attempt ID'] ?? "-",
                        airwalletxRecord?.['Payment ID'] ?? "-",
                        airwalletxRecord?.['Order ID'] ?? "-",
                        airwalletxRecord?.['Request ID'] ?? "-",
                        airwalletxRecord?.['Settlement batch ID'] ?? "-",
                        airwalletxRecord?.['Payment method'] ?? "-",
                        airwalletxRecord?.['Connected account id'] ?? "-",
                        airwalletxRecord?.['Account ID'] ?? "-",
                        airwalletxRecord?.['Taxes on fees'] ?? "-",
                        labpayRecord?.['Platform Txn Ref'] ?? "-",
                        labpayRecord?.Outlet ?? "-",
                        labpayRecord?.['Device Sequence #'] ?? "-",
                        labpayRecord?.Currency ?? "-",
                        labpayRecord?.['Cash Amount'] ?? "-",
                        labpayRecord?.Amount ?? "-",
                        labpayRecord?.['Refunded Amount'] ?? "-",
                        labpayRecord?.['Settlement Amount'] ?? "-",
                        labpayRecord?.['Settlement Date'] ?? "-",
                        labpayRecord?.Platform ?? "-",
                        labpayRecord?.['Payment Type'] ?? "-",
                        labpayRecord?.Status ?? "-",
                        labpayRecord?.['Txn (DD/MM/YYYY)'] ?? "-",
                        incutixRecord?.PaymentGatewayOrderId ?? "-",
                        incutixRecord?.DateTime ?? "-",
                        incutixRecord?.Email ?? "-",
                        incutixRecord?.Nickname ?? "-",
                        incutixRecord?.Currency ?? "-",
                        incutixRecord?.Items ?? "-",
                        incutixRecord?.EventName ?? "-",
                        incutixRecord?.PaymentState ?? "-",
                        incutixRecord?.PromotionCode ?? "-",
                        "0.0%",
                        currentRowNumber ? { formula: `=${AmountColumnIndex}${currentRowNumber}*${EasyLiveProfitSharingRateColumnIndex}${currentRowNumber}` } : "-",
                        currentRowNumber ? { formula: `=${AmountColumnIndex}${currentRowNumber}*(1-${EasyLiveProfitSharingRateColumnIndex}${currentRowNumber})` } : "-",
                        currentRowNumber ? { formula: `=${AmountColumnIndex}${currentRowNumber}` } : "-"
                    ]
                );
                dataRow.getCell(2).fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: foundAtAirwalletX ? "AAFFAA" : "FFFFAAAA" }
                };
                dataRow.getCell(3).fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: foundAtLabPay ? "AAFFAA" : "FFFFAAAA" }
                };
                dataRow.getCell(4).fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: foundAtIncutix ? "AAFFAA" : "FFFFAAAA" }
                };
            });
            // Restyle column background color based on the entity.
            const AirwalletCells = ExcelUtils.SelectRange(worksheet_overview, `${AirwalletxColumnIndex.start}1:${AirwalletxColumnIndex.end}${worksheet_overview.actualRowCount}`);
            AirwalletCells.forEach(cell => {
                cell.fill = {
                    type: "pattern",
                    pattern: "solid",
                    fgColor: { argb: "FFE8CC" },
                };
            });
            const LabpayCells = ExcelUtils.SelectRange(worksheet_overview, `${LabpayColumnIndex.start}1:${LabpayColumnIndex.end}${worksheet_overview.actualRowCount}`);
            LabpayCells.forEach(cell => {
                cell.fill = {
                    type: "pattern",
                    pattern: "solid",
                    fgColor: { argb: "F0F8FF" },
                };
            });
            ExcelUtils.SelectRange(worksheet_overview, `${IncutixColumnIndex.start}1:${IncutixColumnIndex.end}${worksheet_overview.actualRowCount}`).forEach(cell => {
                cell.fill = {
                    type: "pattern",
                    pattern: "solid",
                    fgColor: { argb: "FFFFF0" },
                };
            });
            ExcelUtils.SelectRange(worksheet_overview, `${InputColumnIndex.start}1:${InputColumnIndex.end}${worksheet_overview.actualRowCount}`).forEach(cell => {
                cell.fill = {
                    type: "pattern",
                    pattern: "solid",
                    fgColor: { argb: "FFE8CC" },
                };
            });
            ExcelUtils.SelectRange(worksheet_overview, `${ComputedColumnIndex.start}1:${ComputedColumnIndex.end}${worksheet_overview.actualRowCount}`).forEach(cell => {
                cell.fill = {
                    type: "pattern",
                    pattern: "solid",
                    fgColor: { argb: "F0FFF0" },
                };
            });
            ExcelUtils.SelectRange(worksheet_overview, `${AirwalletxColumnIndex.start}3:${ComputedColumnIndex.end}${worksheet_overview.actualRowCount}`).forEach(cell => {
                cell.alignment = { horizontal: "left" };
            });
            // Resize column width
            ExcelUtils.AutoresizeColumn(worksheet_overview);
            // Add Auto filter to table
            worksheet_overview.autoFilter = {
                from: {
                    row: 2,
                    column: 1
                },
                to: {
                    row: worksheet_overview.actualRowCount,
                    column: 58
                }
            };

            // Output excel
            const excelBuffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', `Incutix_Settlement_Report_${dayjs().format("YYYYMMDDHHmmss")}.xlsx`);
            document.body.appendChild(link);
            link.click();
            enqueueSnackbar("Start downloading", { variant: "success" });
            onFinished();
        })();
    }, [airwalletxDataReader, incutixSalesDetailDataReader, labpayDataReader, enqueueSnackbar, onFinished]);

    return (
        <Button variant="contained" endIcon={<UpgradeIcon />} onClick={onExport}>Export</Button>
    )
};

export default ExportButton;