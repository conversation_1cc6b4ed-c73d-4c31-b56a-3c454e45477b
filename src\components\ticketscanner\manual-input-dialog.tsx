import { useBoolean } from '@/hooks/utils/use-boolean';
import { useRef, use<PERSON><PERSON><PERSON>, MouseE<PERSON><PERSON><PERSON><PERSON>, ChangeEventHandler } from 'react';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

type ManualInputDialogProps = {
    state: ReturnType<typeof useBoolean>;
    onConfirm: (token?: string) => void;
}
const ManualInputDialog = ({ onConfirm, state }: ManualInputDialogProps) => {
    const _onClick: MouseEventHandler<HTMLButtonElement> = useCallback((e) => {
        state.onFalse();
        onConfirm(ticketTokenRef.current);
    }, [onConfirm, state]);
    const ticketTokenRef = useRef<string>("");
    const onTextChanged: ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement> = (e) => {
        ticketTokenRef.current = e.target.value;
    }
    return (
        <Dialog open={state.value} onClose={state.onFalse}>
            <DialogTitle>Manual Input</DialogTitle>

            <DialogContent>
                <Typography sx={{ mb: 3 }}>
                    For developer uses only.
                </Typography>

                <TextField
                    inputRef={ticketTokenRef}
                    autoFocus
                    fullWidth
                    margin="dense"
                    variant="outlined"
                    label="Ticket Token"
                    onChange={onTextChanged}
                />
            </DialogContent>

            <DialogActions>
                <Button onClick={state.onFalse} variant="outlined" color="inherit">
                    Cancel
                </Button>
                <Button onClick={_onClick} variant="contained">
                    Confirm
                </Button>
            </DialogActions>
        </Dialog>
    );
};
export default ManualInputDialog;