

import API from '@/utils/api';
import { NextPage } from 'next';
import <PERSON><PERSON><PERSON><PERSON> from "@/constants/config/backend";
import EnumRequestHeader from '@/enum/EnumRequestHeader';
import END_USER_ENDPOINTS from "@/models/api/end-user-endpoints";
import ForbiddenContent from '@/sections/error/forbidden/content';
import PermissionAPIResult from "@/models/api/result/user/permission";

import Stack from '@mui/material/Stack';
import Container from '@mui/material/Container';

const getRequiredPermissions = async (requiredPermissions: string[]) => {
    if (requiredPermissions.length <= 0) {
        return { isValidPermissions: true };
    }
    try {
        // make a function to get client side cookie 
        const userJWT = API.GetUserJWT();
        console.log(`userJWT: ${userJWT}`);
        if (!userJWT) {
            throw new Error("EMPTY_JWT");
        }
        const res = (await BACKEND.Gateway.fetchQuery<PermissionAPIResult>({
            url: END_USER_ENDPOINTS.GetUserInfo(),
            headers: {
                [EnumRequestHeader.AUTHORIZATION as string]: userJWT
            },
            params: {
                queryKey: `userinfo`
            },
            refetchInterval: 5000,
            // cacheTime: 0,
            staleTime: 0
        })).data!;
        const isHavingMismatchedPermissions = requiredPermissions.filter(permission => !res.permissions.includes(permission)).length > 0;
        if (isHavingMismatchedPermissions) {
            throw new Error("SUFFICENT_PERMISSIONS");
        }
        return { isValidPermissions: true };
    } catch (error: unknown) {
        console.warn(error);
        return { isValidPermissions: false };
    }
}
const withPermissions = (PageComponent: NextPage<any>, required_permissions: string[]) => {
    const WrappedPage: NextPage<any> = async (props) => {
        const { isValidPermissions } = await getRequiredPermissions(required_permissions);
        if (!isValidPermissions) {
            return (
                <Container component="main">
                    <Stack
                        sx={{
                            py: 12,
                            m: 'auto',
                            maxWidth: 400,
                            maxHeight: "80vh",
                            textAlign: 'center',
                            justifyContent: 'center',
                        }}
                    >
                        <ForbiddenContent />
                    </Stack>
                </Container>
            );
        }
        return <PageComponent {...props} />
    };
    return WrappedPage;
}
export default withPermissions;