export interface ShoppingCartItem {
    eventId: string;
    eventSessionBundleId: string;
    eventTimeZone: string;
    sessionStartDateTime: number;
    sessionEndDateTime: number;
    eventBundleName: string;
    price: number;
    currency: string;
}
export interface ShoppingCartSummarizedItem extends ShoppingCartItem {
    quantity: number;
}
export interface ShoppingCart {
    eventName: string;
    items: ShoppingCartSummarizedItem[];    
}