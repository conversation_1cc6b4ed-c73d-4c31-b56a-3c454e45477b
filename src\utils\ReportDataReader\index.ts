abstract class ReportDataReader<RawRecord> {
    public rawRecords: RawRecord[] = [];

    abstract GetIds(): string[];
    abstract GetRecordById(id: string): RawRecord | null | undefined;
    abstract ExtractDatasetFromContent(content: string): Promise<RawRecord[]>;

    async ReadContent(content: string) {
        const rawRecords = await this.ExtractDatasetFromContent(content);
        this.rawRecords = rawRecords;
    }
}
export default ReportDataReader;
