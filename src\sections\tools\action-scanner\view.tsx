'use client';

import { TicketScanner } from '@/components/ticketscanner';
import { EnumTicketActionPermission } from '@/enum/EnumTicketActionPermission';

import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';

import { useSettingsContext } from 'src/components/settings';

import ScannerActions from './scanner-actions';

type ActionScannerViewProps = {
    permission: EnumTicketActionPermission
}
export default function ActionScannerView(props: ActionScannerViewProps) {
    const settings = useSettingsContext();
    const { permission } = props;
    return (
        <Container maxWidth={settings.themeStretch ? false : 'xl'}>
            <Typography variant="h4"> Admission QR Code Scanner </Typography>
            <TicketScanner permission={permission} Actions={ScannerActions} />
        </Container>
    );
}
