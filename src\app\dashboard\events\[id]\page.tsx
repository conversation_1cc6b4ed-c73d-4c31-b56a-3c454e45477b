
import { EnumUserPermission } from '@/enum/EnumUserPermission';

import withPermissions from "src/layouts/auth/permission-required-page";

import EventView from '@/sections/events/event/view';

// ----------------------------------------------------------------------

export const metadata = {
    title: 'Incutix Admin: Event',
};

type Props = {
    params: {
        id: string;
    };
};

function Page({ params }: Props) {
    const { id } = params;
    
    return <EventView id={id} />;
}
export default withPermissions(Page, [EnumUserPermission.AES_TEST]);