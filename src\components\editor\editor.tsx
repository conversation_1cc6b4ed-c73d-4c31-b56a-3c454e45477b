/* eslint-disable perfectionist/sort-imports */
import 'src/utils/highlight';

import dynamic from 'next/dynamic';

import { alpha } from '@mui/material/styles';
import Skeleton from '@mui/material/Skeleton';

import { useMemo, useState, useCallback, useRef, RefObject } from 'react';
import { <PERSON><PERSON>rray, FileNavbar, FileBrowser } from 'chonky';
import { Modal } from '@mui/material';
import ReactQuill from 'react-quill';
import { EditorProps } from './types';
import { StyledEditor } from './styles';
import Toolbar, { formats } from './toolbar';



const useFiles = () => useMemo(() => {
    const files: FileArray = [];
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < 8; ++i) {
        files.push({
            id: `file-${i}`,
            name: `${String.fromCharCode(65 + i).repeat(5)}.png`,
        });
    }
    files.push(null);
    return files;
}, []);
// const ReactQuill = dynamic(() => import('react-quill'), {
//     ssr: false,
//     loading: () => (
//         <Skeleton
//             sx={{
//                 top: 0,
//                 left: 0,
//                 right: 0,
//                 bottom: 0,
//                 height: 1,
//                 borderRadius: 1,
//                 position: 'absolute',
//             }}
//         />
//     ),
// });

// ----------------------------------------------------------------------

export default function Editor({
    id = 'minimal-quill',
    error,
    simple = false,
    helperText,
    sx,
    editContainerClasses = {},
    ...other
}: EditorProps) {
    const quillRef = useRef();
    const files = useFiles();
    const [openFileBrowser, setOpenFileBrowser] = useState(false);

    // const handleCustomImagePicker = useCallback(() => setOpenFileBrowser(!openFileBrowser), [openFileBrowser]);
    const handleCustomImagePicker = (value: boolean) => {
        // console.log(quillRef?.current.getEditor().getSelection());
        console.log(value);
        if (value) {
            // setOpenFileBrowser(!openFileBrowser);
        }
    };

    const modules = useMemo(() => ({
        toolbar: {
            container: `#${id}`,
            handlers: {
                'image': handleCustomImagePicker
            }
        },
        history: {
            delay: 500,
            maxStack: 100,
            userOnly: true,
        },
        syntax: true,
        clipboard: {
            matchVisual: false,
        },
    }), [id]);

    return (
        <>
            <StyledEditor
                sx={{
                    ...(error && {
                        border: (theme) => `solid 1px ${theme.palette.error.main}`,
                        '& .ql-editor': {
                            bgcolor: (theme) => alpha(theme.palette.error.main, 0.08),
                        },
                    }),
                    ...sx,
                }}
                className={editContainerClasses}
            >
                <Toolbar id={id} simple={simple} />

                <ReactQuill
                    ref={quillRef as unknown as RefObject<ReactQuill>}
                    modules={modules}
                    formats={formats}
                    placeholder="Write something awesome..."
                    {...other}
                />

                <Modal open={openFileBrowser}>
                    <FileBrowser
                        files={files}
                        // folderChain={folderChain}
                        // onFileAction={handleFileAction}
                    >
                        <FileNavbar />
                    </FileBrowser>
                </Modal>
            </StyledEditor>

            {helperText && helperText}
        </>
    );
}
