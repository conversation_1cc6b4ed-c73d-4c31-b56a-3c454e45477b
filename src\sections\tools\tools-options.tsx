import { m } from 'framer-motion';
import { paths } from '@/routes/paths';
import { useCallback, createElement } from 'react';

import Box from '@mui/material/Box';
import { SvgIconOwnProps } from '@mui/material';
import ListItemButton from '@mui/material/ListItemButton';
import BookOnlineIcon from '@mui/icons-material/BookOnline';

import { useRouter } from 'src/routes/hooks';

import { varFade, MotionViewport } from 'src/components/animate';


// ----------------------------------------------------------------------

const CATEGORIES = [
    {
        label: 'Admission (Scan QR Code)',
        icon: BookOnlineIcon,
        href: paths.dashboard.tools.scan.admission,
    },
    {
        label: 'Reward (Scan QR Code)',
        icon: BookOnlineIcon,
        href: paths.dashboard.tools.scan.reward,
    }
];

// ----------------------------------------------------------------------

export default function ToolsOptions() {
    const router = useRouter();
    const onClickPath = useCallback((url: string) => {
        router.push(url);
    }, [router]);
    return (
        <Box
            component={MotionViewport}
            gap={1}
            display="flex"
            flexWrap="wrap"
            sx={{ alignItems: 'flex-start', p: 1 }}
        >
            {CATEGORIES.map((category) => (
                <m.div key={category.label} variants={varFade().in} onClick={() => onClickPath(category.href)}>
                    <Card category={category} />
                </m.div>
            ))}
        </Box>
    );
}

// ----------------------------------------------------------------------

type CardDesktopProps = {
    category: {
        label: string;
        icon: React.FC<SvgIconOwnProps>;
    };
};
// ----------------------------------------------------------------------

function Card({ category }: CardDesktopProps) {
    const { label, icon } = category;

    return (
        <ListItemButton
            key={label}
            sx={{
                py: 2,
                minWidth: 140,
                borderRadius: 1,
                textAlign: 'center',
                alignItems: 'center',
                typography: 'subtitle2',
                flexDirection: 'column',
                justifyContent: 'center',
                bgcolor: 'background.neutral',
            }}
        >
            {createElement(icon, {
                sx: { width: 48, height: 48, mb: 1 }
            })}
            {category.label}
        </ListItemButton>
    );
}