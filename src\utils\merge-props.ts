export function mergeNestedProps(obj1: { [key in string]: any }, obj2: { [key in string]: any }) {
    const mergedProps = { ...obj1 };

    Object.keys(obj2).forEach((key) => {
        if (Object.prototype.hasOwnProperty.call(obj2, key)) {
            if (typeof obj2[key] === "object" && typeof obj1[key] === "object") {
                mergedProps[key] = mergeNestedProps(obj1[key], obj2[key]);
            } else {
                mergedProps[key] = obj2[key];
            }
        }
    });

    return mergedProps;
}