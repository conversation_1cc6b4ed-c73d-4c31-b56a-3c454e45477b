
import Stepper from '@/components/stepper';
import TStep from '@/components/stepper/type';
import FilePicker from '@/components/file-picker';
import { useSnackbar } from '@/components/snackbar';
import { useMemo, useState, useCallback } from 'react';
import LabPayReportDataReader from '@/utils/ReportDataReader/LabPayReportNormalizer';
import AirwalletxReportDataReader from '@/utils/ReportDataReader/AirwalletxReportDataReader';
import IncutixSalesDetailReportDataReader from '@/utils/ReportDataReader/IncutixSalesDetailReportDataReader';

import RestartAltIcon from '@mui/icons-material/RestartAlt';
import {
    Card,
    Paper,
    Button,
    CardContent,
    CardActions
} from '@mui/material';

import ExportButton from './export-button';

const StepContentSettlementReport = () => {
    const { enqueueSnackbar } = useSnackbar();
    const [airwalletxDataReader, setAirwalletxDataReader] = useState<AirwalletxReportDataReader | undefined>();
    const [labpayDataReader, setLabPayDataReader] = useState<LabPayReportDataReader | undefined>();
    const [incutixSalesDetailDataReader, setIncutixSalesDetailDataReader] = useState<IncutixSalesDetailReportDataReader | undefined>();
    const [activeStep, setActiveStep] = useState<number>(0);

    const IsValidStep = useCallback((step: number) => {
        switch (step) {
            case 0:
                if (!airwalletxDataReader) {
                    enqueueSnackbar("Please upload airwalletx report (.xlsx, .xls)", { variant: "error" });
                    return false;
                }
                break;
            case 1:
                if (!labpayDataReader) {
                    enqueueSnackbar("Please upload labpay report (.xlsx, .xls)", { variant: "error" });
                    return false;
                }
                break;
            case 2:
                if (!incutixSalesDetailDataReader) {
                    enqueueSnackbar("Please upload incutix report (.xlsx)", { variant: "error" });
                    return false;
                }
                break;
            default:
                break;
        }
        return true;
    }, [airwalletxDataReader, incutixSalesDetailDataReader, labpayDataReader, enqueueSnackbar]);

    const CleanAirwalletxDataReader = useCallback(() => {
        setAirwalletxDataReader(undefined);
    }, []);
    const CleanLabPayDataReader = useCallback(() => {
        setLabPayDataReader(undefined);
    }, []);
    const CleanIncutixSalesDetailDataReader = useCallback(() => {
        setIncutixSalesDetailDataReader(undefined);
    }, []);
    const CancelStep = useCallback((step: number) => {
        switch (step) {
            case 1:
                CleanAirwalletxDataReader();
                break;
            case 2:
                CleanLabPayDataReader();
                break;
            case 3:
                CleanIncutixSalesDetailDataReader();
                break;
            default:
                break;
        }
    }, [CleanAirwalletxDataReader, CleanIncutixSalesDetailDataReader, CleanLabPayDataReader]);
    const handleStepperNext = useCallback(() => {
        if (!IsValidStep(activeStep)) {
            return;
        }
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }, [IsValidStep, activeStep]);

    const handleStepperBack = useCallback(() => {
        CancelStep(activeStep - 1);
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    }, [CancelStep, activeStep]);

    const handleStepperReset = useCallback(() => {
        if (activeStep === 0) {
            return;
        }
        for (let i = activeStep - 1; i === 0; i -= 1) {
            CancelStep(i);
        }
        setActiveStep(0);
    }, [CancelStep, activeStep]);

    const onAirWalletXReportChanged = useCallback((content?: string) => {
        (async () => {
            if (!content) {
                CleanAirwalletxDataReader();
                return;
            }
            const dataReader = new AirwalletxReportDataReader();
            try {
                await dataReader.ReadContent(content);
                setAirwalletxDataReader(dataReader);
            } catch {
                enqueueSnackbar("Excel file invalid.", { variant: "error" });
                CleanAirwalletxDataReader();
            }
        })();
    }, [CleanAirwalletxDataReader, enqueueSnackbar]);
    const onLabPayReportChanged = useCallback((content?: string) => {
        (async () => {
            if (!content) {
                CleanLabPayDataReader();
                return;
            }
            const dataReader = new LabPayReportDataReader();
            try {
                await dataReader.ReadContent(content);
                setLabPayDataReader(dataReader);
            } catch {
                enqueueSnackbar("Excel file invalid.", { variant: "error" });
                CleanLabPayDataReader();
            }
        })();
    }, [CleanLabPayDataReader, enqueueSnackbar]);
    const onIncutixSalesDetailReportChanged = useCallback((content?: string) => {
        (async () => {
            if (!content) {
                CleanIncutixSalesDetailDataReader();
                return;
            }
            const dataReader = new IncutixSalesDetailReportDataReader();
            try {
                await dataReader.ReadContent(content);
                setIncutixSalesDetailDataReader(dataReader);
            } catch {
                enqueueSnackbar("Excel file invalid.", { variant: "error" });
                CleanIncutixSalesDetailDataReader();
            }
        })();
    }, [CleanIncutixSalesDetailDataReader, enqueueSnackbar]);
    const steps: TStep[] = useMemo(() => [
        {
            label: "Upload AirWalletX Report",
            component: (
                <Card>
                    {airwalletxDataReader &&
                        <CardContent>
                            {`${airwalletxDataReader.rawRecords.length} Records`}
                        </CardContent>
                    }
                    <CardActions>
                        {airwalletxDataReader ?
                            <Button startIcon={<RestartAltIcon />} onClick={CleanAirwalletxDataReader}>
                                Upload other file
                            </Button> :
                            <FilePicker accept=".xlsx, .xls" onChange={onAirWalletXReportChanged} />
                        }
                    </CardActions>
                </Card>
            )
        },
        {
            label: "Upload LabPay Report",
            component: (
                <Card>
                    {labpayDataReader &&
                        <CardContent>
                            {`${labpayDataReader.rawRecords.length} Records`}
                        </CardContent>
                    }
                    <CardActions>
                        {labpayDataReader ?
                            <Button startIcon={<RestartAltIcon />} onClick={CleanLabPayDataReader}>
                                Upload other file
                            </Button> :
                            <FilePicker accept=".xlsx, .xls" onChange={onLabPayReportChanged} />
                        }
                    </CardActions>
                </Card>
            )
        },
        {
            label: "Upload Incutix Sales Detail Report",
            component: (
                <Card>
                    {incutixSalesDetailDataReader &&
                        <CardContent>
                            {`${incutixSalesDetailDataReader.rawRecords.length} Records`}
                        </CardContent>
                    }
                    <CardActions>
                        {incutixSalesDetailDataReader ?
                            <Button startIcon={<RestartAltIcon />} onClick={CleanIncutixSalesDetailDataReader}>
                                Upload other file
                            </Button> :
                            <FilePicker accept=".xlsx, .xls" onChange={onIncutixSalesDetailReportChanged} />
                        }
                    </CardActions>
                </Card>
            )
        },
    ], [CleanAirwalletxDataReader, CleanIncutixSalesDetailDataReader, CleanLabPayDataReader, airwalletxDataReader, incutixSalesDetailDataReader, labpayDataReader, onAirWalletXReportChanged, onIncutixSalesDetailReportChanged, onLabPayReportChanged]);

    return (
        <>
            <Stepper activeStep={activeStep} steps={steps} onNext={handleStepperNext} onBack={handleStepperBack} />
            {activeStep === steps.length && (
                <Paper square elevation={0} sx={{ p: 3 }}>
                    <Button
                        variant='outlined'
                        color='warning'
                        onClick={handleStepperBack}
                        sx={{ mr: '2rem' }}
                    >
                        Back
                    </Button>
                    <ExportButton incutixSalesDetailDataReader={incutixSalesDetailDataReader!} airwalletxDataReader={airwalletxDataReader!} labpayDataReader={labpayDataReader!} onFinished={handleStepperReset} />
                </Paper>
            )}
        </>
    )
}

export default StepContentSettlementReport;