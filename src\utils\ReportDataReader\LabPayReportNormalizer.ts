import ExcelJS from "exceljs";
import { Buffer } from "buffer";
import { NumberRegex } from "@/constants/common";

import ReportDataReader from "./index";

type LabPayRawRecord = {
    "Order #": string;
    "Platform Txn Ref": string;
    "Outlet": string;
    "Device Sequence #": string;
    "Currency": string;
    "Cash Amount": string;
    "Amount": string;
    "Refunded Amount": string;
    "Settlement Amount": string;
    "Settlement Date": string;
    "Platform": string;
    "Payment Type": string;
    "Status": string;
    "Txn (DD/MM/YYYY)": string;
}
class LabPayReportDataReader extends ReportDataReader<LabPayRawRecord> {
    // eslint-disable-next-line class-methods-use-this
    async ExtractDatasetFromContent(content: string): Promise<LabPayRawRecord[]> {
        const base64Data = content.split(';base64,').pop();
        if (!base64Data) {
            throw new Error("Unable to find Transaction Details (Format invalid)");
        }
        const buffer = Buffer.from(base64Data, 'base64');
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(buffer);
        const worksheet = workbook.worksheets[0];

        let headerRowNumber: number = -1;
        let datasetRowNumber: number = -1;
        let datasetColumnCount: number = 0;
        for (let i = 1; i <= worksheet.rowCount; i += 1) {
            const cellValue = worksheet.getCell(`A${i}`).value;
            if (cellValue === "Order #") {
                headerRowNumber = i;
                datasetRowNumber = i + 1;
                datasetColumnCount = worksheet.getRow(headerRowNumber).cellCount;
                break;
            }
            if (i === worksheet.rowCount) {
                throw new Error("Unable to find Transaction Details (Format invalid)");
            }
        }
        const rawRecords: LabPayRawRecord[] = [];
        for (let i = datasetRowNumber; i <= worksheet.rowCount; i++) {
            const rawRecord: { [key in string]: unknown } = {};
            for (let j = 1; j <= datasetColumnCount; j++) {
                const columnName = worksheet.getRow(headerRowNumber!).getCell(j).value!.toString() as keyof LabPayRawRecord;
                const cellValue = worksheet.getRow(i).getCell(j).value!.toString();
                if (cellValue) {
                    rawRecord[columnName] = NumberRegex.test(cellValue) ? parseFloat(cellValue) : cellValue;
                }
            }
            rawRecords.push(rawRecord as LabPayRawRecord);
        }

        this.rawRecords = rawRecords;
        return rawRecords;
    }

    GetIds(): string[] {
        return this.rawRecords.map(item => item["Order #"]);
    }

    GetRecordById(id: string): LabPayRawRecord | null | undefined {
        return this.rawRecords.find(item => item["Order #"] === id);
    }
}
export default LabPayReportDataReader;
