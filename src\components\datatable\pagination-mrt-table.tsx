import React, { useState } from 'react';
import { mergeNestedProps } from '@/utils/merge-props';
import {
    type MRT_RowData,
    type MRT_SortingState
} from 'material-react-table';

import CustomMRTTable, { CustomMRTTableProps } from './mrt-table';

type PaginationMRTTableProps<TData extends MRT_RowData> = Omit<CustomMRTTableProps<TData>, "data"> & {
    data?: TData[];
}

const PaginationMRTTable = <TData extends MRT_RowData>({ columns, data, ...restProps }: PaginationMRTTableProps<TData>) => {
    const [sorting, setSorting] = useState<MRT_SortingState>([]);
    const fixedProps = {
        state: {
            sorting,
            isLoading: data === undefined
        }
    };
    const mergedProps = mergeNestedProps(fixedProps, restProps) as Omit<CustomMRTTableProps<TData>, "columns" | "data">
    return (
        <CustomMRTTable
            columns={columns}
            data={data ?? []}
            onSortingChange={setSorting}
            {...mergedProps}
        />
    );
};
export default React.memo(PaginationMRTTable) as typeof PaginationMRTTable;