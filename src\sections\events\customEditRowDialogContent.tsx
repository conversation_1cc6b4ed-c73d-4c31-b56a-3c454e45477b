import { useLocales } from "@/locales";
import CustomTextField from "@/components/custom-text-field";
import { eventCategory } from "@/utils/get-language-category-id";
import LocalizableInformationZone from "@/components/event/localizable-information";
import React, { useMemo, useState, ReactNode, useCallback, ReactElement } from "react";
import { MRT_Row, type MRT_TableInstance, MRT_EditActionButtons as MRTEditActionButtons  } from 'material-react-table';
import { detailsZone, publishInfoZone, ticketSaleInfoZone, hasMultipleLanguage } from "@/utils/eventRowDialogContent/layoutDeterminer";

import { 
    Box, 
    Card, 
    Paper, 
    Stack, 
    CardHeader, 
    CardContent, 
    DialogTitle,
    DialogActions,
    DialogContent
} from "@mui/material";

import { GetEvent } from "./types";
import styles from './customEditRowDialogContent.module.scss';

interface CustomEditRowDialogContentProps {
    internalEditComponents: ReactNode[];
    row: MRT_Row<GetEvent>;
    table: MRT_TableInstance<GetEvent>;
    setLocalizableInformationZoneLocale?: React.Dispatch<React.SetStateAction<string>>;
}

interface MultipleLanguageElement {
    element: ReactElement;
    translationCategoryId: string;
}

const CustomEditRowDialogContent = (props: CustomEditRowDialogContentProps) => {
    const { internalEditComponents, row, table, setLocalizableInformationZoneLocale } = props;
    const { currentLang } = useLocales();

    const [ translationCategoryIds ] = useState<MultipleLanguageElement[]>(() => {
        const ids: MultipleLanguageElement[] = [];
        internalEditComponents.forEach(internalEditComponent => {
            const element = internalEditComponent as ReactElement;
            const _key = element.key ?? "";
            const columnId = element.props.cell.column.id;
            if (_key !== "" && hasMultipleLanguage(_key)) {
                ids.push({
                    element,
                    translationCategoryId: eventCategory(columnId),
                });
            }
        });
        return ids;
    });
    const eventId = useMemo(() => row.getValue("eventId") as string, [row]);

    const generalElements = useMemo(() => 
        internalEditComponents.map((internalEditComponent) => {
            const element = internalEditComponent as ReactElement;
            if (!detailsZone(element?.key??"") && !ticketSaleInfoZone(element.key??"") && !publishInfoZone(element.key??"") && !hasMultipleLanguage(element.key??"")) {
                return (
                    <Paper key={element.key} elevation={0}>
                        <div>
                            {element}
                        </div>
                    </Paper>
                );
            }
            return null;
        }), [internalEditComponents]);

    return (
        <>
            <DialogTitle variant="h3">Edit Event</DialogTitle>
            <DialogContent
                sx={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}
            >
                <Box 
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                >
                    <form onSubmit={(e) => e.preventDefault()}>
                        <Stack direction="row" spacing={2} useFlexGap>
                            <Box sx={{ maxWidth: 'xl' }}>
                                <Stack direction="column" spacing={3} useFlexGap>
                                    {generalElements}
                                    <LocalizableInformationZone 
                                        defaultTranslationLanguage={currentLang.value} 
                                        translationCategoryIds={translationCategoryIds}
                                        eventId={eventId}
                                        setLocalizableInformationZoneLocale={setLocalizableInformationZoneLocale}
                                    />
                                </Stack>
                            </Box>
                            <Box sx={{ maxWidth: 'sm' }}>
                                <Stack direction="column" spacing={3} useFlexGap>
                                    <Card sx={{ minWidth: '320px' }}>
                                        <CardHeader title="Details" titleTypographyProps={{ className: styles.editorZoneHeader }} />
                                        <CardContent 
                                            children={
                                                <Stack direction="column" spacing={3} useFlexGap>
                                                    {internalEditComponents.map(internalEditComponent  => {
                                                        const element = internalEditComponent as ReactElement;
                                                        if (detailsZone(element.key??"")) {
                                                            return <CustomTextField key={element.key} {...element.props} />;
                                                        }
                                                        return null;
                                                    })}
                                                </Stack>
                                            } 
                                        />
                                    </Card>
                                    <Card sx={{ minWidth: '320px' }}>
                                        <CardHeader title="Ticket Sales Info." titleTypographyProps={{ className: styles.editorZoneHeader }} />
                                        <CardContent 
                                            children={
                                                <Stack direction="column" spacing={3} useFlexGap>
                                                    {internalEditComponents.map(internalEditComponent  => {
                                                        const element = internalEditComponent as ReactElement;
                                                        if (ticketSaleInfoZone(element.key??"")) {
                                                            return <CustomTextField key={element.key} {...element.props} />;
                                                        }
                                                        return null;
                                                    })}
                                                </Stack>
                                            } 
                                        />
                                    </Card>
                                    <Card sx={{ minWidth: '320px' }}>
                                        <CardHeader title="Publishing" titleTypographyProps={{ className: styles.editorZoneHeader }} />
                                        <CardContent 
                                            children={
                                                <Stack direction="column" spacing={3} useFlexGap>
                                                    {internalEditComponents.map(internalEditComponent  => {
                                                        const element = internalEditComponent as ReactElement;
                                                        if (publishInfoZone(element.key??"")) {
                                                            return <CustomTextField key={element.key} {...element.props} />;
                                                        }
                                                        return null;
                                                    })}
                                                </Stack>
                                            } 
                                        />
                                    </Card>
                                </Stack>
                            </Box>
                        </Stack>
                    </form>
                </Box>
            </DialogContent>
            <DialogActions>
                <MRTEditActionButtons variant="text" table={table} row={row} />
            </DialogActions>
        </>
    );
};

export default React.memo(CustomEditRowDialogContent);