import BACKEND from "@/constants/config/backend";
import END_USER_ENDPOINTS from "@/models/api/end-user-endpoints";
import PermissionAPIResult from "@/models/api/result/user/permission";

const getRequiredPermissions = async (requiredPermissions: string[]) => {
    const res = (await BACKEND.Gateway.fetchQuery<PermissionAPIResult>({
        url: END_USER_ENDPOINTS.GetUserInfo(),
        params: {
            queryKey: `userinfo`
        },
        refetchInterval: 5000,
        // cacheTime: 0,
        staleTime: 0
    })).data!;
    const isHavingMismatchedPermissions = requiredPermissions.filter(permission => !res.permissions.includes(permission)).length > 0;
    if (isHavingMismatchedPermissions) {
        return { isValidPermissions: false };
    }
    return { isValidPermissions: true };
}
export default getRequiredPermissions;