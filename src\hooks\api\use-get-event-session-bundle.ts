import <PERSON><PERSON><PERSON><PERSON> from "@/constants/config/backend";
import { keepPreviousData } from "@tanstack/react-query";
import ADMIN_ENDPOINTS from "@/models/api/admin-endpoints";
import GetAllEventSessionAPIResult from "@/models/api/result/events/GetAllEventSession";

const useGetEventAllSessionBundle = (id: string,sid:string, pageNum: number, pageSize: number) => {
    const result = BACKEND.Gateway.useQuery<GetAllEventSessionAPIResult>({
        url: `${ADMIN_ENDPOINTS.GetAllSessionBundleById(id,sid, pageNum)}`,
        query: {
            page_size: `${pageSize}`
        },
        params: {
            queryKey: `session-${id}-${pageNum}-${pageSize}`
        },
        placeholderData: keepPreviousData,
    });
    return result.data?.data;
};
export default useGetEventAllSessionBundle;