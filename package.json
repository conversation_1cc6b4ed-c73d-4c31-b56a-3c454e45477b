{"name": "@minimal-kit/starter-next-ts", "author": "Minimals", "version": "5.6.86", "description": "Next Starter & TypeScript", "private": true, "scripts": {"dev": "cross-env PROJECT_FRONTEND_ENV=LOC PROJECT_BACKEND_ENV=DEV next dev -p 3000", "loc": "cross-env PROJECT_FRONTEND_ENV=LOC PROJECT_BACKEND_ENV=LOC next dev -p 3000", "start": "next start -p 3000", "build": "next build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "rm:all": "rm -rf node_modules .next out dist build", "re:start": "yarn rm:all && yarn install && yarn dev", "re:build": "yarn rm:all && yarn install && yarn build", "re:build-npm": "npm run rm:all && npm install && npm run build", "dev:ts": "yarn dev & yarn ts:watch", "ts": "tsc --noEmit --incremental", "ts:watch": "yarn ts --watch"}, "dependencies": {"@auth0/auth0-react": "^2.2.1", "@emotion/cache": "^11.10.5", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.3.1", "@iconify/react": "^4.1.1", "@mui/icons-material": "^5.14.18", "@mui/lab": "^5.0.0-alpha.173", "@mui/material": "^5.16.7", "@mui/system": "^5.14.18", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.5", "@react-dnd/invariant": "^4.0.2", "@stoneleigh/api-lib": "^6.1.7", "@stoneleigh/sdk-core": "^1.1.82", "@stoneleigh/sdk-nextjs": "^1.1.95", "@tanstack/react-query": "^5.8.9", "@tanstack/react-query-devtools": "^5.8.9", "@tanstack/react-table": "^8.10.7", "@yudiel/react-qr-scanner": "^1.2.3", "autosuggest-highlight": "^3.3.4", "axios": "^1.5.1", "chonky": "^2.3.2", "cookies-next": "^4.1.0", "date-fns": "^2.30.0", "dayjs": "^1.11.10", "dayjs-plugin-utc": "^0.1.2", "exceljs": "^4.4.0", "framer-motion": "^10.16.4", "highlight.js": "^11.9.0", "i18next": "^23.7.7", "i18next-browser-languagedetector": "^7.2.0", "immutable": "^5.0.0-beta.4", "lodash": "^4.17.21", "material-react-table": "^2.0.4", "next": "^14.0.2", "notistack": "^3.0.1", "nprogress": "^0.2.0", "numeral": "^2.0.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-i18next": "^13.5.0", "react-lazy-load-image-component": "^1.6.0", "react-quill": "^2.0.0", "simplebar-react": "^3.2.4", "stylis": "^4.3.0", "stylis-plugin-rtl": "^2.1.1", "yup": "^1.3.2"}, "devDependencies": {"@next/swc-linux-arm64-musl": "13.4.12", "@next/swc-linux-x64-musl": "13.4.12", "@svgr/webpack": "^8.1.0", "@types/autosuggest-highlight": "^3.2.0", "@types/lodash": "^4.14.199", "@types/node": "^20.8.2", "@types/nprogress": "^0.2.1", "@types/numeral": "^2", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "@types/react-lazy-load-image-component": "^1", "@types/stylis": "^4.2.1", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "cross-env": "^7.0.3", "eslint": "^8.50.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-perfectionist": "^2.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^3.0.0", "prettier": "^3.0.3", "sass": "^1.69.5", "typescript": "^5.2.2"}, "packageManager": "yarn@4.9.2"}