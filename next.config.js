

module.exports = {
  trailingSlash: true,
  reactStrictMode: true,
  env: {
    PROJECT_FRONTEND_ENV: process.env.PROJECT_FRONTEND_ENV,
    PROJECT_BACKEND_ENV: process.env.PROJECT_BACKEND_ENV,
  },
  modularizeImports: {
    '@mui/material': {
      transform: '@mui/material/{{member}}',
    },
    '@mui/lab': {
      transform: '@mui/lab/{{member}}',
    },
  },
  webpack(config,{ isServer }) {
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });
    config.module = {
      ...config.module,
      exprContextCritical: false // suppress warning caused within package 'prettier'
    };
    if (!isServer) {
      if (!config.resolve.fallback) {
        config.resolve.fallback = {};
      }
      config.resolve.fallback.fs = false;
    }
    return config;
  },
};
