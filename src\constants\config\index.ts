import EnumReportType from '@/enum/EnumReportType';
import { PROJECT_BACKEND_ENV } from '@/config-global';
import { Enum, AppConfig } from "@stoneleigh/sdk-core";
import { EnumConfigEnv } from '@stoneleigh/sdk-core/dist/Enum';


interface PowerBIReport {
    reportId: string;
}
interface IAppConfig {
    powerbi: {
        workspaceId: string,
        reports: { [key in EnumReportType]: PowerBIReport }
    }
}

const appConfig = new AppConfig<IAppConfig>({
    CurrentEnv: PROJECT_BACKEND_ENV as EnumConfigEnv,
    EnvConfigsMap: {
        [Enum.EnumConfigEnv.LOC]: {
            powerbi: {
                workspaceId: "bdbced93-2934-4bad-92ae-f5314b7c4ae0",
                reports: {
                    [EnumReportType.User]: {
                        reportId: "048648a1-f74d-4f2c-8402-46add409c0bf"
                    }
                }
            }
        },
        [Enum.EnumConfigEnv.DEV]: {
            powerbi: {
                workspaceId: "bdbced93-2934-4bad-92ae-f5314b7c4ae0",
                reports: {
                    [EnumReportType.User]: {
                        reportId: "048648a1-f74d-4f2c-8402-46add409c0bf"
                    }
                }
            }
        },
        [Enum.EnumConfigEnv.UAT]: {
            powerbi: {
                workspaceId: "bdbced93-2934-4bad-92ae-f5314b7c4ae0",
                reports: {
                    [EnumReportType.User]: {
                        reportId: "048648a1-f74d-4f2c-8402-46add409c0bf"
                    }
                }
            }
        },
        [Enum.EnumConfigEnv.PREPRD]: {
            powerbi: {
                workspaceId: "2643ef6f-476a-4181-8d6d-ed50d2f05b71",
                reports: {
                    [EnumReportType.User]: {
                        reportId: "90e3b2b1-68a8-4e37-a133-2a3168a1aef5"
                    }
                }
            }
        },
        [Enum.EnumConfigEnv.PRD]: {
            powerbi: {
                workspaceId: "2643ef6f-476a-4181-8d6d-ed50d2f05b71",
                reports: {
                    [EnumReportType.User]: {
                        reportId: "90e3b2b1-68a8-4e37-a133-2a3168a1aef5"
                    }
                }
            }
        }
    }
});


export { appConfig as AppConfig };
export type { IAppConfig, PowerBIReport };
