
import { EnumUserPermission } from '@/enum/EnumUserPermission';

import withPermissions from "src/layouts/auth/permission-required-page";

import EventSessionView from '@/sections/sessions/view';

// ----------------------------------------------------------------------

export const metadata = {
    title: 'Incutix Admin: Event Session',
};

type Props = {
    params: {
        sid: string;
    };
};

function Page({ params }: Props) {
    const { sid } = params;
    
    return <EventSessionView sid={sid} />;
}
export default withPermissions(Page, [EnumUserPermission.AES_TEST]);