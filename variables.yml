variables:
  DOCKER_AWS_ACCESS_KEY_ID: $EASYLIVE_CODEARTIFACT_AWS_ACCESS_KEY_ID
  DOCKER_AWS_SECRET_ACCESS_KEY: $EASYLIVE_CODEARTIFACT_AWS_SECRET_ACCESS_KEY
  ECR_AWS_PROFILE: "vex"
  ECR_REPO_NAME: "incutix-react-adminweb"
  ECR_AWS_ACCOUNT_ID: $VEXMETA_ECR_AWS_ACCOUNT_ID
  ECR_AWS_ACCESS_KEY_ID: $VEXMETA_ECR_AWS_ACCESS_KEY_ID
  ECR_AWS_SECRET_ACCESS_KEY: $VEXMETA_ECR_AWS_SECRET_ACCESS_KEY
  ECR_AWS_REGION: $VEXMETA_ECR_AWS_REGION
  ECR_DOMAIN: "$ECR_AWS_ACCOUNT_ID.dkr.ecr.$ECR_AWS_REGION.amazonaws.com"
  K8S_AWS_ACCOUNT_ID: $VEXMETA_K8S_AWS_ACCOUNT_ID
  K8S_AWS_ACCESS_KEY_ID: $VEXMETA_K8S_AWS_ACCESS_KEY_ID
  K8S_AWS_SECRET_ACCESS_KEY: $VEXMETA_K8S_AWS_SECRET_ACCESS_KEY
  K8S_AWS_REGION: $VEXMETA_K8S_AWS_REGION
  K8S_CLUSTER_NAME: $VEXMETA_K8S_CLUSTER_NAME
  K8S_BASIC_NAMESPACE_NAME: "incutix"
