'use client';

import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';

import { useApproveMembership } from '@/hooks/api/use-approve-membership';
import useGetMembershipApplications from '@/hooks/api/use-get-membership-applications';
import GetMembershipApplicationsAPIResult from '@/models/api/result/memberships/GetMembershipApplications';
import CheckIcon from '@mui/icons-material/Check';
import ClearIcon from '@mui/icons-material/Clear';
import { Box, IconButton } from '@mui/material';
import dayjs from 'dayjs';
import { MRT_ColumnDef, MRT_Row } from 'material-react-table';
import React, { useCallback, useMemo, useState } from 'react';
import { PaginationDataTable } from 'src/components/datatable';
import { useSettingsContext } from 'src/components/settings';
import { useSnackbar } from 'src/components/snackbar';
import { GetMembership } from '../types';

const MembershipApplicationView = () => {
    const settings = useSettingsContext();
    const { enqueueSnackbar } = useSnackbar();

    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: 20,
    });
    const columns: Array<MRT_ColumnDef<GetMembership>> = useMemo(() => [
        {
            accessorKey: "membershipSubscriptionId",
            header: "MembershipSubscriptionId",
            enableSorting: false
        },
        {
            accessorKey: 'userId',
            header: 'User Id',
            enableSorting: false,
            enableClickToCopy: true,
            enableEditing: false
        },
        {
            accessorKey: "email",
            header: "Email",
            enableSorting: true
        },
        {
            accessorKey: "firstName",
            header: "First Name",
            enableSorting: true
        },
        {
            accessorKey: "lastName",
            header: "Last Name",
            enableSorting: true
        },
        {
            accessorKey: "nickName",
            header: "Nick Name",
            enableSorting: true
        },
        {
            accessorKey: "country",
            header: "Country",
            enableSorting: true
        },
        {
            accessorKey: "mobilePhoneNumber",
            header: "PhoneNumber",
            enableSorting: false
        },
        {
            accessorKey: "dateOfBirth",
            accessorFn: (originalRow: GetMembership) => dayjs.utc(originalRow.dateOfBirth).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Date of birth",
            enableSorting: true
        },
        {
            accessorKey: "registerDateTime",
            accessorFn: (originalRow: GetMembership) => dayjs.utc(originalRow.registerDateTime).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Register Date",
            enableSorting: true
        },
        {
            accessorKey: 'paymentOrderId',
            header: 'Event Name',
            enableSorting: false,
            enableEditing: false
        },
        {
            accessorKey: 'serviceType',
            header: 'Service Type',
            enableSorting: false,
            enableEditing: false
        },
        {
            accessorKey: 'membershipTypeName',
            header: 'Membership Type Name',
            enableSorting: false
        },
        {
            accessorKey: 'price',
            header: 'Price',
            enableSorting: false
        },
        {
            accessorKey: "currency",
            header: "Currency",
            enableSorting: false
        },
        {
            accessorKey: "createdDateTime",
            accessorFn: (originalRow: GetMembership) => dayjs.utc(originalRow.registerDateTime).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Applied Date",
            enableSorting: true
        },
        {
            accessorKey: "subscribedPromotion",
            accessorFn: (originalRow: GetMembership) => originalRow.subscribedPromotion ? "Yes" : "No",
            header: "Marketing Opt-in",
            enableSorting: false
        },
    ], []);
    const [ paginationDataTableInitialState ] = useState({
        columnVisibility: {
            userId: false,
            paymentOrderId: false,
            membershipSubscriptionId: false,
        }
    });
    const [ paginationDataTableState ] = useState({
        pagination
    });

    const { data: membershipApplications, refetch } = useGetMembershipApplications('REQUEST', pagination.pageIndex + 1, pagination.pageSize);
    const approveMembershipAsync = useApproveMembership();
    const apiData = useMemo(() => {
        if (membershipApplications?.data) return membershipApplications.data;
        return { list: [], total: 0 } as unknown as GetMembershipApplicationsAPIResult;
    }, [membershipApplications]);

    const handleApprove = useCallback(async (row: MRT_Row<GetMembership>) => {
        const membershipSubscriptionId = row.getValue<string>("membershipSubscriptionId");
        try {
            const approveRes = await approveMembershipAsync([ membershipSubscriptionId ], "VERIFIED");
            if (approveRes.data) {
                enqueueSnackbar("Approved", { variant: "success" });
            }
        } catch(error) {
            enqueueSnackbar(error.message, { variant: "error" });
        }
        refetch();
    }, [approveMembershipAsync, enqueueSnackbar, refetch]);
    
    const handleReject = useCallback(() => {
        enqueueSnackbar("Not implemented", { variant: "warning" });
        refetch();
    }, [enqueueSnackbar, refetch]);
    
    const rowActions = ({ row } : { row: MRT_Row<GetMembership> }) => (
        <Box>
            <IconButton onClick={() => handleApprove(row)}>
                <CheckIcon sx={{ color: "green" }} />
            </IconButton>
            <IconButton onClick={() => handleReject()}>
                <ClearIcon sx={{ color: "red" }} />
            </IconButton>
        </Box>
    );

    return (
        <Container maxWidth={settings.themeStretch ? false : 'xl'}>
            <Typography variant="h4"> Membership Subscription Review </Typography>
            <PaginationDataTable
                columns={columns}
                data={apiData?.list}
                rowCount={apiData?.total}
                enableEditing
                editDisplayMode="modal"
                createDisplayMode="modal"
                enableSelectAll={false}
                enableRowSelection={false}
                manualPagination
                initialState={paginationDataTableInitialState}
                state={paginationDataTableState}
                onPaginationChange={setPagination}
                enableRowActions
                renderRowActions={rowActions}
            />
        </Container>
    );
}

export default React.memo(MembershipApplicationView);