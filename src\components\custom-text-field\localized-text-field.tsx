import useGetLanguageTranslation from "@/hooks/api/use-get-language-translation";
import { useSaveInputValueToRowCache } from "@/hooks/utils/use-save-input-row-cache";
import 
React,
{ 
    useRef,
    useMemo,
    useState,
    useEffect
} from "react";

import { Box } from "@mui/system";

import CustomTextField, { CustomTextFieldRef } from ".";
import { MultipleLanguageElement } from "../event/types";

interface CustomLocalizedTextFieldProps {
    language: string;
    content: MultipleLanguageElement;
    eventId: string;
}

const CustomLocalizedTextField = (props: CustomLocalizedTextFieldProps) => {
    const {
        language,
        content,
        eventId
    } = props;
    const { element, translationCategoryId } = content;

    const { requestAsync: fetchLanguageTranslationAsync } = useGetLanguageTranslation();
    const saveInputValueToRowCache = useSaveInputValueToRowCache();
    const customTextFieldRef = useRef<CustomTextFieldRef>(null);

    const [ table, setTable ] = useState(element.props.table);
    const [ cell, setCell ] = useState(element.props.cell);

    useEffect(() => {
        try {
            (async () => {
                console.log('rerender');
                const translationDetail = await fetchLanguageTranslationAsync({
                    locale: language,
                    categoryId: translationCategoryId,
                    translationKey: eventId
                });
                if (translationDetail.result && translationDetail.data) {
                    const firstRecord = translationDetail.data.list.at(0);
                    saveInputValueToRowCache({
                        newValue: firstRecord?.value as string, 
                        element
                    });

                    setTable(element.props.table);
                    setCell(element.props.cell);
                    customTextFieldRef.current?.setValue(element.props.cell.getValue());
                } 
            })();
        } catch { /* empty */ }

        return () => {}
    }, [element, eventId, fetchLanguageTranslationAsync, language, saveInputValueToRowCache, translationCategoryId]);

    const renderTextField = useMemo(() => <CustomTextField ref={customTextFieldRef} table={table} cell={cell} />, [cell, table]);

    return (
        <Box mb={2}>
            {renderTextField}
        </Box>
    );
};

export default React.memo(CustomLocalizedTextField);