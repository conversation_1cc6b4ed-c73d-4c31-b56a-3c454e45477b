'use client';

import MRTMarkdownEditCellTextField from '@/components/datatable/mrt-markdown-edit-cell-text-field';
import { useSnackbar } from '@/components/snackbar';
import useGetEvents from '@/hooks/api/use-get-events';
import { useUpdateEvent, useUpdateEventLocalizableAsync } from '@/hooks/api/use-update-event';
import { useLocales } from '@/locales';
import { useRouter } from '@/routes/hooks';
import eventInputData, { EventInputProps } from '@/utils/dataFormatter/eventInputData';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { MRT_Row, MRT_TableOptions, type MRT_ColumnDef, type MRT_TableInstance } from 'material-react-table';
import React, { ReactNode, useCallback, useMemo, useState } from 'react';
import { LiteralUnion } from 'react-hook-form';

import AddIcon from "@mui/icons-material/Add";
import {
    Button,
    Container,
    Typography,
} from '@mui/material';

import { PaginationDataTable } from 'src/components/datatable';
import { useSettingsContext } from 'src/components/settings';

import CustomEditRowDialogContent from './customEditRowDialogContent';
import { type GetEvent } from './types';

dayjs.extend(utc);
dayjs.extend(timezone);

interface CustomEditRowDialogContentProps {
    internalEditComponents: ReactNode[];
    row: MRT_Row<GetEvent>;
    table: MRT_TableInstance<GetEvent>;
}

const EventsView = () => {
    const settings = useSettingsContext();
    const { enqueueSnackbar } = useSnackbar();
    const router = useRouter();
    const { currentLang } = useLocales();

    console.log("event view - shows 2 times is correct, case the Page will render 2 times for /dashboard/*");

    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: 10,
    });
    const columns: Array<MRT_ColumnDef<GetEvent>> = useMemo(() => [
        {
            accessorKey: 'eventId',
            header: 'Event Id (Auto-generated)',
            enableSorting: false,
            enableClickToCopy: true,
            enableEditing: false
        },
        {
            accessorKey: 'eventName',
            header: 'Event Name',
            enableSorting: true
        },
        {
            accessorKey: 'eventDescription',
            header: 'Event Description',
            enableSorting: false,
            /* eslint-disable react/prop-types */ // TODO: upgrade to latest eslint tooling
            Edit: ({ cell, table, ...rest }) => <MRTMarkdownEditCellTextField cell={cell} table={table} {...rest} />,
        },
        {
            accessorKey: 'eventTimeZone',
            header: 'Event Time Zone',
            enableSorting: false
        },
        {
            accessorKey: 'eventLocation',
            header: 'Event Location',
            enableSorting: false
        },
        {
            accessorKey: "eventStartDateTime",
            accessorFn: (originalRow: GetEvent) => dayjs.utc(originalRow.eventStartDateTime).tz(originalRow.eventTimeZone).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Event Start Date Time",
            enableSorting: true
        },
        {
            accessorKey: "eventEndDateTime",
            accessorFn: (originalRow: GetEvent) => dayjs.utc(originalRow.eventEndDateTime).tz(originalRow.eventTimeZone).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Event End Date Time",
            enableSorting: true
        },
        {
            accessorKey: "saleStartDateTimeForDisplay",
            accessorFn: (originalRow: GetEvent) => dayjs.utc(originalRow.saleStartDateTimeForDisplay).tz(originalRow.eventTimeZone).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Sale Start Date Time (For Display)",
            enableSorting: true
        },
        {
            accessorKey: "saleEndDateTimeForDisplay",
            accessorFn: (originalRow: GetEvent) => dayjs.utc(originalRow.saleEndDateTimeForDisplay).tz(originalRow.eventTimeZone).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Sale End Date Time (For Display)",
            enableSorting: true
        },
        {
            accessorKey: "isPrivate",
            accessorFn: (originalRow: GetEvent) => originalRow.isPrivate ? "Yes" : "No",
            header: 'Is Private',
            enableSorting: true,
            editVariant: 'select',
            editSelectOptions: ['Yes', 'No'],
        },
        {
            accessorKey: "publish",
            accessorFn: (originalRow: GetEvent) => originalRow.publish ? "Yes" : "No",
            header: 'Publish',
            enableSorting: true,
            editVariant: 'select',
            editSelectOptions: ['Yes', 'No'],
        },
        {
            accessorKey: "publishDateTime",
            accessorFn: (originalRow: GetEvent) => dayjs.utc(originalRow.publishDateTime).tz(originalRow.eventTimeZone).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Publish Date Time",
            enableSorting: true
        },
        {
            accessorKey: "isListDetails",
            accessorFn: (originalRow: GetEvent) => originalRow.isListDetails ? "Yes" : "No",
            header: "Is List Details",
            enableSorting: false,
            editVariant: 'select',
            editSelectOptions: ['Yes', 'No'],
        },
        {
            accessorKey: "alertThreshold",
            header: "Alert Threshold",
            enableSorting: false
        },
        {
            accessorKey: "totalSupply",
            header: "Total Supply",
            enableSorting: false
        }
    ], []);

    const [ paginationDataTableInitialState ] = useState({
        columnVisibility: {
            eventDescription: false,
            eventLocation: false,
            saleStartDateTimeForDisplay: false,
            saleEndDateTimeForDisplay: false,
            alertThreshold: false,
            totalSupply: false,
        }
    });

    const [ paginationDataTableState ] = useState({
        pagination
    });

    const apiData = useGetEvents(pagination.pageIndex + 1, pagination.pageSize);
    const updateEventAsync = useUpdateEvent();
    const updateEventLocalizableAsync = useUpdateEventLocalizableAsync();
    const [ localizableInformationZoneLocale, setLocalizableInformationZoneLocale ] = useState<string>(currentLang.value);

    const onCreatingRowSave = useCallback(({ table, values }: { table: MRT_TableInstance<GetEvent>, values: Record<LiteralUnion<keyof GetEvent, string>, any> }) => {
        // validate data
        // save data to api
        table.setCreatingRow(null); // exit creating mode
    }, []);

    const CreateButton = useCallback((props: { table: MRT_TableInstance<GetEvent> }) => (
        <Button color="primary" startIcon={<AddIcon />} onClick={() => props.table.setCreatingRow(true)}>
            Create New Events
        </Button>
    ), []);

    const renderEditRowDialogContent = useCallback((props: CustomEditRowDialogContentProps) => 
        <CustomEditRowDialogContent 
            setLocalizableInformationZoneLocale={setLocalizableInformationZoneLocale} 
            {...props}
        />
    , []);

    const handleSaveEvent: MRT_TableOptions<GetEvent>['onEditingRowSave'] = useCallback(async ({
        values,
        table,
    }: { values: EventInputProps, table: MRT_TableInstance<GetEvent> }) => {
        // setValidationErrors({});
        console.log(values);
        console.log(eventInputData(values));
        const eventTableInfo = eventInputData(values);
        console.log(localizableInformationZoneLocale);
        await updateEventAsync(eventTableInfo.fields);
        await updateEventLocalizableAsync(eventTableInfo.localizableFields, localizableInformationZoneLocale);
        enqueueSnackbar("Update Success");
        // table.setEditingRow(null); // exit editing mode
    }, [enqueueSnackbar, localizableInformationZoneLocale, updateEventAsync, updateEventLocalizableAsync]);

    const handleRowClick = useCallback(({ row }: { row: MRT_Row<GetEvent> } ) => ({
        onClick: () => {
            row.getToggleSelectedHandler();
            console.log("Row Clicked:", `/dashboard/events/${row.original.eventId}?name=${row.original.eventName}`); 
            router.push(`/dashboard/events/${row.original.eventId}?name=${row.original.eventName}`)
            // router.push('/dashboard')
          },
        sx: { cursor: 'pointer' },
    }), [router]);

    return (
        <Container maxWidth={settings.themeStretch ? false : 'xl'}>
            <Typography variant="h4"> Events </Typography>
            <PaginationDataTable
                columns={columns}
                data={apiData?.list}
                rowCount={apiData?.total}
                enableEditing
                editDisplayMode="modal"
                createDisplayMode="modal"
                onCreatingRowSave={onCreatingRowSave}
                renderTopToolbarCustomActions={CreateButton}
                enableSelectAll={false}
                enableRowSelection={false}
                manualPagination
                initialState={paginationDataTableInitialState}
                state={paginationDataTableState}
                onPaginationChange={setPagination}
                renderEditRowDialogContent={renderEditRowDialogContent}
                onEditingRowSave={handleSaveEvent}
                muiTableBodyRowProps={handleRowClick}
            />
        </Container>
    );
}

export default React.memo(EventsView);
