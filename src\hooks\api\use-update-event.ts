import API from "@/utils/api";
import <PERSON><PERSON><PERSON><PERSON> from "@/constants/config/backend";
import EnumRequestHeader from "@/enum/EnumRequestHeader";
import ADMIN_ENDPOINTS from "@/models/api/admin-endpoints";
import { eventCategory } from "@/utils/get-language-category-id";
import UpdateEventAPIResult from "@/models/api/result/events/UpdateEvent";
import { EventLocalizableFieldProps } from "@/utils/dataFormatter/eventInputData";

interface UpdatedFieldsInput {
    categoryId: string;
    translationKey: string;
    translationValue: string;
}

export const useUpdateEvent = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async (params: { eventId: string }) => {
        const { eventId, ...restParams } = params;
        return fetchAsync<UpdateEventAPIResult>(
            ADMIN_ENDPOINTS.UpdateEventById(eventId),
            {
                method: "PUT",
                data: API.ToFormData(restParams)
            }
        );
    };
    return requestAsync;
};

export const useUpdateEventLocalizableAsync = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async (params: EventLocalizableFieldProps, locale: string) => {
        const formDataObject = {};
        const updatedFields = Object.keys(params).reduce((obj: { [key: string]: unknown; }, keyName, index) => {
            if (keyName !== "eventId") {
                const value = params[keyName as keyof EventLocalizableFieldProps];
                obj[`data[${index-1}][categoryId]`] = eventCategory(keyName);
                obj[`data[${index-1}][translationKey]`] = params.eventId;
                obj[`data[${index-1}][translationValue]`] = value;
            }
            return obj;
        }, formDataObject);
        return fetchAsync<UpdateEventAPIResult>(
            ADMIN_ENDPOINTS.UpdateEventLocalizationById(locale),
            {
                method: "POST",
                data: API.ToFormData(updatedFields)
            }
        );
    };        
    return requestAsync;
};