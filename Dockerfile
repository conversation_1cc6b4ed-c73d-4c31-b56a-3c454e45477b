ARG PREBUILD_STAGE
FROM node:lts-alpine as pre-build-dev
WORKDIR /app
# set .env
RUN touch .env
COPY <<EOF ./.env
	NEXT_PUBLIC_HOST_API=https://dev-api.incutix.com
    NEXT_PUBLIC_ASSETS_API=https://assets.incutix.com
EOF

FROM node:lts-alpine as pre-build-uat
WORKDIR /app
# set .env
RUN touch .env
COPY <<EOF ./.env
	NEXT_PUBLIC_HOST_API=https://sit-api.incutix.com
    NEXT_PUBLIC_ASSETS_API=https://assets.incutix.com
EOF

FROM node:lts-alpine as pre-build-preprd
WORKDIR /app
# set .env
RUN touch .env
COPY <<EOF ./.env
	NEXT_PUBLIC_HOST_API=https://preprd-api.incutix.com
    NEXT_PUBLIC_ASSETS_API=https://assets.incutix.com
EOF

FROM node:lts-alpine as pre-build-prd
WORKDIR /app
# set .env
RUN touch .env
COPY <<EOF ./.env
	NEXT_PUBLIC_HOST_API=https://api.incutix.com
    NEXT_PUBLIC_ASSETS_API=https://assets.incutix.com
EOF

FROM ${PREBUILD_STAGE} as pre-build

FROM node:lts-alpine as builder

WORKDIR /app

ARG STONELEIGH_CODEARTIFACT_AWS_ACCESS_KEY_ID
ARG STONELEIGH_CODEARTIFACT_AWS_SECRET_ACCESS_KEY
ARG PROJECT_ENV
ENV PROJECT_ENV=$PROJECT_ENV
ENV PROJECT_FRONTEND_ENV=$PROJECT_ENV
ENV PROJECT_BACKEND_ENV=$PROJECT_ENV
RUN apk add aws-cli

# aws profile setup
RUN aws configure set aws_access_key_id $STONELEIGH_CODEARTIFACT_AWS_ACCESS_KEY_ID --profile stoneleigh 
RUN aws configure set aws_secret_access_key $STONELEIGH_CODEARTIFACT_AWS_SECRET_ACCESS_KEY --profile stoneleigh

COPY package.json ./
COPY .yarn/ ./.yarn
#RUN touch ./build.log
COPY . ./
COPY --from=pre-build /app/.env ./.env
RUN yarn install --immutable
RUN yarn build

FROM node:lts-alpine as runner
WORKDIR /app
ENV NODE_ENV=production
ENV PORT=3000
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder /app/next.config.js ./next.config.js
COPY --from=builder /app/.env ./.env

# yarn 3
COPY --from=builder /app/.yarn ./.yarn
COPY --from=builder /app/.yarnrc.yml ./.yarnrc.yml
COPY --from=builder /app/.yarn-plugin-aws-codeartifact.yml ./.yarn-plugin-aws-codeartifact.yml
COPY --from=builder /app/.pnp.cjs ./.pnp.cjs
COPY --from=builder /app/.pnp.loader.mjs ./.pnp.loader.mjs
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/yarn.lock ./yarn.lock

# Note yarn rebuild again - this is to let yarn rebuild binaries in the "runner" stage of the Dockerfile
# We also have to remove unplugged, so that rebuilding happens and replaces the old binaries
RUN rm -rf /app/.yarn/unplugged && yarn rebuild

USER nextjs
EXPOSE ${PORT}
CMD [ "yarn", "start" ]
