import ExcelJS from "exceljs";

const CellRegExp = /([A-Z]+)(\d+)/gi
class ExcelUtils {
    static SelectRange = (sheet: ExcelJS.Worksheet, cellRange: string) => {
        const [startCell, endCell] = cellRange.split(":", 2);
        const [, startCellColumn, startRow] = new RegExp(CellRegExp).exec(startCell)!;
        const [, endCellColumn, endRow] = new RegExp(CellRegExp).exec(endCell)!;
        const endColumn = sheet.getColumn(endCellColumn);
        const startColumn = sheet.getColumn(startCellColumn);

        if (!endColumn) throw new Error("End column not found");
        if (!startColumn) throw new Error("Start column not found");

        const endColumnNumber = endColumn.number;
        const startColumnNumber = startColumn.number;

        const cells = [];
        for (let y = parseInt(startRow, 10); y <= parseInt(endRow, 10); y++) {
            const row = sheet.getRow(y);
            for (let x = startColumnNumber; x <= endColumnNumber; x++) {
                cells.push(row.getCell(x));
            }
        }
        return cells;
    };

    static AutoresizeColumn = (worksheet: ExcelJS.Worksheet) => {
        worksheet.columns.forEach((column) => {
            const lengths = column.values ? column.values.map(v => v ? v.toString().length + 5 : 0) : [10];
            const maxLength = Math.max(...lengths.filter(v => typeof v === 'number'));
            column.width = maxLength;
        });
    };
}
export default ExcelUtils;