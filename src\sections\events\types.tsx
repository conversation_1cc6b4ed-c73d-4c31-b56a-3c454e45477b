// ----------------------------------------------------------------------

export type GetEvent = {
    eventId: string;
    eventName: string;
    eventCategory: string;
    eventDescription: string,
    eventStartDateTime: number;
    eventEndDateTime: number;
    saleStartDateTimeForDisplay: number;
    saleEndDateTimeForDisplay: number;
    isPrivate: boolean;
    publish: boolean;
    publishDateTime: number;
    publishDetailsDateTime: number;
    isListDetails: boolean,
    alertThreshold: number;
    totalSupply: number;
    acceptCurrency: string;
    eventTimeZone: string;
    eventLocation: string;
};

export type UpdateEvent = GetEvent;