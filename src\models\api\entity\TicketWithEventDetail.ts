interface TicketWithEventDetail {
    ticketId: string;
    eventId: string;
    eventCategory: string;
    eventName: string;
    eventDescription: string;
    eventStartDateTime: number;
    eventEndDateTime: number;
    eventTimeZone: string;
    eventFloorPlanSeatId: string;
    eventPreviewContent: string;
    eventSessionId: string;
    eventSessionBundleId: string;
    eventBundleName: string;
    sessionStartDateTime: number;
    sessionEndDateTime: number;
    ticketToken: string;
    qrCodeToken: string;
    metadata: string;
    remark: string;
    usedPermissions: string[];
}
export default TicketWithEventDetail;