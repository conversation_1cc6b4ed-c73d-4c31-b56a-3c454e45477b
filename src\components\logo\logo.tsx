import { forwardRef } from 'react';

import Link from '@mui/material/Link';
import Box, { BoxProps } from '@mui/material/Box';

import { RouterLink } from 'src/routes/components';

// ----------------------------------------------------------------------

export interface LogoProps extends BoxProps {
    disabledLink?: boolean;
}

const Logo = forwardRef<HTMLDivElement, LogoProps>(
    ({ disabledLink = false, sx, ...other }, ref) => {
        const logo = (
            <Box
                component="img"
                src="https://assets.incutix.com/logo.png"
                sx={{ width: 100, height: 'auto', cursor: 'pointer', ...sx }}
            />
        );

        if (disabledLink) {
            return logo;
        }

        return (
            <Link component={RouterLink} href="/" sx={{ display: 'contents' }}>
                {logo}
            </Link>
        );
    }
);

export default Logo;
