import { hash } from 'stylis';
import React, { useMemo } from 'react';
import { type MRT_RowData } from 'material-react-table';
import { useSaveInputValueToRowCache } from '@/hooks/utils/use-save-input-row-cache';

import InputLabel from '@mui/material/InputLabel';
import FormControl from '@mui/material/FormControl';

import RichTextEditor from 'src/components/editor';

import { CustomTextFieldProps } from '../custom-text-field/types';
import styles from './mrt-markdown-edit-cell-text-field.module.scss';

const MRT_MarkdownEditCellTextField = <TData extends MRT_RowData>({
    cell,
    table,
    ...rest
}: CustomTextFieldProps<TData>) => {
    const {
        getState,
        options: { createDisplayMode, editDisplayMode },
        refs: { editInputRefs },
    } = table;
    const { column, row } = cell;
    const { columnDef } = column;
    const { creatingRow, editingRow } = getState();

    const isCreating = creatingRow?.id === row.id;
    const isEditing = editingRow?.id === row.id;
    const isSelectEdit = columnDef.editVariant === 'select';

    const saveInputValueToRowCache = useSaveInputValueToRowCache();
    const fieldId = useMemo(() => `field-${columnDef.header.replace(' ', "_")}`, [columnDef.header]);

    // Reminder: cell may not have detected for change when using memo, please keep it;
    const defaultValue = cell.getValue<string>() ?? "";
    const componentKey = hash(defaultValue, 16);

    console.log(editInputRefs);

    const handleChange = (value: string) => {
        console.log('changed');
        saveInputValueToRowCache({
            newValue: value, 
            element: {
                props: {
                    cell,
                    table,
                },
                type: '',
                key: null
            }
        });
    };

    /*
    const handleBlur = (event: FocusEvent<HTMLInputElement>) => {
        saveInputValueToRowCache(value);
        setEditingCell(null);
    };

    const handleEnterKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter' && !event.shiftKey) {
            editInputRefs.current[column.id]?.blur();
        }
    };
    */

    return (
        <FormControl>
            <InputLabel htmlFor={fieldId} sx={{
                transform: 'none'
            }}>
                {
                    ['custom', 'modal'].includes(
                        (isCreating ? createDisplayMode : editDisplayMode) as string,
                    )
                        ? columnDef.header
                        : undefined
                }
            </InputLabel>
            <RichTextEditor 
                key={componentKey} 
                defaultValue={defaultValue} 
                editContainerClasses={styles.mrtMarkdownEditor}
                onChange={handleChange}
            />
        </FormControl>
    );
};

export default MRT_MarkdownEditCellTextField;