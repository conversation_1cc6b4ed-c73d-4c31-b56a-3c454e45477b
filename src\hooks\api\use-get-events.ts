import <PERSON><PERSON><PERSON>ND from "@/constants/config/backend";
import { keepPreviousData } from "@tanstack/react-query";
import ADMIN_ENDPOINTS from "@/models/api/admin-endpoints";
import GetAllEventsAPIResult from "@/models/api/result/events/GetAllEvents";

const useGetEvents = (pageNum: number, pageSize: number) => {
    const result = BACKEND.Gateway.useQuery<GetAllEventsAPIResult>({
        url: `${ADMIN_ENDPOINTS.GetAllEventsByPage(pageNum)}`,
        query: {
            category: "EXPO",
            page_size: `${pageSize}`
        },
        params: {
            queryKey: `events-${pageNum}-${pageSize}`,
        },
        placeholderData: keepPreviousData,
        refetchOnWindowFocus: false,
    });
    return result.data?.data;
};
export default useGetEvents;