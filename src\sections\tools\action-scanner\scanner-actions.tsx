import React, { useState, useCallback } from 'react';
import { useBoolean } from '@/hooks/utils/use-boolean';
import { EnumTicketAction } from '@/enum/EnumTicketAction';
import useInsertTicketActions from '@/hooks/api/use-insert-ticket-actions';
import { EnumTicketActionPermission } from '@/enum/EnumTicketActionPermission';

import CancelIcon from '@mui/icons-material/Cancel';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import {
    Card,
    Stack,
    Backdrop,
    Typography,
    CardActionArea,
    CircularProgress
} from '@mui/material';

import ScannerDialog from './scanner-dialog';

interface ScannerActionsProps {
    qrCodeToken: string;
    permission: EnumTicketActionPermission;
    onAfterAction: () => void;
}

export default function ScannerActions(props: ScannerActionsProps) {
    const { qrCodeToken, permission, onAfterAction } = props;
    const [isDoingAction, setIsDoingAction] = useState(false);

    const successDialog = useBoolean();
    const failedDialog = useBoolean();

    const insertTicketActionsAsync = useInsertTicketActions();
    const onTicketAction = useCallback((actionName: string) => async (evt: React.MouseEvent<HTMLButtonElement>) => {
        evt.preventDefault();
        setIsDoingAction(true);
        const target = evt.target as HTMLElement;
        const action = actionName || target.dataset.action!;
        const _success = await insertTicketActionsAsync({ qrCodeToken, permission, action });
        setIsDoingAction(false);
        if (_success) {
            successDialog.onTrue();
        } else {
            failedDialog.onTrue();
        }
    }, [insertTicketActionsAsync, qrCodeToken, permission, successDialog, failedDialog]);

    return (
        <>
            <Stack spacing={{ xs: 1, sm: 2 }} direction="row" alignItems="center" gap={1} flexWrap="wrap" justifyContent="center" justifyItems="center">
                <Card>
                    <CardActionArea
                        data-action={EnumTicketAction.ALLOW}
                        onClick={onTicketAction('ALLOW')}
                        sx={{
                            p: 2,
                            bgcolor: 'success.main',
                            color: 'white',
                            width: '40vw',
                            height: '40vw',
                        }}
                    >
                        <div tabIndex={-1}>
                            <Stack direction="column" alignItems="center">
                                <CheckCircleIcon />
                                <Typography textAlign="center">
                                    Allow
                                </Typography>
                            </Stack>
                        </div>
                    </CardActionArea>
                </Card>
                <Card>
                    <CardActionArea
                        data-action={EnumTicketAction.DENY}
                        onClick={onTicketAction('DENY')}
                        sx={{
                            p: 2,
                            bgcolor: 'error.main',
                            color: 'white',
                            width: '40vw',
                            height: '40vw',
                        }}
                    >
                        <div tabIndex={-1}>
                            <Stack direction="column" alignItems="center">
                                <CancelIcon />
                                <Typography textAlign="center">
                                    Reject
                                </Typography>
                            </Stack>
                        </div>
                    </CardActionArea>
                </Card>
            </Stack>

            <Backdrop
                sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
                open={isDoingAction}
            >
                <CircularProgress color="inherit" />
            </Backdrop>
            <ScannerDialog message="Success" state={successDialog} onScanNext={onAfterAction} />
            <ScannerDialog message="Error" state={failedDialog} onScanNext={onAfterAction} />
        </>
    )
};