import dayjs, { Dayjs } from "dayjs";
import { ReactElement } from "react";
import {
    type MRT_RowData,
} from "material-react-table";

interface useSaveInputValueToRowCacheProps<TData extends MRT_RowData> {
    newValue: string | Dayjs | TData | null;
    element: ReactElement;
}

export const useSaveInputValueToRowCache = <TData extends MRT_RowData>() => 
    (props: useSaveInputValueToRowCacheProps<TData>) => {
        const { 
            newValue, 
            element
        } = props;

        const {
            cell,
            table
        } = element.props;

        const {
            getState,
            setCreatingRow,
            setEditingRow,
        } = table;
        const { column, row } = cell;
        const { creatingRow, editingRow } = getState();

        const columnId = column.id;
        const isCreating = creatingRow?.id === row.id;
        const isEditing = editingRow?.id === row.id;

        if (dayjs.isDayjs(newValue)) {
            // @ts-ignore
            row._valuesCache[columnId] = newValue.format("YYYY-MM-DD HH:mm (Z)");
        } else {
            // @ts-ignore
            row._valuesCache[columnId] = newValue;
        }
        if (isCreating) {
            setCreatingRow(row);
        } else if (isEditing) {
            setEditingRow(row);
        }
    }