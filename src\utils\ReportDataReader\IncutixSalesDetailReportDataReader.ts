import ExcelJS from "exceljs";
import { Buffer } from "buffer";
import { NumberRegex } from "@/constants/common";

import ReportDataReader from "./index";

type IncutixRawRecord = {
    "PaymentOrderId": string;
    "PaymentGatewayOrderId": string;
    "DateTime": string;
    "Email": string;
    "Nickname": string;
    "Currency": string;
    "UnitPrice": number;
    "ItemName": string;
    "Quantity": number;
    "EventName": string;
    "SessionStartDateTime": number;
    "SessionEndDateTime": number;
    "PaymentState": string;
    "PromotionCode": string;
}
interface GroupedIncutixRecord {
    "PaymentOrderId": string;
    "PaymentGatewayOrderId": string;
    "DateTime": string;
    "Email": string;
    "Nickname": string;
    "Currency": string;
    "EventName": string;
    "PaymentState": string;
    "PromotionCode": string;
    "Items": {
        "UnitPrice": number;
        "ItemName": string;
        "Quantity": number;
        "SessionStartDateTime": number;
        "SessionEndDateTime": number;
    }[]
}
class IncutixSalesDetailReportDataReader extends ReportDataReader<GroupedIncutixRecord> {
    GetIds(): string[] {
        return this.rawRecords.map(item => item.PaymentOrderId);
    }

    GetRecordById(id: string): GroupedIncutixRecord | null | undefined {
        return this.rawRecords.find(item => item.PaymentOrderId === id);
    }

    // eslint-disable-next-line class-methods-use-this
    async ExtractDatasetFromContent(dataUriContent: string): Promise<GroupedIncutixRecord[]> {
        /*
        // Read CSV
        const workbook = new ExcelJS.Workbook();
        const base64Content = dataUriContent.split(',')[1];
        const buffer = Buffer.from(base64Content, 'base64');
        const content = buffer.toString('utf-8');
        const rows = content.split('\n');
        const data = rows.map((row) => row.split(',')).filter(row => !(row.length === 1 && row[0].trim().length === 0));
        const worksheet = workbook.addWorksheet('Sheet 1');
        worksheet.addRows(data);
        */
        const base64Data = dataUriContent.split(';base64,').pop();
        if (!base64Data) {
            throw new Error("Unable to find Transaction Details (Format invalid)");
        }
        const buffer = Buffer.from(base64Data, 'base64');
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(buffer);
        const worksheet = workbook.worksheets[0];
        let headerRowNumber: number = -1;
        let datasetRowNumber: number = -1;
        let datasetColumnCount: number = 0;
        for (let i = 1; i <= worksheet.rowCount; i += 1) {
            const cellValue = worksheet.getCell(`A${i}`).value;
            if (cellValue === "PaymentOrderId") {
                headerRowNumber = i;
                datasetRowNumber = i + 1;
                datasetColumnCount = worksheet.getRow(headerRowNumber).cellCount;
                break;
            }
            if (i === worksheet.rowCount) {
                throw new Error("Unable to find Transaction Details (Format invalid)");
            }
        }
        const rawRecords: IncutixRawRecord[] = [];
        for (let i = datasetRowNumber; i <= worksheet.rowCount; i++) {
            const rawRecord: { [key in string]: unknown } = {};
            for (let j = 1; j <= datasetColumnCount; j++) {
                const columnName = worksheet.getRow(headerRowNumber!).getCell(j).value!.toString() as keyof IncutixRawRecord;
                const cellValue = worksheet.getRow(i).getCell(j).value!.toString();
                if (cellValue) {
                    rawRecord[columnName] = NumberRegex.test(cellValue) ? parseFloat(cellValue) : cellValue;
                }
            }
            rawRecords.push(rawRecord as IncutixRawRecord);
        }
        const grouped: GroupedIncutixRecord[] = Object.values(rawRecords.reduce((result: { [key: string]: GroupedIncutixRecord }, item) => {
            const {
                PaymentOrderId,
                PaymentGatewayOrderId,
                DateTime,
                Email,
                Nickname,
                Currency,
                EventName,
                PaymentState,
                PromotionCode,
                ...rest
            } = item;
            const key = PaymentOrderId;
            if (result[key]) {
                result[key].Items.push(rest);
            } else {
                result[key] = {
                    PaymentOrderId,
                    PaymentGatewayOrderId,
                    DateTime,
                    Email,
                    Nickname,
                    Currency,
                    EventName,
                    PaymentState,
                    PromotionCode,
                    Items: [rest],
                };
            }
            return result;
        }, {}));

        this.rawRecords = grouped;
        return grouped;
    }

}
export default IncutixSalesDetailReportDataReader;
