import dayjs, { Dayjs } from "dayjs";
import { 
    type MRT_RowData, 
    getValueAndLabel,
    parseFromValuesOrFunc
} from "material-react-table";
import 
React,
{ 
    useState,
    type FocusEvent,
    type ChangeEvent,
    type KeyboardEvent,
    useImperativeHandle,
    useMemo
} from "react";

import { Box } from "@mui/system";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { Paper, MenuItem, TextField, TextFieldProps } from "@mui/material";

import { CustomTextFieldProps } from "./types";

export type CustomTextFieldRef = {
    setValue: React.Dispatch<React.SetStateAction<string | dayjs.Dayjs | null>>;
};

const CustomTextField = <TData extends MRT_RowData>({
    cell,
    table,
    ...rest
}: CustomTextFieldProps<TData>, ref: React.ForwardedRef<CustomTextFieldRef>) => {
    const {
        getState,
        options: { createDisplayMode, editDisplayMode, muiEditTextFieldProps },
        refs: { editInputRefs },
        setCreatingRow,
        setEditingCell,
        setEditingRow,
    } = table;
    const { column, row } = cell;
    const { columnDef } = column;
    const { creatingRow, editingRow } = getState();

    const isCreating = creatingRow?.id === row.id;
    const isEditing = editingRow?.id === row.id;
    const isSelectEdit = columnDef.editVariant === 'select';

    const [value, setValue] = useState(() => cell.getValue<string | Dayjs | null>());

    useImperativeHandle(ref, () => ({ setValue }));

    const textFieldProps: TextFieldProps = {
        ...parseFromValuesOrFunc(muiEditTextFieldProps, {
            cell,
            column,
            row,
            table,
        }),
        ...parseFromValuesOrFunc(columnDef.muiEditTextFieldProps, {
            cell,
            column,
            row,
            table,
        }),
        ...rest,
    };

    const saveInputValueToRowCache = (newValue: string | Dayjs | null) => {
        if (dayjs.isDayjs(newValue)) {
            // @ts-ignore
            row._valuesCache[column.id] = newValue.format("YYYY-MM-DD HH:mm (Z)");
        } else {
            // @ts-ignore
            row._valuesCache[column.id] = newValue;
        }
        if (isCreating) {
            setCreatingRow(row);
        } else if (isEditing) {
            setEditingRow(row);
        }
    };

    const handleChangeDatetimePicker = (newValue: Dayjs | null) => {
        setValue(newValue);
        saveInputValueToRowCache(newValue);
    };

    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
        setValue(event.target.value);
        saveInputValueToRowCache(event.target.value);
    };

    const handleBlur = (event: FocusEvent<HTMLInputElement>) => {
        saveInputValueToRowCache(value);
        setEditingCell(null);
    };

    const handleEnterKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter' && !event.shiftKey) {
            editInputRefs.current[column.id]?.blur();
        }
    };
    const columnContent = useMemo(() => {
        if (!columnDef.Edit) {
            return null;
        }
        return <>{columnDef.Edit?.({ cell, column, row, table })}</>;
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [columnDef, cell, column, row, table, value]);

    if (columnContent) {
        return columnContent;
    }
    if (dayjs(value).isValid()) {
        return (
            <Paper>
                <DateTimePicker
                    label={columnDef.header} 
                    value={dayjs(value)} 
                    onChange={handleChangeDatetimePicker}
                />
            </Paper>
        );
    }

    return (
        <Box mb={2}>
            <TextField
                disabled={parseFromValuesOrFunc(columnDef.enableEditing, row) === false}
                fullWidth
                inputRef={(inputRef) => {
                    if (inputRef) {
                        editInputRefs.current[column.id] = inputRef;
                        if (textFieldProps.inputRef) {
                            textFieldProps.inputRef = inputRef;
                        }
                    }
                }}
                label={
                    ['custom', 'modal'].includes(
                        (isCreating ? createDisplayMode : editDisplayMode) as string,
                    )
                    ? columnDef.header
                    : undefined
                }
                margin="none"
                name={column.id}
                placeholder={
                    !['custom', 'modal'].includes(
                        (isCreating ? createDisplayMode : editDisplayMode) as string,
                    )
                    ? columnDef.header
                    : undefined
                }
                select={isSelectEdit}
                size="small"
                value={value}
                variant="standard"
                {...textFieldProps}
                InputProps={{
                    ...(textFieldProps.variant !== 'outlined'
                        ? { disableUnderline: editDisplayMode === 'table' }
                        : {}),
                    ...textFieldProps.InputProps,
                    sx: (theme) => ({
                        mb: 0,
                        ...(parseFromValuesOrFunc(
                            textFieldProps?.InputProps?.sx,
                            theme,
                        ) as any),
                    }),
                }}
                inputProps={{
                    autoComplete: 'new-password', // disable autocomplete and autofill
                    ...textFieldProps.inputProps,
                }}
                onBlur={handleBlur}
                onChange={handleChange}
                onClick={(e) => {
                    e.stopPropagation();
                    textFieldProps?.onClick?.(e);
                }}
                onKeyDown={handleEnterKeyDown}
                >
                {textFieldProps.children ??
                    columnDef?.editSelectOptions?.map((option) => {
                    const { label, value: selectValue } = getValueAndLabel(option);
                    return (
                        <MenuItem
                            key={selectValue}
                            sx={{
                                alignItems: 'center',
                                display: 'flex',
                                gap: '0.5rem',
                                m: 0,
                            }}
                            value={selectValue}
                        >
                            {label}
                        </MenuItem>
                    );
                    })}
                </TextField>
        </Box>
    );
};

const ForwardedCustomTextField = React.forwardRef(CustomTextField);

export default React.memo(ForwardedCustomTextField);