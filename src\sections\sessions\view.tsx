'use client';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { useMemo, useState, useCallback } from 'react';

import { Container, Typography,Button} from '@mui/material';
import { useSettingsContext } from 'src/components/settings';
import { PaginationDataTable } from '@/components/datatable';
import AddIcon from "@mui/icons-material/Add";
import {type MRT_ColumnDef, type MRT_TableInstance } from 'material-react-table';
import { useSearchParams } from '@/routes/hooks';
import useGetEventAllSessionBundle from '@/hooks/api/use-get-event-session-bundle';
import { LiteralUnion } from 'react-hook-form';

import { type GetSession } from './types';

dayjs.extend(utc);
dayjs.extend(timezone);

type Props = {
    sid: string;
};

export default function EventView({ sid }: Props) {
    const settings = useSettingsContext();
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: 10,
    });
    const searchParams = useSearchParams()
    const name = searchParams.get('name')
    const id = searchParams.get('eventId') || '';
    console.log(id, sid)
    const apiData = useGetEventAllSessionBundle(id, sid, pagination.pageIndex + 1, pagination.pageSize);
    const onCreatingRowSave = useCallback(({ table, values }: { table: MRT_TableInstance<GetSession>, values: Record<LiteralUnion<keyof GetSession, string>, any> }) => {
        // validate data
        // save data to api
        table.setCreatingRow(null); // exit creating mode
    }, []);

    const CreateButton = useCallback((props: { table: MRT_TableInstance<GetSession> }) => (
        <Button color="primary" startIcon={<AddIcon />} onClick={() => props.table.setCreatingRow(true)}>
            Create New Event Session
        </Button>
    ), []);
    const columns: Array<MRT_ColumnDef<GetSession>> = useMemo(() => [
        {
            accessorKey: 'eventId',
            header: 'Event Id (Auto-generated)',
            enableSorting: false,
            enableClickToCopy: true,
            enableEditing: false
        },
        {
            accessorKey: 'eventSessionId',
            header: 'Event Session Id (Auto-generated)',
            enableSorting: false,
            enableClickToCopy: true,
            enableEditing: false
        },
        {
            accessorKey: 'eventSessionBundleId',
            header: 'Event Session Bundle Id (Auto-generated)',
            enableSorting: false,
            enableClickToCopy: true,
            enableEditing: false
        },
        {
            accessorKey: 'eventBundleId',
            header: 'Event Bundle Id (Auto-generated)',
            enableSorting: false,
            enableClickToCopy: true,
            enableEditing: false
        },
        {
            accessorKey: 'eventName',
            header: 'Event Name',
            enableSorting: true
        },
        {
            accessorKey: 'eventBundleName',
            header: 'Event Bundle Name',
            enableSorting: true
        },
        {
            accessorKey: 'eventTimeZone',
            header: 'Event Time Zone',
            enableSorting: true
        },
        {
            accessorKey: 'displayPriority',
            header: 'Display Priority',
            enableSorting: true
        },
        {
            accessorKey: 'price',
            header: 'Price',
            enableSorting: false
        },
        {
            accessorKey: 'forSell',
            header: 'For Sell',
            enableSorting: false
        },
        {
            accessorKey: 'soldOut',
            header: 'Sold Out',
            enableSorting: false
        },
        {
            accessorKey: "saleEndDateTime",
            accessorFn: (originalRow: GetSession) => dayjs.utc(originalRow.saleEndDateTime).tz(originalRow.eventTimeZone).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Sale Start Date Time (HKT)",
            enableSorting: true
        },
        {
            accessorKey: "saleStartDateTime",
            accessorFn: (originalRow: GetSession) => dayjs.utc(originalRow.saleStartDateTime).tz(originalRow.eventTimeZone).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Sale End Date Time (HKT)",
            enableSorting: true
        },
        {
            accessorKey: "sessionStartDateTime",
            accessorFn: (originalRow: GetSession) => dayjs.utc(originalRow.sessionStartDateTime).tz(originalRow.eventTimeZone).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Session Start Date Time (HKT)",
            enableSorting: true
        },
        {
            accessorKey: "sessionEndDateTime",
            accessorFn: (originalRow: GetSession) => dayjs.utc(originalRow.sessionEndDateTime).tz(originalRow.eventTimeZone).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Session End Date Time (HKT)",
            enableSorting: true
        }
    ], []);

    return (
        <Container maxWidth={settings.themeStretch ? false : 'xl'}>
            <Typography variant="h4"> Events : {name} </Typography>
            <PaginationDataTable
                columns={columns}
                data={apiData?.list}
                rowCount={apiData?.total}
                enableEditing
                editDisplayMode="modal"
                createDisplayMode="modal"
                onCreatingRowSave={onCreatingRowSave}
                renderTopToolbarCustomActions={CreateButton}
                enableSelectAll={false}
                enableRowSelection={false}
                manualPagination
                initialState={{
                    columnVisibility: {
                        eventDescription: false,
                        eventLocation: false,
                        saleStartDateTimeForDisplay: false,
                        saleEndDateTimeForDisplay: false,
                        alertThreshold: false,
                        totalSupply: false,
                    }
                }}
                state={{
                    pagination
                }}
                onPaginationChange={setPagination}
                // onEditingRowSave={handleSaveEvent}
                muiTableBodyRowProps={({ row }) => ({
                    onClick: () => {
                        row.getToggleSelectedHandler();
                        // router.push(`/dashboard/events/${row.original.eventId}/${row.original.eventSessionId}/?name=${eventName}`)
                        console.log("Row Clicked:", row.original);
                    },
                    sx: { cursor: 'pointer' },
                })}
            />
        </Container>
    );
}
