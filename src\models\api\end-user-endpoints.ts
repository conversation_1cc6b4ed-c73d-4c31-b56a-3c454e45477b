import BACKEND from '@/constants/config/backend';

class END_USER_ENDPOINTS {
    static version = 'v1';

    static baseUrl: string = `${BACKEND.INTERNAL_API_ENDPOINT}/${this.version}`;

    public static readonly Login = () => `${this.baseUrl}/user/login`;

    public static readonly Logout = () => `${this.baseUrl}/user/logout`;

    public static readonly Register = () => `${this.baseUrl}/user/register`;

    public static readonly TrendingEventByPage = (page: number) => `${this.baseUrl}/events/trending/${page}`;

    public static readonly PastEventByPage = (page: number) => `${this.baseUrl}/events/past/${page}`;

    public static readonly RecentEventByPage = (page: number) => `${this.baseUrl}/events/recent/${page}`;

    public static readonly EventDetilsById = (eventId: string) => `${this.baseUrl}/events/${eventId}`;

    public static readonly SessionDetail = (eventSessionId: string) => `${this.baseUrl}/events/sessions/${eventSessionId}`;

    public static readonly SessionByEventIdDate = (eventId: string, date: string) => `${this.baseUrl}/events/${eventId}/sessions?date=${date}`;

    public static readonly SessionBundlesByEventIdDate = (eventId: string, date: string) => `${this.baseUrl}/events/${eventId}/sessions/bundles?date=${date}`;

    public static readonly OrderPayment = () => `${this.baseUrl}/order`;

    public static readonly requestEmailVerificationCode = () => `${this.baseUrl}/user/email/verify/request`;

    public static readonly confirmEmailVerificationCode = () => `${this.baseUrl}/user/email/verify/confirm`;

    public static readonly forgotPasswordCode = () => `${this.baseUrl}/user/password/forgot/request`;

    public static readonly forgotPassword = () => `${this.baseUrl}/user/password/forgot/confirm`;

    public static readonly changePassword = () => `${this.baseUrl}/user/password/change`;

    public static readonly GetUserInfo = () => `${this.baseUrl}/user/info`;

    public static readonly redeemTicket = () => `${this.baseUrl}/user/events/redeem`;

    public static readonly GetUserEventsByTicketStatus = () => `${this.baseUrl}/user/events`;

    public static readonly GetUserOrder = () => `${this.baseUrl}/user/orders/paid`;

    public static readonly GetUserOrdersByStatus = () => `${this.baseUrl}/user/orders`;

    public static readonly GetShoppingCart = () => `${this.baseUrl}/user/shoppingCart`;

    public static readonly UpdateShoppingCartItem = () => `${this.baseUrl}/user/shoppingCart`;

    public static readonly DeleteShoppingCartItem = (eventSessionBundleId: string) => `${this.baseUrl}/user/shoppingCart/${eventSessionBundleId}`;

    public static readonly DeleteAllShoppingCartItem = () => `${this.baseUrl}/user/shoppingCart`;

    public static readonly GetOrderById = (id: string) => `${this.baseUrl}/order/${id}`;

    public static readonly onShow = () => `${this.baseUrl}/myEvent/onshow`;

    private static readonly _GetSaleReport = `${this.baseUrl}/admin/reports/sales`;

    public static readonly GetSaleReport = () => this._GetSaleReport;
}
export default END_USER_ENDPOINTS;
