import { useMemo } from 'react';
import { useAuthContext } from '@/auth/hooks';
import { EnumUserPermission } from '@/enum/EnumUserPermission';

import EventIcon from '@mui/icons-material/Event';
import BuildOutlinedIcon from '@mui/icons-material/BuildOutlined';
import AssessmentOutlinedIcon from '@mui/icons-material/AssessmentOutlined';

import { paths } from 'src/routes/paths';

import SvgColor from 'src/components/svg-color';

// ----------------------------------------------------------------------

const icon = (name: string) => (
    <SvgColor src={`/assets/icons/navbar/${name}.svg`} sx={{ width: 1, height: 1 }} />
    // OR
    // <Iconify icon="fluent:mail-24-filled" />
    // https://icon-sets.iconify.design/solar/
    // https://www.streamlinehq.com/icons
);

const ICONS = {
    job: icon('ic_job'),
    blog: icon('ic_blog'),
    chat: icon('ic_chat'),
    mail: icon('ic_mail'),
    user: icon('ic_user'),
    file: icon('ic_file'),
    lock: icon('ic_lock'),
    tour: icon('ic_tour'),
    order: icon('ic_order'),
    label: icon('ic_label'),
    blank: icon('ic_blank'),
    kanban: icon('ic_kanban'),
    folder: icon('ic_folder'),
    banking: icon('ic_banking'),
    booking: icon('ic_booking'),
    invoice: icon('ic_invoice'),
    product: icon('ic_product'),
    calendar: icon('ic_calendar'),
    disabled: icon('ic_disabled'),
    external: icon('ic_external'),
    menuItem: icon('ic_menu_item'),
    ecommerce: icon('ic_ecommerce'),
    analytics: icon('ic_analytics'),
    dashboard: icon('ic_dashboard'),
};

// ----------------------------------------------------------------------

export function useNavData() {
    const { user } = useAuthContext();
    const data = useMemo(() => [
        // OVERVIEW
        // ----------------------------------------------------------------------
        {
            subheader: 'Management',
            items: [
                {
                    title: 'Overview',
                    path: paths.dashboard.root,
                    icon: ICONS.dashboard
                },
                ...(user?.permissions?.includes(EnumUserPermission.APPROVE_MEMBERSHIP_APPLICATIONS) ? [{
                    title: 'Members',
                    path: paths.dashboard.membership.root,
                    children: [
                        {
                            title: 'Application',
                            path: paths.dashboard.membership.application,
                        },
                    ],
                }] : []),
                ...(user?.permissions?.includes(EnumUserPermission.AES_TEST) ? [{
                    title: 'Events',
                    path: paths.dashboard.events,
                    icon: <EventIcon />,
                }] : []),
                ...(user?.permissions?.includes(EnumUserPermission.SCAN_TICKET) ? [{
                    title: 'Tools',
                    path: paths.dashboard.tools.root,
                    icon: <BuildOutlinedIcon />,
                    children: [
                        {
                            title: 'Admission (QR Code Scan)',
                            path: paths.dashboard.tools.scan.admission,
                        },
                        {
                            title: 'Reward (QR Code Scan)',
                            path: paths.dashboard.tools.scan.reward,
                        },
                    ]
                }] : []),
                {
                    title: 'Reports',
                    path: paths.dashboard.reports.root,
                    icon: <AssessmentOutlinedIcon />,
                    children: [
                        ...(user?.permissions?.includes(EnumUserPermission.GET_SALES_REPORT) ? [{
                            title: 'Sales Report',
                            path: paths.dashboard.reports.root,
                            children: [
                                ...(user?.permissions?.includes(EnumUserPermission.GET_SALES_REPORT) ? [{
                                    title: 'Summary Report',
                                    path: paths.dashboard.reports.sales.summary,
                                }] : []),
                                ...(user?.permissions?.includes(EnumUserPermission.GET_SALES_REPORT) ? [{
                                    title: 'Detail Report',
                                    path: paths.dashboard.reports.sales.detail,
                                }] : []),
                            ]
                        }] : []),

                        ...(user?.permissions?.includes(EnumUserPermission.GET_MEMBER_REPORT) ? [{
                            title: 'Member Report',
                            path: paths.dashboard.reports.member,
                        }] : []),
                        ...(user?.permissions?.includes(EnumUserPermission.GET_ADMISSION_REPORT) ? [{
                            title: 'Admission Report',
                            path: paths.dashboard.reports.admission,
                        }] : []),
                        ...(user?.permissions?.includes(EnumUserPermission.GET_SETTLEMENT_REPORT) ? [{
                            title: 'Settlement Report',
                            path: paths.dashboard.reports.settlement,
                        }] : []),
                    ]
                }
            ],
        }
    ], [user?.permissions]);

    return data;
}
