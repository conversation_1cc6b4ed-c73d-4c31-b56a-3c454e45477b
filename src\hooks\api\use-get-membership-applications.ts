import <PERSON><PERSON><PERSON><PERSON> from "@/constants/config/backend";
import { keepPreviousData } from "@tanstack/react-query";
import ADMIN_ENDPOINTS from "@/models/api/admin-endpoints";
import GetMembershipApplicationsAPIResult from "@/models/api/result/memberships/GetMembershipApplications";

const useGetMembershipApplications = (state: string, pageNum: number, pageSize: number) => 
    BACKEND.Gateway.useQuery<GetMembershipApplicationsAPIResult>({
        url: `${ADMIN_ENDPOINTS.GetMembershipApplications()}`,
        query: {
            state,
            page: `${pageNum}`,
            page_size: `${pageSize}`
        },
        params: {
            queryKey: `membership-applications-${pageNum}-${pageSize}`,
        },
        placeholderData: keepPreviousData,
        refetchOnWindowFocus: false,
    });
export default useGetMembershipApplications;