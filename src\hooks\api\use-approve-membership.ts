import <PERSON><PERSON><PERSON>ND from "@/constants/config/backend";
import EnumRequestHeader from "@/enum/EnumRequestHeader";
import { RequestMethod } from "@/enum/EnumRequestMethod";
import ADMIN_ENDPOINTS from "@/models/api/admin-endpoints";
import ApproveMembershipApplicationAPIResult from "@/models/api/result/memberships/ApproveMembershipApplication";
import API from "@/utils/api";

export const useApproveMembership = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async (subscriptionIds: string[], action: string) => {
        const formDataObject = {};
        const modifiedFormDataObject = subscriptionIds.reduce((obj: { [key: string]: unknown; }, subscribeId, index) => {
            obj[`membershipSubscriptionIdList[${index}].MembershipSubscriptionId`] = subscribeId;
            obj[`membershipSubscriptionIdList[${index}].Action`] = action;
            return obj;
        }, formDataObject);
        return fetchAsync<ApproveMembershipApplicationAPIResult>(
            ADMIN_ENDPOINTS.ApproveMembershipApplication(),
            {
                method: RequestMethod.POST,
                data: API.ToFormData(modifiedFormDataObject)
            }
        );
    };
    return requestAsync;
};