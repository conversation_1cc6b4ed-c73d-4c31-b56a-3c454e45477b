'use client';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { LiteralUnion } from 'react-hook-form';
import { useMemo, useState, ReactNode, useCallback } from 'react';
import { MRT_Row, type MRT_ColumnDef, type MRT_TableInstance, MRT_TableOptions } from 'material-react-table';
import AddIcon from "@mui/icons-material/Add";
import {
    Button,

} from '@mui/material';
import { PaginationDataTable } from 'src/components/datatable';
import useGetEventAllSession from '@/hooks/api/use-get-event-session';
import { useRouter } from '@/routes/hooks';

import { type GetSession } from './types';



dayjs.extend(utc);
dayjs.extend(timezone);

type Props = {
    id: string;
    eventName:string | null;
};

export default function SessionTableView({ id,eventName }: Props) {
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: 10,
    });
    const router = useRouter();

    const columns: Array<MRT_ColumnDef<GetSession>> = useMemo(() => [
        {
            accessorKey: 'eventId',
            header: 'Event Id (Auto-generated)',
            enableSorting: false,
            enableClickToCopy: true,
            enableEditing: false
        },
        {
            accessorKey: 'eventSessionId',
            header: 'Event Session Id (Auto-generated)',
            enableSorting: false,
            enableClickToCopy: true,
            enableEditing: false
        },
        {
            accessorKey: 'sessionType',
            header: 'Event Session Type',
            enableSorting: true
        },
        {
            accessorKey: 'sessionDescription',
            header: 'Event Session Description',
            enableSorting: true
        },
        {
            accessorKey: 'sessionTimeZone',
            header: 'Event Session Time Zone',
            enableSorting: false
        },
        {
            accessorKey: "sessionStartDateTime",
            accessorFn: (originalRow: GetSession) => dayjs.utc(originalRow.sessionStartDateTime).tz(originalRow.sessionTimeZone).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Session Start Date Time (HKT)",
            enableSorting: true
        },
        {
            accessorKey: "sessionEndDateTime",
            accessorFn: (originalRow: GetSession) => dayjs.utc(originalRow.sessionEndDateTime).tz(originalRow.sessionTimeZone).format("YYYY-MM-DD HH:mm (Z)"),
            header: "Session End Date Time (HKT)",
            enableSorting: true
        }
    ], []);



    const apiData = useGetEventAllSession(id, pagination.pageIndex + 1, pagination.pageSize);

    const onCreatingRowSave = useCallback(({ table, values }: { table: MRT_TableInstance<GetSession>, values: Record<LiteralUnion<keyof GetSession, string>, any> }) => {
        // validate data
        // save data to api
        table.setCreatingRow(null); // exit creating mode
    }, []);

    const CreateButton = useCallback((props: { table: MRT_TableInstance<GetSession> }) => (
        <Button color="primary" startIcon={<AddIcon />} onClick={() => props.table.setCreatingRow(true)}>
            Create New Event Session
        </Button>
    ), []);



    return (
        <PaginationDataTable
            columns={columns}
            data={apiData?.list}
            rowCount={apiData?.total}
            enableEditing
            editDisplayMode="modal"
            createDisplayMode="modal"
            onCreatingRowSave={onCreatingRowSave}
            renderTopToolbarCustomActions={CreateButton}
            enableSelectAll={false}
            enableRowSelection={false}
            manualPagination
            initialState={{
                columnVisibility: {
                    eventDescription: false,
                    eventLocation: false,
                    saleStartDateTimeForDisplay: false,
                    saleEndDateTimeForDisplay: false,
                    alertThreshold: false,
                    totalSupply: false,
                }
            }}
            state={{
                pagination
            }}
            onPaginationChange={setPagination}
            // onEditingRowSave={handleSaveEvent}
            muiTableBodyRowProps={({ row }) => ({
                onClick: () => {
                    row.getToggleSelectedHandler();
                    router.push(`/dashboard/sessions/${row.original.eventSessionId}/?name=${eventName}&eventId=${row.original.eventId}`)
                    console.log("Row Clicked:", row.original);
                },
                sx: { cursor: 'pointer' },
            })}
        />
    );
}
