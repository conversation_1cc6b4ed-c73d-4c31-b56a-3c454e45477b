import Stepper from '@/components/stepper';
import TStep from '@/components/stepper/type';
import { useSnackbar } from '@/components/snackbar';
import useGetEvents from '@/hooks/api/use-get-events';
import { useMemo, useState, useCallback } from 'react';

import Select, { SelectChangeEvent } from '@mui/material/Select';
import {
    Paper,
    Button,
    MenuItem
} from '@mui/material';

import ExportButton from './export-button';


const StepContentAdmissionReport = () => {
    const { enqueueSnackbar } = useSnackbar();
    const events = useGetEvents(1, 9999);

    const [eventId, setEventId] = useState('');
    const [activeStep, setActiveStep] = useState<number>(0);

    const onEventInputChange = useCallback((e: SelectChangeEvent) => {
        setEventId(e.target.value as string);
    }, []);
    const handleStepperNext = useCallback(() => {
        if (activeStep === 0) {
            if (eventId === '') {
                enqueueSnackbar("Missing event id", { variant: "error" });
                return;
            }
        }
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }, [activeStep, eventId, enqueueSnackbar]);

    const handleStepperBack = useCallback(() => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    }, []);

    const handleStepperReset = useCallback(() => {
        setEventId('');
        setActiveStep(0);
    }, []);

    const steps: TStep[] = useMemo(() => events ? [
        {
            label: 'Select event',
            description: `Select an event that you would like to have a report`,
            component:
                <Select
                    labelId="event-selector"
                    id="report-event-selector"
                    onChange={onEventInputChange}
                    value={eventId}
                    displayEmpty
                    MenuProps={{
                        anchorOrigin: {
                            vertical: "bottom",
                            horizontal: "right"
                        },
                    }}
                >
                    <MenuItem disabled value="">
                        <em>Please Select Event</em>
                    </MenuItem>

                    {events?.list?.map((event: any) => (
                        <MenuItem key={event.eventId} value={event.eventId}>{event.eventName}</MenuItem>
                    ))}
                </Select>,
        },
    ] : [], [eventId, events, onEventInputChange]);

    return (
        <>
            <Stepper activeStep={activeStep} steps={steps} onNext={handleStepperNext} onBack={handleStepperBack} />
            {activeStep === steps.length && (
                <Paper square elevation={0} sx={{ p: 3 }}>
                    <Button
                        variant='outlined'
                        color='warning'
                        onClick={handleStepperBack}
                        sx={{ mr: '2rem' }}
                    >
                        Back
                    </Button>
                    <ExportButton event={eventId} onFinished={handleStepperReset} />
                </Paper>
            )}
        </>
    )
}

export default StepContentAdmissionReport;
