import { APISuccessResult } from "@stoneleigh/api-lib";

import { ShoppingCartSummarizedItem } from "../../entity/ShoppingCart";

type FetchShoppingCartAPIResult = APISuccessResult<{
    eventName: string;
    items: ShoppingCartSummarizedItem[];
}>;
type UpdateShoppingCartItemAPIResult = APISuccessResult<null>;
type DeleteShoppingCartItemAPIResult = APISuccessResult<null>;
type DeleteAllShoppingCartItemAPIResult = APISuccessResult<null>;

export type { ShoppingCartSummarizedItem, FetchShoppingCartAPIResult, UpdateShoppingCartItemAPIResult, DeleteShoppingCartItemAPIResult, DeleteAllShoppingCartItemAPIResult };