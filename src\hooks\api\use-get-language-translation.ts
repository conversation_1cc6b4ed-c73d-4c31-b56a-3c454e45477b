import <PERSON><PERSON><PERSON><PERSON> from "@/constants/config/backend";
import EnumRequestHeader from "@/enum/EnumRequestHeader";
import ADMIN_ENDPOINTS from "@/models/api/admin-endpoints";
import GetLanguageTranslationsAPIResult from "@/models/api/result/languageTranslations/GetLanguageTranslations";

const useGetLanguageTranslation = () => {
    const abortController = new AbortController();
    const { signal } = abortController;
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION], signal });
    const requestAsync = async (params: { locale: string, pageNum?: Number, pageSize?: Number, categoryId?: string, translationKey?: string }) => fetchAsync<GetLanguageTranslationsAPIResult>(
        ADMIN_ENDPOINTS.GetLanguageTranslation(params.locale),
        {
            method: "GET",
            params
        }
    );
    return {
        abortController,
        requestAsync
    };
};
export default useGetLanguageTranslation;