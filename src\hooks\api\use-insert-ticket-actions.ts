import API from "@/utils/api";
import BACKEND from "@/constants/config/backend";
import EnumRequestHeader from "@/enum/EnumRequestHeader";
import ADMIN_ENDPOINTS from "@/models/api/admin-endpoints";
import InsertTicketActionsAPIResult from "@/models/api/result/events/InsertTicketActions";

const useInsertTicketActions = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async (params: { qrCodeToken: string, permission: string, action: string }) => {
        const { permission, ...restParams } = params;
        return fetchAsync<InsertTicketActionsAPIResult>(
            ADMIN_ENDPOINTS.InsertTicketActions(permission),
            {
                method: "POST",
                data: API.ToFormData(restParams)
            }
        );
    };
    return requestAsync;
}
export default useInsertTicketActions;