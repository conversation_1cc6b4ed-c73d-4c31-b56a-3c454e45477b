.setup_yarn:
  script:
    - yarn set version berry

.setup_git:
  script:
    - apk add --no-cache openssh-client git
    - mkdir /root/.ssh && chmod 0700 /root/.ssh
    - eval `ssh-agent -s`
    - ssh-add <(echo "$DEPLOYMENT_SSH_PRIVATE_KEY")
    - ssh-keyscan -H gitlab.com >> ~/.ssh/known_hosts
    - git config --global user.email $CI_EMAIL
    - git config --global user.name $CI_NAME
    - git remote remove origin || true
    - git remote add origin $CI_GIT_URI
    - git fetch

.install_aws:
  script:  
    - apk add --no-cache aws-cli

.aws_add_ecr_profile:
  script:
    - aws configure set aws_access_key_id $ECR_AWS_ACCESS_KEY_ID --profile $ECR_AWS_PROFILE
    - aws configure set aws_secret_access_key $ECR_AWS_SECRET_ACCESS_KEY --profile $ECR_AWS_PROFILE
    - aws configure set default.region $ECR_AWS_REGION --profile $ECR_AWS_PROFILE
    - aws configure set region $ECR_AWS_REGION --profile $ECR_AWS_PROFILE

.aws_login_k8s_cluster:
  script:
    - aws configure set aws_access_key_id $K8S_AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $K8S_AWS_SECRET_ACCESS_KEY
    - aws configure set default.region $K8S_AWS_REGION
    - aws configure set region $K8S_AWS_REGION
    - aws eks update-kubeconfig --name ${K8S_CLUSTER_NAME} --region ${K8S_AWS_REGION}
    - eksctl utils write-kubeconfig --cluster=${K8S_CLUSTER_NAME} --region=${K8S_AWS_REGION}
    
.push_git_tag:
  script:
    - !reference [.setup_yarn, script]
    - git checkout $GIT_TARGET_BRANCH
    - yarn dlx standard-version -t "$GIT_TAG_PREFIX-v" --releases-as $(node -p "require('./package.json').version") --quiet --skip.changelog --skip.commit
    - git push --follow-tags
