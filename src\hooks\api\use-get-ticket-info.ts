import BACKEND from "@/constants/config/backend";
import EnumRequestHeader from "@/enum/EnumRequestHeader";
import ADMIN_ENDPOINTS from "@/models/api/admin-endpoints";
import GetTicketInfoAPIResult from "@/models/api/result/events/GetTicketInfo";

const useGetTicketInfo = () => {
    const fetchAsync = BACKEND.Gateway.useSubmit({ withHeaders: [EnumRequestHeader.AUTHORIZATION] });
    const requestAsync = async (params: { qrCodeToken: string }) => fetchAsync<GetTicketInfoAPIResult>(
        ADMIN_ENDPOINTS.GetTicketInfo(),
        {
            method: "GET",
            params
        }
    );
    return requestAsync;
}
export default useGetTicketInfo;
