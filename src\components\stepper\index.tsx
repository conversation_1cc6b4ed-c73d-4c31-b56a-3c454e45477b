
import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import Step from "@mui/material/Step";
import Button from "@mui/material/Button";
import MUIStepper from "@mui/material/Stepper";
import StepLabel from "@mui/material/StepLabel";
import Typography from "@mui/material/Typography";
import CardContent from "@mui/material/CardContent";
import StepContent from "@mui/material/StepContent";
import CardActions from "@mui/material/CardActions";

import type TStep from "./type"

type StepperProps = {
    activeStep: number;
    steps: TStep[];
    onBack: () => void;
    onNext: () => void;
}
const Stepper = ({ activeStep, steps, onBack, onNext }: StepperProps) => (
    <MUIStepper activeStep={activeStep} orientation="vertical" sx={{
        maxWidth: {
            md: "80vw",
            xs: "100%"
        }
    }}>
        {steps.map((step, index) => (
            <Step key={step.label}>
                <StepLabel
                    optional={
                        index === steps.length ? (
                            <Typography variant="caption">Last step</Typography>
                        ) : null
                    }
                >
                    {step.label}
                </StepLabel>
                <StepContent sx={{
                    width: "100%"
                }}>
                    <Typography>{step.description}</Typography>
                    <Box sx={{ mb: 2 }}>
                        <Card elevation={0}>
                            <CardContent sx={{
                                justifyContent: 'center',
                                justifyItems: 'center',
                                padding: {
                                    xs: 0
                                }
                            }}
                            >
                                {step.component}
                            </CardContent>
                            <CardActions>
                                <Button
                                    variant="contained"
                                    onClick={onNext}
                                    sx={{ mt: 1, mr: 1 }}
                                >
                                    {index === steps.length - 1 ? 'Continue to export' : 'Continue'}
                                </Button>
                                <Button
                                    disabled={index === 0}
                                    onClick={onBack}
                                    sx={{ mt: 1, mr: 1 }}
                                >
                                    Back
                                </Button>
                            </CardActions>
                        </Card>
                    </Box>
                </StepContent>
            </Step>
        ))}
    </MUIStepper>
);
export default Stepper;