'use client';

/* eslint-disable perfectionist/sort-imports */
import 'src/global.css';

import 'react-quill/dist/quill.snow.css';

// ----------------------------------------------------------------------

import ThemeProvider from 'src/theme';
import { primaryFont } from 'src/theme/typography';

import ProgressBar from 'src/components/progress-bar';
import { MotionLazy } from 'src/components/animate/motion-lazy';
import { SettingsDrawer, SettingsProvider } from 'src/components/settings';

import { APIConfig } from "@stoneleigh/api-lib";
import API from "@/utils/api";
import EnumRequestHeader from '@/enum/EnumRequestHeader';
import EnumLocale from '@/enum/EnumLocale';
import { AuthProvider } from 'src/auth/context/jwt';
import SnackbarProvider from 'src/components/snackbar/snackbar-provider';
import DayJSPlugin_Timezone from 'dayjs/plugin/timezone';
import DayJSPlugin_UTC from 'dayjs/plugin/utc';
import 'dayjs/locale/en';
import 'dayjs/locale/zh-hk';
import 'dayjs/locale/id';
import dayjs from "dayjs";
import { LocalizationProvider } from '@/locales';
import BACKEND from '@/constants/config/backend';

dayjs.extend(DayJSPlugin_UTC);
dayjs.extend(DayJSPlugin_Timezone);

// ----------------------------------------------------------------------

// export const metadata = {
//   title: 'Minimal UI Kit',
//   description:
//     'The starting point for your next project with Minimal UI Kit, built on the newest version of Material-UI ©, ready to be customized to your style',
//   keywords: 'react,material,kit,application,dashboard,admin,template',
//   themeColor: '#000000',
//   manifest: '/manifest.json',
//   viewport: { width: 'device-width', initialScale: 1, maximumScale: 1 },
//   icons: [
//     { rel: 'icon', url: '/favicon/favicon.ico' },
//     { rel: 'icon', type: 'image/png', sizes: '16x16', url: '/favicon/favicon-16x16.png' },
//     { rel: 'icon', type: 'image/png', sizes: '32x32', url: '/favicon/favicon-32x32.png' },
//     { rel: 'apple-touch-icon', sizes: '180x180', url: '/favicon/apple-touch-icon.png' },
//   ],
// };

type Props = {
    children: React.ReactNode;
};

export default function RootLayout({ children }: Props) {
    return (
        <html lang="en" className={primaryFont.className}>
            <body>
                <APIConfig.APIConfigProvider
                    queryClient={BACKEND.QueryClient}
                    gatewayConfigs={{
                        internal: {
                            headers: [
                                {
                                    key: EnumRequestHeader.AUTHORIZATION,
                                    value: () => API.GetUserJWT() || "",
                                },
                                {
                                    key: EnumRequestHeader.LANGUAGE,
                                    value: EnumLocale.English,
                                },
                            ]
                        }
                    }}
                >
                    <AuthProvider>
                        <LocalizationProvider>
                            <SettingsProvider
                                defaultSettings={{
                                    themeMode: 'light', // 'light' | 'dark'
                                    themeDirection: 'ltr', //  'rtl' | 'ltr'
                                    themeContrast: 'default', // 'default' | 'bold'
                                    themeLayout: 'vertical', // 'vertical' | 'horizontal' | 'mini'
                                    themeColorPresets: 'default', // 'default' | 'cyan' | 'purple' | 'blue' | 'orange' | 'red'
                                    themeStretch: false,
                                }}
                            >
                                <ThemeProvider>
                                    <MotionLazy>
                                        <SnackbarProvider>
                                            <SettingsDrawer />
                                            <ProgressBar />
                                            {children}
                                        </SnackbarProvider>
                                    </MotionLazy>
                                </ThemeProvider>
                            </SettingsProvider>
                        </LocalizationProvider>
                    </AuthProvider>
                </APIConfig.APIConfigProvider>
            </body>
        </html>
    );
}
