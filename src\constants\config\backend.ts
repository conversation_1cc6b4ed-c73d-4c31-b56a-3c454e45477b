import { HOST_API } from '@/config-global';
import { APIConfig, GenericAPI } from "@stoneleigh/api-lib";
import { QueryClient as ReactQuery_QueryClient } from '@tanstack/react-query';

class BackendConfig {
    public Gateway: GenericAPI;

    public QueryClient: ReactQuery_QueryClient;

    public INTERNAL_API_ENDPOINT?: string;

    constructor() {
        this.Gateway = new GenericAPI();
        APIConfig.Init({
            internal: this.Gateway
        });
        this.QueryClient = APIConfig.CreateQueryClient();
        APIConfig.SetQueryClient(this.QueryClient);
        this.INTERNAL_API_ENDPOINT = HOST_API;
    }
}
const BACKEND = new BackendConfig();
export default BACKEND;