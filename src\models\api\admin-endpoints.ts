import BACKEND from '@/constants/config/backend';

class ADMIN_ENDPOINTS {
    static version = 'v1';

    static baseUrl: string = `${BACKEND.INTERNAL_API_ENDPOINT}/${this.version}/admin`;

    public static readonly GetAllEventsByPage = (page: number) => `${this.baseUrl}/events/${page}`;

    public static readonly GetAllEventSessionById = (id: string, page: number) => `${this.baseUrl}/events/${id}/sessions/all/${page}`;

    public static readonly GetAllSessionBundleById = (id: string,sid:string, page: number) => `${this.baseUrl}/events/${id}/sessions/${sid}/${page}`;

    public static readonly UpdateEventById = (id: string) => `${this.baseUrl}/events/${id}`;

    public static readonly GetTicketInfo = () => `${this.baseUrl}/qrcode/scan`;

    public static readonly InsertTicketActions = (permission: string) => `${this.baseUrl}/ticket/${permission}`;

    public static readonly GetSalesReportDetail = () => `${this.baseUrl}/reports/sales/detail`;

    public static readonly GetSalesReportSummary = () => `${this.baseUrl}/reports/sales/summary`;

    public static readonly GetMemberReport = () => `${this.baseUrl}/reports/member`;

    public static readonly GetAdmissionReport = () => `${this.baseUrl}/reports/admission`;

    public static readonly GetSettlementReport = () => `${this.baseUrl}/reports/settlement`;

    public static readonly GetLanguageTranslation = (locale: string = 'en-US') => `${this.baseUrl}/i18n/translations/${locale}`;

    public static readonly UpdateEventLocalizationById = (locale: string = 'en-US') => `${this.baseUrl}/i18n/translations/${locale}`;

    public static readonly GetMembershipApplications = () => `${this.baseUrl}/membership/subscribe`;

    public static readonly ApproveMembershipApplication = () => `${this.baseUrl}/membership/subscribe/verify`;
}
export default ADMIN_ENDPOINTS;
