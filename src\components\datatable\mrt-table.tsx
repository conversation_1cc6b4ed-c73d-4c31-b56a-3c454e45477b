import React from 'react';
import {
    type MRT_RowData, // default shape of TData (Record<string, any>)
    MaterialReactTable,
    type MRT_ColumnDef,
    useMaterialReactTable,
    type MRT_TableOptions,
} from 'material-react-table';

import ContentCopyIcon from '@mui/icons-material/ContentCopy';

import { mergeNestedProps } from 'src/utils/merge-props';


export interface CustomMRTTableProps<TData extends MRT_RowData> extends MRT_TableOptions<TData> {
    columns: MRT_ColumnDef<TData>[];
    data: TData[];
}


const CustomMRTTable = <TData extends MRT_RowData>({ columns, data, ...restProps }: CustomMRTTableProps<TData>) => {
    const defaultConfig = {
        initialState: {
            density: 'compact',
            showColumnFilters: false, // override default initial state for just this table
        },
        muiTableHeadCellProps: { sx: { maxWidth: 350, whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis" } },
        muiTableBodyCellProps: { sx: { maxWidth: 350, whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis" } },
        muiEditRowDialogProps : { fullScreen: true },
        muiCopyButtonProps: {
            sx: { width: '100%' },
            startIcon: <ContentCopyIcon />,
        },
        enableRowPinning: false,
        enableStickyHeader: true,
        paginationDisplayMode: 'pages',
        enableDensityToggle: false,
        enableFullScreenToggle: false,
        enableSorting: false
    };
    const mergedProps = mergeNestedProps(defaultConfig, restProps) as MRT_RowData;

    const table = useMaterialReactTable({
        columns,
        data,
        ...mergedProps
    });
    return <MaterialReactTable table={table} />;
};
export default React.memo(CustomMRTTable) as typeof CustomMRTTable;