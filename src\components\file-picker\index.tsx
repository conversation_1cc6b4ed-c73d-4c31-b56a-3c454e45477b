import { useState, ChangeEvent, useCallback } from 'react';

import { styled } from '@mui/material/styles';
import LoadingButton from '@mui/lab/LoadingButton';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

const VisuallyHiddenInput = styled('input')({
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    opacity: 0
});

interface FilePickerProps {
    accept: string;
    onChange: (content?: string) => void;
}
const FilePicker = (props: FilePickerProps) => {
    const { onChange, accept } = props;
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const _onChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        const file = e?.target.files?.[0];
        if (!file) {
            onChange();
            return;
        }
        const reader = new FileReader();
        reader.onload = (evt: ProgressEvent<FileReader>) => {
            setIsLoading(false);
            onChange(evt.target?.result as string);
        }
        setIsLoading(true);
        reader.readAsDataURL(file);
    }, [onChange]);
    return (
        <LoadingButton loading={isLoading} loadingIndicator="Loading…" startIcon={<CloudUploadIcon />} variant="outlined">
            Upload file
            <VisuallyHiddenInput type="file" accept={accept} onChange={_onChange} />
        </LoadingButton>
    );
}
export default FilePicker;